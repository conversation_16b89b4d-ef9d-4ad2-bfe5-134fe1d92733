/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.projection.HousemaidList;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public class SubRuleResult<T> {

    private SubRule subRuleApplied;
    private List<T> subRuleResult;
    private List<Object> subRuleProjectedResult;
    private final Class projectionClass;
    private Boolean withUpdate;

    @Autowired
    private ProjectionFactory projectionFactory;

    private ProjectionFactory getProjectionFactory() {
        if (projectionFactory == null) {
            projectionFactory = Setup.getApplicationContext().getBean(ProjectionFactory.class);
        }
        return projectionFactory;
    }

    /**
     *
     * @param subRuleApplied
     * @param subRuleResult
     * @param withUpdate
     * @param projectionClass
     */
    public SubRuleResult(SubRule subRuleApplied, List<T> subRuleResult, Boolean withUpdate, Class projectionClass) {
        this.projectionClass = projectionClass;
        this.subRuleApplied = subRuleApplied;
        this.subRuleResult = subRuleResult;
        this.withUpdate = withUpdate;
    }

    /**
     *
     * @return
     */
    public Boolean getWithUpdate() {
        return withUpdate;
    }

    /**
     *
     * @param withUpdate
     */
    public void setWithUpdate(Boolean withUpdate) {
        this.withUpdate = withUpdate;
    }

    /**
     *
     * @return
     */
    public SubRule getSubRuleApplied() {
        return subRuleApplied;
    }

    /**
     *
     * @param subRuleApplied
     */
    public void setSubRuleApplied(SubRule subRuleApplied) {
        this.subRuleApplied = subRuleApplied;
    }

    /**
     *
     * @return
     */
    @JsonIgnore
    public List<T> getSubRuleResult() {
        return subRuleResult;
    }

    /**
     *
     * @param subRuleResult
     */
    public void setSubRuleResult(List<T> subRuleResult) {
        this.subRuleResult = subRuleResult;
    }

    public List<Object> getSubRuleProjectedResult() {
        if (subRuleProjectedResult != null) {
            return this.subRuleProjectedResult;
        }
        if (projectionClass == null) {
            return null;
        }
        if (subRuleResult == null) {
            return null;
        }
        try {
            subRuleProjectedResult = (List<Object>) subRuleResult.stream().map(h -> getProjectionFactory().createProjection(projectionClass,
                    h)).collect(Collectors.toList());
        } catch (Exception ex) {
            System.err.println(ex);
            return null;
        }

        return subRuleProjectedResult;
    }

    /**
     *
     * @return
     */
    public Integer getSubRuleResultSize() {
        if (getSubRuleResult() == null) {
            return 0;
        }
        return getSubRuleResult().size();
    }

    /**
     *
     * @return
     */
    public List<Long> getSubRuleResultIds() {
        if (getSubRuleResult() == null) {
            return null;
        }
        try {
            List<Long> result = new ArrayList<>();
            getSubRuleResult().forEach((item) -> {
                result.add(((BaseEntity) item).getId());
            });
            return result;
        } catch (Exception ex) {
        }
        return null;
    }
}
