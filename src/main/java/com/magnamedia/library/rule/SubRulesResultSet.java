/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.entity.BaseEntity;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public class SubRulesResultSet<T> {

    private List<SubRuleResult<T>> subRulesResults;
    private List<T> remaining;

    /**
     *
     * @param subRulesResults
     * @param remaining
     */
    public SubRulesResultSet(List<SubRuleResult<T>> subRulesResults, List<T> remaining) {
        this.subRulesResults = subRulesResults;
        this.remaining = remaining;
    }

    /**
     *
     * @return
     */
    public List<SubRuleResult<T>> getSubRulesResults() {
        return subRulesResults;
    }

    /**
     *
     * @param subRulesResults
     */
    public void setSubRulesResults(List<SubRuleResult<T>> subRulesResults) {
        this.subRulesResults = subRulesResults;
    }

    /**
     *
     * @return
     */
    public Integer getSubRulesResultsSize() {
        if (getSubRulesResults() == null) {
            return 0;
        }
        return getSubRulesResults().size();
    }

    /**
     *
     * @return
     */
    @JsonIgnore
    public List<T> getRemaining() {
        return remaining;
    }

    /**
     *
     * @param remaining
     */
    public void setRemaining(List<T> remaining) {
        this.remaining = remaining;
    }

    /**
     *
     * @return
     */
    public Integer getRemainingSize() {
        if (getRemaining() == null) {
            return 0;
        }
        return getRemaining().size();
    }

    /**
     *
     * @return
     */
    public List<Long> getRemainingIds() {
        if (getRemaining() == null) {
            return null;
        }
        try {
            List<Long> result = new ArrayList<>();
            getRemaining().forEach((item) -> {
                result.add(((BaseEntity) item).getId());
            });
            return result;
        } catch (Exception ex) {
        }
        return null;
    }   
}
