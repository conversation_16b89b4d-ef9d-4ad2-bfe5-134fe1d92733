/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public class GeneralRuleResult<T> {

    private BaseRule generalRuleApplied;
    private List<T> generalRuleRemainingResult;
    private List<Object> generalRuleRemainingProjectedResult;
    private Boolean subRulesApplied;
    private SubRulesResultSet<T> subRulesResultSet;
    private Boolean withUpdate;

    @Autowired
    private ProjectionFactory projectionFactory;

    private ProjectionFactory getProjectionFactory() {
        if (projectionFactory == null) {
            projectionFactory = Setup.getApplicationContext().getBean(ProjectionFactory.class);
        }
        return projectionFactory;
    }

    /**
     *
     * @param generalRuleApplied
     * @param generalRuleResult
     * @param subRulesApplied
     * @param subRulesResultSet
     * @param withUpdate
     */
    public GeneralRuleResult(BaseRule generalRuleApplied, List<T> generalRuleResult, Boolean subRulesApplied, SubRulesResultSet<T> subRulesResultSet, Boolean withUpdate) {
        this.generalRuleApplied = generalRuleApplied;
        this.generalRuleRemainingResult = generalRuleResult;
        this.subRulesApplied = subRulesApplied;
        this.subRulesResultSet = subRulesResultSet;
        this.withUpdate = withUpdate;
    }

    /**
     *
     * @return
     */
    @JsonIgnore
    public List<T> getGeneralRuleRemainingResult() {
        return generalRuleRemainingResult;
    }

    /**
     *
     * @param generalRuleRemainingResult
     */
    public void setGeneralRuleRemainingResult(List<T> generalRuleRemainingResult) {
        this.generalRuleRemainingResult = generalRuleRemainingResult;
    }

    public List<Object> getGeneralRuleRemainingProjectedResult() {
        if (generalRuleRemainingProjectedResult != null) {
            return this.generalRuleRemainingProjectedResult;
        }
        if (generalRuleApplied == null) {
            return null;
        }

        if (generalRuleApplied.getProjectionClass() == null) {
            return null;
        }

        if (generalRuleRemainingResult == null) {
            return null;
        }

        try {
            generalRuleRemainingProjectedResult = (List<Object>) generalRuleRemainingResult.stream().map(h -> getProjectionFactory().createProjection(generalRuleApplied.getProjectionClass(),
                    h)).collect(Collectors.toList());
        } catch (Exception ex) {
            System.err.println(ex);
            return null;
        }

        return generalRuleRemainingProjectedResult;
    }

    /**
     *
     * @return
     */
    public Integer getGeneralRuleRemainingResultSize() {
        if (getGeneralRuleRemainingResult() == null) {
            return 0;
        }
        return getGeneralRuleRemainingResult().size();
    }

    /**
     *
     * @return
     */
    public BaseRule getGeneralRuleApplied() {
        return generalRuleApplied;
    }

    /**
     *
     * @param generalRuleApplied
     */
    public void setGeneralRuleApplied(BaseRule generalRuleApplied) {
        this.generalRuleApplied = generalRuleApplied;
    }

    /**
     *
     * @return
     */
    public Boolean getSubRulesApplied() {
        return subRulesApplied;
    }

    /**
     *
     * @param subRulesApplied
     */
    public void setSubRulesApplied(Boolean subRulesApplied) {
        this.subRulesApplied = subRulesApplied;
    }

    /**
     *
     * @return
     */
    public SubRulesResultSet<T> getSubRulesResultSet() {
        return subRulesResultSet;
    }

    /**
     *
     * @param subRulesResultSet
     */
    public void setSubRulesResultSet(SubRulesResultSet<T> subRulesResultSet) {
        this.subRulesResultSet = subRulesResultSet;
    }

    /**
     *
     * @return
     */
    public Boolean getWithUpdate() {
        return withUpdate;
    }

    /**
     *
     * @param withUpdate
     */
    public void setWithUpdate(Boolean withUpdate) {
        this.withUpdate = withUpdate;
    }

    /**
     *
     * @return
     */
    public List<Long> getGeneralRuleRemainingResultIds() {
        if (getGeneralRuleRemainingResult() == null) {
            return null;
        }
        try {
            List<Long> result = new ArrayList<>();
            getGeneralRuleRemainingResult().forEach((item) -> {
                result.add(((BaseEntity) item).getId());
            });
            return result;
        } catch (Exception ex) {
        }
        return null;
    }

    public Integer getRecordsAppliedInSubRules() {
        if (!getSubRulesApplied()) {
            return 0;
        }
        if (getSubRulesResultSet() == null) {
            return 0;
        }
        Integer sum = 0;
        for (SubRuleResult<T> subRuleResult : getSubRulesResultSet().getSubRulesResults()) {
            if (subRuleResult.getSubRuleResultSize() != null) {
                sum += subRuleResult.getSubRuleResultSize();
            }
        }
        
        return sum;
    }

}
