/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.library.rule;

import com.magnamedia.core.Setup;
import com.magnamedia.repository.BaseRuleLogRepository;
import com.magnamedia.repository.SubRuleLogRepository;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.AspectJTypeFilter;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
@Component
public interface BaseRuleServiceDefinition<T> {

    /**
     * The package name where to look for BaseRules
     *
     * @return
     */
    public String getPackageName();

    /**
     * The annotation that should be present above all BaseRules
     *
     * @return
     */
    public Class getAnnotationClass();

    public default Logger.LoggingMode getLoggingMode() {
        return Logger.LoggingMode.ONLY_UPDATES;
    }
    
    public default Logger<T> getLogger(){
        return new Logger<T>();
    }

    /**
     *
     * @param codeFilter: filter by BaseRule's getCode() function, pass null for
     * no filter
     * @return a list of all BaseRules with specified filter
     */
    public default List<BaseRule<T>> getAllGeneralRules(String codeFilter) {
        List<BaseRule<T>> finalResult = new ArrayList<>();
        ClassPathScanningCandidateComponentProvider scanner
                = new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AnnotationTypeFilter(getAnnotationClass()));

        for (BeanDefinition bd : scanner.findCandidateComponents(getPackageName())) {
//            System.out.println(bd.getBeanClassName());
            try {
                BaseRule r = (BaseRule) Class.forName(bd.getBeanClassName()).newInstance();
                if (codeFilter != null && r.getCode() != null && !r.getCode().equals(codeFilter)) {
                    continue;
                }
                finalResult.add(r);
            } catch (Exception ex) {
                System.err.println(ex);
            }
        }

        return finalResult;
    }

    /**
     *
     * @return a list of all BaseRules
     */
    public default List<BaseRule<T>> getAllGeneralRules() {
        return getAllGeneralRules(null);
    }

    /**
     *
     * @param codeFilter filter by BaseRule's getCode() function, pass null for
     * no filter
     * @return a list of the result of applying all specified subrules
     */
    public default List<GeneralRuleResult<T>> applyAllGeneralRules(String codeFilter) {
        List<GeneralRuleResult<T>> finalResult = new ArrayList<>();
        for (BaseRule generalRule : getAllGeneralRules(codeFilter)) {
            List<T> generalRuleResult = generalRule.applyGeneralRule();
            finalResult.add(new GeneralRuleResult(generalRule, generalRuleResult, Boolean.FALSE, null, Boolean.FALSE));
        }
        
        getLogger().LogGeneralRuleResults(finalResult, getLoggingMode());
        
        return finalResult;
    }

    /**
     *
     * @return a list of the result of applying all subrules
     */
    public default List<GeneralRuleResult<T>> applyAllGeneralRules() {
        return applyAllGeneralRules(null);
    }

    /**
     *
     * @param codeFilter filter by BaseRule's getCode() function, pass null for
     * no filter
     * @param withUpdate whether to update the subRules or not
     * @param subRuleCodeFilter filter by @SubRule annotation's code
     * @return a list of the result of applying all specified subrules
     */
    public default List<GeneralRuleResult<T>> applyAllGeneralRulesAndSubRules(String codeFilter, Boolean withUpdate, String subRuleCodeFilter) {
        List<GeneralRuleResult<T>> finalResult = new ArrayList<>();
        for (BaseRule generalRule : getAllGeneralRules(codeFilter)) {
            SubRulesResultSet<T> subRulesResultSet = generalRule.applySubRules(withUpdate, subRuleCodeFilter);
            finalResult.add(new GeneralRuleResult(generalRule, subRulesResultSet.getRemaining(), Boolean.TRUE, subRulesResultSet, withUpdate));
        }
        
        getLogger().LogGeneralRuleResults(finalResult, getLoggingMode());
        return finalResult;
    }

    /**
     *
     * @return a list of the result of applying all subrules
     */
    public default List<GeneralRuleResult<T>> applyAllGeneralRulesAndSubRules() {
        return applyAllGeneralRulesAndSubRules(null, false, null);
    }

    /**
     *
     * @param withUpdate whether to update the subRules or not
     * @return a list of the result of applying all specified subrules
     */
    public default List<GeneralRuleResult<T>> applyAllGeneralRulesAndSubRules(Boolean withUpdate) {
        return applyAllGeneralRulesAndSubRules(null, withUpdate, null);
    }

    /**
     *
     * @param codeFilter filter by BaseRule's getCode() function, pass null for
     * no filter
     * @return a list of the result of applying all specified subrules
     */
    public default List<GeneralRuleResult<T>> applyAllGeneralRulesAndSubRules(String codeFilter) {
        return applyAllGeneralRulesAndSubRules(codeFilter, false, null);
    }

   
}
