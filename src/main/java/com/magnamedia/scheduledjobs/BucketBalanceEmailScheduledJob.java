package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Bucket;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.service.AccountBalanceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 13 2019
 *         Jirra ACC-1167
 */
public class BucketBalanceEmailScheduledJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(
            BucketBalanceEmailScheduledJob.class.getName());

    private BucketRepository bucketRepo;
    private AccountBalanceService accountBalanceService;

    public BucketBalanceEmailScheduledJob() {
        bucketRepo = Setup.getRepository(BucketRepository.class);
        accountBalanceService = Setup.getApplicationContext()
                .getBean(AccountBalanceService.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        this.sendEmailsJob();
    }

    public void sendEmailsJob() {
        Page<Bucket> page;
        Integer pageIndex = 0;
        accountBalanceService.calculateCorrectBalanceBasedTransaction();
        MessagingService messagingService = Setup.getApplicationContext()
                .getBean(MessagingService.class);
        do {
            page = bucketRepo.findBucketsWithEmail(PageRequest.of(pageIndex++, 100));
            List<Bucket> buckets = page.getContent();

            // ACC-1170
            buckets.forEach((bucket) -> {
                //bucket = accountBalanceService.setBucketBalanceBasedOnTransaction(bucket);
                if(bucket.getBalance() == 0) return;


                DecimalFormat df = new DecimalFormat("###,###");
                String subject = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_TITLE);
                subject = subject.replace("@bucket_name@", bucket.getName());
                subject = subject.replace("@amount@", df.format(bucket.getBalance()));

                HashMap<String, String> parameters = new HashMap<>();

                parameters.put("param_1", Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_OWNER));
                parameters.put("param_2", Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_ADDRESS));
                parameters.put("date", DateUtil.bucketEmailDateFormat.format(new Date()));

                messagingService.sendEmailToOfficeStaff("BUCKET_BALANCE_ASSISTANT_MAIL",
                        parameters, bucket.getHolderEmail(),
                        subject);
            });
        } while (page.hasNext());
    }
}