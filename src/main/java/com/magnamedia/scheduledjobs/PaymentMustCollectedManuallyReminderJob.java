package com.magnamedia.scheduledjobs;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.LocalDate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;


// ACC-6629
public class PaymentMustCollectedManuallyReminderJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(PaymentMustCollectedManuallyReminderJob.class.getName());
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    private PaymentRepository paymentRepository;
    private MessagingService messagingService;


    @Override
    public void run(Map<String, ?> map) {
        accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        paymentRepository = Setup.getRepository(PaymentRepository.class);
        messagingService = Setup.getApplicationContext()
                        .getBean(MessagingService.class);

        sendEmails();
    }

    private void sendEmails() {
        List<AccountingEntityProperty> l = new ArrayList<>();
        Long lastId = -1L;
        ObjectMapper objectMapper = new ObjectMapper();
        do {
            SelectQuery<AccountingEntityProperty> q = new SelectQuery<>(AccountingEntityProperty.class);
            q.filterBy("id", ">", lastId);
            q.filterBy("creationDate", "<", new LocalDate().toDate());
            q.filterBy("key", "=", Payment.PAYMENT_MUST_COLLECTED_MANUALLY);
            q.setLimit(200);

            l = q.execute();

            l.forEach(a -> {
                try {
                    logger.info("Property id: " + a.getId());
                    Map<String, String> m = objectMapper.readValue(a.getValue(), Map.class);
                    if (paymentRepository.existsPaymentMustCollectedManuallyForAcc6629(
                            (Contract) a.getOrigin(),
                            Double.parseDouble(m.get("amount")),
                            m.get("typeCode"))) {
                        accountingEntityPropertyRepository.deleteByKeyAndOrigin(Payment.PAYMENT_MUST_COLLECTED_MANUALLY, a.getOrigin());
                        logger.info("Exists matching payment delete property id: " + a.getId());
                        return;
                    }

                    logger.info("Sending EMail for property id: " + a.getId());
                    messagingService.sendEmailToOfficeStaff("dd_requested_and_client_paying_via_credit_card",
                                    m, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_REQUESTED_AND_CLIENT_PAYING_VIA_CREDIT_CARD_RECIPIENTS),
                                    "Payment Must be Collected Manually - " + ((Contract) a.getOrigin()).getClient().getId());
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            });

            if (!l.isEmpty()) {
                lastId = l.get(l.size() - 1).getId();
            }
        } while (!l.isEmpty());
    }
}