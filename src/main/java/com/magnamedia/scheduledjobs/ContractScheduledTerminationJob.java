package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.MaidCcContractCancellationFlowService;
import org.joda.time.LocalDate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

public class ContractScheduledTerminationJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(ContractScheduledTerminationJob.class.getName());
    private ContractRepository contractRepository;
    private MaidCcContractCancellationFlowService maidCcContractCancellationFlowService;
    private ContractService contractService;

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");
        contractRepository = Setup.getRepository(ContractRepository.class);
        maidCcContractCancellationFlowService = Setup.getApplicationContext().getBean(MaidCcContractCancellationFlowService.class);
        contractService = Setup.getApplicationContext().getBean(ContractService.class);

        checkContractScheduledTerminationDateBeforeOneDay();

        logger.log(Level.INFO, "Job finished");
    }

    public void checkContractScheduledTerminationDateBeforeOneDay() {
        List<Contract> contracts = contractRepository
                .findByScheduledDateOfTerminationAndStatusNotInAndContractProspectType_Code(
                        new LocalDate().plusDays(1).toDate(),
                        Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED),
                        "maids.cc_prospect");

        for (Contract contract: contracts) {
            try {
                logger.log(Level.INFO, "contract id : {0}", contract.getId());

                maidCcContractCancellationFlowService.startNewFlow(contract,
                        contractService.getMaidCcCancellationInfo(contract, contract.getDateOfTermination(), contract.getScheduledDateOfTermination()));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}