package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import com.magnamedia.entity.PurchaseItem;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.OrderCycle;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.repository.ItemRepository;
import com.magnamedia.repository.PurchaseItemRepository;
import com.magnamedia.repository.PurchasingToDoRepository;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalField;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Mohammad Nosairat (Feb 01, 2021)
 */
public class PurchasingTriggerJob implements MagnamediaJob {
    CategoryRepository categoryRepository;
    ItemRepository itemRepository;
    PurchaseItemRepository purchaseItemRepository;
    Logger logger = Logger.getLogger(PurchasingTriggerJob.class.getName());

    @Override
    public void run(Map<String, ?> map) {
        try {
            categoryRepository = Setup.getRepository(CategoryRepository.class);
            itemRepository = Setup.getRepository(ItemRepository.class);
            purchaseItemRepository = Setup.getRepository(PurchaseItemRepository.class);
            logger.info("begin job PurchasingTriggerJob");
            runJob();
            logger.info("end job PurchasingTriggerJob");
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private void runJob() {
        int daysNumBeforeStartMonthly = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.NBR_DAYS_BEFORE_START_ORDER_CYCLE_MONTHLY));
        logger.info("daysNumBeforeStartMonthly " + daysNumBeforeStartMonthly);

        if (isToStartMonthlyCycle(daysNumBeforeStartMonthly)) {
            logger.info("StartMonthlyCycle");

            PicklistItem monthlyItem = Setup.getItem(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE, OrderCycle.MONTHLY.getName());
            List<Category> categories = categoryRepository.findByOrderCycle(monthlyItem);
            logger.info("categories size " + categories.size());
            createPurchasingRequest(categories, OrderCycle.MONTHLY, daysNumBeforeStartMonthly);
            logger.info("end StartMonthlyCycle");
        }

        int daysNumBeforeStartWeekly = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.NBR_DAYS_BEFORE_START_ORDER_CYCLE_WEEKLY));
        logger.info("daysNumBeforeStartWeekly " + daysNumBeforeStartWeekly);

        if (isToStartWeeklyCycle(daysNumBeforeStartWeekly)) {
            logger.info("StartWeeklyCycle");
            PicklistItem weeklyItem = Setup.getItem(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE, OrderCycle.WEEKLY.getName());
            List<Category> categories = categoryRepository.findByOrderCycle(weeklyItem);
            logger.info("categories size " + categories.size());
            createPurchasingRequest(categories, OrderCycle.WEEKLY, daysNumBeforeStartWeekly);
            logger.info("end StartMonthlyCycle");
        }
    }

    private void createPurchasingRequest(List<Category> categories, OrderCycle orderCycle, int daysNumBeforeStart) {
        if (categories.size() == 0) return;

        logger.info("createPurchasingRequest ");

        for (Category category : categories) {
            logger.info("createPurchasingRequest for category  " + category.getId());
            List<PurchaseItem> purchaseItems = new ArrayList<>();

            List<Item> items = itemRepository.findByCategory(category);
            for (Item item : items) {
                try {
                    BigDecimal quantity = item.getQuantityToOrder();
                    logger.info("quantity for item  " + item.getId() + " quantity " + quantity);

                    if (quantity.compareTo(BigDecimal.ZERO) > 0) {
                        logger.info("create PurchaseItem for item  " + item.getId() + "and  quantity " + quantity);
                        purchaseItems.add(new PurchaseItem(item, quantity));
                        logger.info("end creating PurchaseItem for item  " + item.getId() + "and  quantity " + quantity);
                    }
                } catch(Exception ex) {
                    logger.log(Level.SEVERE, "exception in createPurchasingRequest", ex);
                }
            }

            if (purchaseItems.size() > 0) {
                logger.info("create purhcasTodo   " + purchaseItems.size());
                PurchasingToDo purchasingToDo = new PurchasingToDo();
                purchasingToDo.setCategory(category);
                purchasingToDo.setOrderCycleName(getOrderCycleName(orderCycle, daysNumBeforeStart));
                Setup.getRepository(PurchasingToDoRepository.class).save(purchasingToDo);

                for (PurchaseItem purchaseItem : purchaseItems) {
                    purchaseItem.setPurchasingToDo(purchasingToDo);
                    purchaseItemRepository.save(purchaseItem);
                }
                logger.info("end create purhcasTodo   " + purchaseItems.size());

            }

        }
    }

    private String getOrderCycleName(OrderCycle orderCycle, int daysNumBeforeStart) {
        if (orderCycle.equals(OrderCycle.MONTHLY)) {
            return Instant.now().atZone(ZoneId.systemDefault()).plus(daysNumBeforeStart, ChronoUnit.DAYS)
                    .getMonth().getDisplayName(TextStyle.FULL, Locale.US);
        }
        if (orderCycle.equals(OrderCycle.WEEKLY)) {
            LocalDate now = Instant.now().atZone(ZoneId.systemDefault()).plus(daysNumBeforeStart, ChronoUnit.DAYS).toLocalDate();
            TemporalField fieldUS = WeekFields.of(Locale.US).dayOfWeek();
            LocalDate startOfWeekDate = now.with(fieldUS, 7);
            return startOfWeekDate.toString() + " " + startOfWeekDate.plus(6, ChronoUnit.DAYS).toString(); // 2015-02-08 (Sunday)
        }
        return null;
    }

    private boolean isToStartWeeklyCycle(int daysNumBeforeStart) {
        DayOfWeek dayOfWeek = Instant.now().plus(daysNumBeforeStart, ChronoUnit.DAYS)
                .atZone(ZoneId.systemDefault()).getDayOfWeek();
        return dayOfWeek.equals(DayOfWeek.SATURDAY);
    }

    private boolean isToStartMonthlyCycle(int daysNumBeforeStart) {
        int dayOfMonth = Instant.now().plus(daysNumBeforeStart, ChronoUnit.DAYS)
                .atZone(ZoneId.systemDefault()).getDayOfMonth();
        return dayOfMonth == 1;
    }
}
