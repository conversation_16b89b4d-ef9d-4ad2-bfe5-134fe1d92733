package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.mail.EmailRecipient;
import com.magnamedia.core.mail.MailService;
import com.magnamedia.core.mail.TemplateEmail;
import com.magnamedia.core.master.repository.JobDefinitionRepository;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.ReportMail;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.report.TemplatedReport;
import com.magnamedia.repository.ClientRefundTodoRepository;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.thymeleaf.context.Context;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
//ACC-4715
public class ClientPayingViaCreditCardEligibleRefundEmailJob implements TemplatedReport<ClientRefundToDo>, MagnamediaJob {

    private MailService mailService;
    private ReportMail reportMail;
    private ClientRefundTodoRepository repository;
    private JobDefinitionRepository jobDefinitionRepository;

    private static final Logger LOG = Logger.getLogger(ClientPayingViaCreditCardEligibleRefundEmailJob.class.getName());

    public ClientPayingViaCreditCardEligibleRefundEmailJob() {
        mailService = Setup.getMailService();
        reportMail = Setup.getApplicationContext().getBean(ReportMail.class);
        repository = Setup.getRepository(ClientRefundTodoRepository.class);
        jobDefinitionRepository = Setup.getRepository(JobDefinitionRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Start Run");
        SendEmail();
    }

    public String SendEmail() {
        StringBuilder tableBuilder = new StringBuilder("");

        try {
            tableBuilder.append(reportMail.toHtmlTableNoCount(getTable(), getHeaders(), getTitle(), 400));
            tableBuilder.append("<br/><hr/>");
            Map<String, Object> params = new HashMap<>();
            params.put("title", getTitle());
            params.put("tableData", tableBuilder.toString());
            Context context = new Context();
            context.setVariables(params);


            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_ELIGIBLE_REFUND_EMAIL));
            List<EmailRecipient> recipientsCC = EmailHelper.getMailRecipients(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_ELIGIBLE_REFUND_EMAIL_CC));

            List<ClientRefundToDo> refundToDos = getData();
            if (recipients.size() > 0 && !refundToDos.isEmpty()) {
                logger.log(Level.INFO, "Sending emails");
                mailService.sendEmail(recipients, recipientsCC,
                        new TemplateEmail("Clients who are eligible to get credit card refunds",
                                "EligibleRefundEmail", params), null);

                refundToDos.forEach(todo -> {
                    logger.log(Level.INFO, "todo id: {0}", todo.getId());
                    todo.setStopped(true);
                    repository.save(todo);
                });
            }

        } catch (Throwable e) {
            tableBuilder.append("<p>").append(ExceptionUtils.getStackTrace(e)).append("</p>");
        }
        return "";
    }

    @Override
    public List<ClientRefundToDo> getData() {

        return repository.findByConditionalRefundAndRequiredPayment();
    }

    @Override
    public String[] toStringArray(ClientRefundToDo obj) {
        LOG.log(Level.INFO, "obj id : {0}", obj);

        return new String[]{ obj.getClient().getId().toString(), obj.getClient().getName(),
                obj.getContract().getId().toString(),
                new DateTime(obj.getCreationDate()).toString("yyyy-MM-dd HH:mm:ss"),
                String.valueOf(obj.getAmount().intValue()), obj.getFlowTriggered() != null ? obj.getFlowTriggered() : ""};
    }

    @Override
    public String getTitle() {
        return "The following clients are eligible to get refunds :<br/>";
    }

    @Override
    public String[] getHeaders() {
        return new String[]{ "Client ID", "Client Name", "Contract ID", "Refund date", "Refund Amount", "Triggered by"};
    }

    @Override
    public void refreshData() {
    }
}
