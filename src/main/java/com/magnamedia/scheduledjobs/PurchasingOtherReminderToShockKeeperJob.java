package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.OrderCycle;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.repository.ItemRepository;
import com.magnamedia.service.PurchasingReminderService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <PERSON> (Feb 01, 2021)
 */
public class PurchasingOtherReminderToShockKeeperJob implements MagnamediaJob {
    ParameterRepository parameterRepository;
    CategoryRepository categoryRepository;
    int numberOnReminders;
    PurchasingReminderService purchasingReminderService;
    ItemRepository itemRepository;

    @Override
    public void run(Map<String, ?> map) {
        try {
            purchasingReminderService = Setup.getApplicationContext().getBean(PurchasingReminderService.class);
            categoryRepository = Setup.getRepository(CategoryRepository.class);
            parameterRepository = Setup.getRepository(ParameterRepository.class);
            itemRepository = Setup.getRepository(ItemRepository.class);

            numberOnReminders = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.REMINDERS_TO_STOCK_KEEPER));

            runJob();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private void runJob() {
        processMonthlyCycleReminders();
        processWeeklyCycleReminders();
    }

    private void processWeeklyCycleReminders() {
        Parameter weeklyReminderIndexParameter = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), AccountingModule.STOCK_KEEPER_WEEKLY_REMINDER_INDEX);
        int weeklyReminderIndex = Integer.parseInt(weeklyReminderIndexParameter.getValue());
        List<Category> weeklyCategories = getActiveWeeklyCategories();
        if (weeklyCategories.size() == 0) {
            return;
        }

        if (weeklyReminderIndex > numberOnReminders) {
            return;
        }

        if (weeklyReminderIndex == numberOnReminders) {// dis active categories
            List<Category> notUpdatedCategories = getUpdated20PerCent(weeklyCategories);
            logger.info("notUpdatedCategories size  : " + notUpdatedCategories.size());
            if (notUpdatedCategories.size() > 0)
                purchasingReminderService.sendMailToStockKeeperManager(notUpdatedCategories);
            for (Category c : weeklyCategories) {
                c.setInActiveCycle(false);
                categoryRepository.save(c);
            }
            incrementParameter(weeklyReminderIndexParameter, weeklyReminderIndex);
            return;
        }

        List<Category> notUpdatedCategories = getUpdated20PerCent(weeklyCategories);

        if (notUpdatedCategories.size() > 0)
            purchasingReminderService.sendMailToStockKeeper(notUpdatedCategories);


        incrementParameter(weeklyReminderIndexParameter, weeklyReminderIndex);

    }

    private List<Category> getUpdated20PerCent(List<Category> inputList) {
        List<Category> outputList = new ArrayList<>();
        for (Category category : inputList) {
            List<Item> items = itemRepository.findByCategory(category);
            long totalItemCount = items.size();

            if (totalItemCount == 0) continue;
            long updatedCount = items.stream().filter(t -> t.getQuantity().compareTo(t.getQuantityOnCycleBegin()) == -1).count();
            if ((updatedCount / totalItemCount) <= 0.2) {
                outputList.add(category);
            }
        }
        return outputList;

    }


    private void processMonthlyCycleReminders() {
        Parameter monthlyReminderIndexParameter = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), AccountingModule.STOCK_KEEPER_MONTHLY_REMINDER_INDEX);
        int monthlyReminderIndex = Integer.parseInt(monthlyReminderIndexParameter.getValue());
        List<Category> monthlyCategories = getActiveMonthlyCategories();

        if (monthlyCategories.size() == 0) {
            return;
        }

        if (monthlyReminderIndex > numberOnReminders) {
            return;
        }

        if (monthlyReminderIndex == numberOnReminders) {// dis active categories
            List<Category> notUpdatedCategories = getUpdated20PerCent(monthlyCategories);
            if (notUpdatedCategories.size() > 0)
                purchasingReminderService.sendMailToStockKeeperManager(notUpdatedCategories);
            for (Category c : monthlyCategories) {
                c.setInActiveCycle(false);
                categoryRepository.save(c);
            }
            incrementParameter(monthlyReminderIndexParameter, monthlyReminderIndex);
            return;
        }

        List<Category> notUpdatedCategories = getUpdated20PerCent(monthlyCategories);

        if (notUpdatedCategories.size() > 0)
            purchasingReminderService.sendMailToStockKeeper(notUpdatedCategories);


        incrementParameter(monthlyReminderIndexParameter, monthlyReminderIndex);

    }

    private void incrementParameter(Parameter monthlyReminderIndexParameter, int monthlyReminderIndex) {
        monthlyReminderIndexParameter.setValue((monthlyReminderIndex + 1) + "");
        parameterRepository.save(monthlyReminderIndexParameter);
    }

    private List<Category> getActiveMonthlyCategories() {
        PicklistItem monthlyItem = Setup.getItem(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE, OrderCycle.MONTHLY.getName());
        return categoryRepository.findByOrderCycleAndInActiveCycleTrue(monthlyItem);
    }

    private List<Category> getActiveWeeklyCategories() {
        PicklistItem weeklyItem = Setup.getItem(AccountingModule.PICKLIST_CATEGORY_ORDER_CYCLE, OrderCycle.WEEKLY.getName());
        return categoryRepository.findByOrderCycleAndInActiveCycleTrue(weeklyItem);
    }

}
