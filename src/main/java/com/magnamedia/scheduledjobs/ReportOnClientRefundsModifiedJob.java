package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


public class ReportOnClientRefundsModifiedJob implements MagnamediaJob {
    private static final Logger logger =
            Logger.getLogger(ReportOnClientRefundsModifiedJob.class.getName());
    @Override
    public void run(Map<String, ?> map) {
        logger.log(Level.INFO, "Job started");

        HistorySelectQuery<ClientRefundToDo> historyQuery = new HistorySelectQuery<>(ClientRefundToDo.class);
        historyQuery.filterBy("lastModificationDate", ">=", new DateTime().minusDays(1).toDate());
        historyQuery.filterBy(new SelectFilter("purpose","has_changed", "")
                .or("amount", "has_changed", ""));

        List<ClientRefundToDo> todayToDos = historyQuery.execute();

        logger.log(Level.INFO, "todayToDos size: {0}", todayToDos.size());


        for (ClientRefundToDo clientRefundToDoModified: todayToDos) {
            try {
                HistorySelectQuery<ClientRefundToDo> hq = new HistorySelectQuery<>(ClientRefundToDo.class);
                hq.filterBy("id", "=", clientRefundToDoModified.getId());
                hq.filterBy(new SelectFilter("purpose","has_changed", "")
                        .or("amount", "has_changed", ""));

                List<ClientRefundToDo> revisions = hq.execute();

                if(revisions.size() <= 1) {
                    logger.log(Level.INFO, "only one revision");
                    continue;
                }

                ClientRefundToDo clientRefundToDo = Setup.getRepository(ClientRefundTodoRepository.class)
                        .findOne(clientRefundToDoModified.getId());

                logger.log(Level.INFO, "clientRefundToDo id: {0}", clientRefundToDo.getId());
                if (clientRefundToDo.getRequesterUser() == null) {
                    logger.log(Level.INFO, "requester user is null");
                    continue;
                }

                HistorySelectQuery<ClientRefundToDo> old = new HistorySelectQuery<>(ClientRefundToDo.class);
                old.filterBy("id", "=", clientRefundToDo.getId());
                old.filterBy("taskName", "=", ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString());
                old.filterBy("lastModificationDate", "<", revisions.get(revisions.size()-1).getLastModificationDate());
                old.sortBy("lastModificationDate", false, true);
                old.setLimit(1);
                List<ClientRefundToDo> oldClientRefundToDos = old.execute();

                if (oldClientRefundToDos.isEmpty()){
                    logger.log(Level.INFO, "Couldn't find the old request");
                    continue;
                }

                sendRequesterReport(clientRefundToDo, oldClientRefundToDos.get(0));

            }catch (Exception e) {
                e.printStackTrace();
            }

        }
        logger.log(Level.INFO, "Job ended");

    }

    private void sendRequesterReport(
            ClientRefundToDo refundToDos, ClientRefundToDo oldClientRefundToDo) {

        logger.log(Level.INFO, "start send an email to requester");

        Map<String, String> parameters =  new HashMap<String, String>() {{
            put("client_name", refundToDos.getClient() != null ? refundToDos.getClient().getName() : "");
            put("contract_id", refundToDos.getContract().getId().toString());
            put("old_refund_amount", String.valueOf(oldClientRefundToDo.getAmount().intValue()));
            put("new_refund_amount", String.valueOf(refundToDos.getAmount().intValue()));
            put("purpose", refundToDos.getPurpose().getName());
            put("manager_notes", refundToDos.getManagerNotes() != null ? "<br/>Manager notes: " + refundToDos.getManagerNotes() : "");
        }};
        logger.log(Level.INFO, "parameters" + parameters.entrySet());

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("client_refund_has_been_amended",
                        parameters, refundToDos.getRequesterUser().getEmail(),
                        "Client refund has been amended (" + (refundToDos.getClient() != null ? refundToDos.getClient().getName() : "") + ")");
    }
}