package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.MessagingService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 7-5-2020
 *         Jirra ACC-1777
 */

public class BouncingPaymentChequesJob implements MagnamediaJob {

    Logger logger = Logger.getLogger(BouncingPaymentChequesJob.class.getName());

    private ContractRepository contractRepository;

    public BouncingPaymentChequesJob() {
        this.contractRepository = Setup.getRepository(ContractRepository.class);
    }

    @Override
    public void run(Map<String, ?> map) {
        sendChequeMessages();
    }

    private void sendChequeMessages() {
        LocalDate today = new LocalDate();

        String days = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ACCOUNTANT_EMAILS_CHEQUE_DAYS);

        logger.log(Level.SEVERE, "BouncingPaymentChequesJob days:" + days);

        SelectQuery<Payment> query = new SelectQuery(Payment.class);
        query.filterBy("status", "=", PaymentStatus.BOUNCED);
        query.filterBy("methodOfPayment", "=", PaymentMethod.CHEQUE);
        query.filterBy("replaced", "=", false);
        query.filterBy("contract.status", "in", Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED));
        query.filterBy("dateOfBouncing", "=", today.minusDays(Integer.parseInt(days)).toDate());

        query.leftJoinFetch("contract", "contract");
        query.nestedLeftJoinFetch("contract", "client");
        query.setAllowNestedJoinsWithPageable(true);

        String subject = "Report bounced cheque (@cheque_number@) to police";

        Page<Payment> page;
        Integer pageIndex = 0;
        do {
            page = query.execute(PageRequest.of(pageIndex++, 100));
            List<Payment> payments = page.getContent();

            for (Payment payment : payments) {
                try {
                    runBouncingPaymentChequesJob(payment, subject, days);
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "BouncingPaymentChequesJob exception while processing payment#" + payment.getId());
                    logger.log(Level.SEVERE, "BouncingPaymentChequesJob exception: " + ExceptionUtils.getStackTrace(e));
                }
            }
        } while (page.hasNext());
    }

    private void runBouncingPaymentChequesJob(
            Payment payment, String subject, String days) {

        logger.log(Level.SEVERE, "BouncingPaymentChequesJob payment:" + payment.getId());

        Contract contract = payment.getContract();
        if (contractRepository.existsByStatusAndClient(ContractStatus.ACTIVE, contract.getClient())) {

            logger.log(Level.SEVERE, "BouncingPaymentChequesJob there is active contract");

            return;
        }

        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("number_of_days", days);
        parameters.put("cheque_number", payment.getChequeNumber());
        parameters.put("bank_name", payment.getBankName() != null ? payment.getBankName().getName() : "");
        parameters.put("cheque_name", payment.getChequeName());

        String emails = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ACCOUNTANT_EMAILS);
        logger.log(Level.SEVERE, "BouncingPaymentChequesJob emails: " + emails);

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("report_bounced_cheque_to_police",
                        parameters, emails,
                        subject.replaceAll("@cheque_number@", payment.getChequeNumber()));
    }
}
