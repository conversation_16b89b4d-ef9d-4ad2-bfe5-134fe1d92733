package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Bucket;
import com.magnamedia.repository.BucketRepository;
import com.magnamedia.service.AccountBalanceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Map;
import java.util.logging.Logger;

/*

 * <AUTHOR>
 * @created 09/03/2024 - 4:11 PM
 * ACC-5512

 */
public class CalculateCorrectBalanceBasedTransactionJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(CalculateCorrectBalanceBasedTransactionJob.class.getName());

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info( "started");

        Setup.getApplicationContext()
                .getBean(AccountBalanceService.class)
                .calculateCorrectBalanceBasedTransaction();

        logger.info( "finished");
    }
}
