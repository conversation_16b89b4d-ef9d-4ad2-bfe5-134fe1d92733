/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.scheduledjobs;

import com.magnamedia.core.schedule.MagnamediaJob;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DataIntegrityReportJob implements MagnamediaJob {
    
    @Override
    public void run(Map<String, ?> map)
    {
        DataCorrectionandIntegrityScheduledJob originalJob = new DataCorrectionandIntegrityScheduledJob();
        originalJob.InvalidDataEmail();
    }
}
