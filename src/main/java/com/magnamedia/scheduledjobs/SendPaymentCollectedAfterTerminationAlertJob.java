package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.dto.PaymentCollectedAfterTerminationCsv;
import com.magnamedia.extra.JobUtils;
import com.magnamedia.extra.PaymentCollectedAfterTerminationCsvProjection;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.MessagingService;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created on Apr 12, 2025
 * ACC-8802
 */
public class SendPaymentCollectedAfterTerminationAlertJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(SendPaymentCollectedAfterTerminationAlertJob.class.getName());

    private MessagingService messagingService;
    private PaymentRepository paymentRepository;
    private AccountingEntityProperty accountingEntityProperty;
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;

    private static final String ACCOUNTING_PROPERTY_PAYMENT_COLLECTED_AFTER_TERMINATION_JOB_TIME = "accounting_property_payment_collected_after_termination_job_time";

    @Override
    public void run(Map<String, ?> parameters) {
        paymentRepository = Setup.getRepository(PaymentRepository.class);
        messagingService = Setup.getApplicationContext().getBean(MessagingService.class);
        accountingEntityProperty = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByKeyAndIsDeletedFalse(ACCOUNTING_PROPERTY_PAYMENT_COLLECTED_AFTER_TERMINATION_JOB_TIME);
        contractPaymentConfirmationToDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);

        sendEmail();
    }

    public void sendEmail() {
        Date lastRunDate = JobUtils.getJobLastRunDate(accountingEntityProperty, new Date());
        JobUtils.setJobLastRunDate(accountingEntityProperty, ACCOUNTING_PROPERTY_PAYMENT_COLLECTED_AFTER_TERMINATION_JOB_TIME, new LocalDateTime());
        List<PaymentCollectedAfterTerminationCsv> todos = processTodos(paymentRepository.findAllReceivedPaymentPerToDo(lastRunDate));

        if (todos.isEmpty()) return;

        String[] headers = {"Contract ID", "Client ID", "Client name", "Payment ID", "Payment date", "Profile link"};
        String[] names = {"contractID", "clientID", "clientName", "paymentID", "paymentDate", "profileLink"};

        String subject = "Payment Collected After Termination (" + new LocalDate().minusDays(1).toString("yyyy-MM-dd") + ")";
        try {
            File file = CsvHelper.generateCsv(todos, PaymentCollectedAfterTerminationCsvProjection.class,
                    headers, names, subject, ".csv");

            FileInputStream inputStream = new FileInputStream(file);

            messagingService.sendEmailToOfficeStaffWithAttachments("payment_collected_after_termination",
                    new HashMap<>(), Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_PAYMENT_COLLECTED_AFTER_TERMINATION_RECEIPTS),
                    Collections.singletonList(Storage.storeTemporary(file.getName(),
                            inputStream, null, false)),
                    subject);

            StreamsUtil.closeStream(inputStream);
        } catch (Exception e) {
            logger.severe("error : " + e.getMessage());
            e.printStackTrace();
        }
    }

    private List<PaymentCollectedAfterTerminationCsv> processTodos(List<Map> payments) {
        Map<Long, List<Map>> groupedByTodoId = payments.stream()
                .collect(Collectors.groupingBy(result -> (Long) result.get("todoId")));
        String paymentsTabLink = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL)
                + "#!/client/payments/";

        List<PaymentCollectedAfterTerminationCsv> resultList = new ArrayList<>();

        for (Map.Entry<Long, List<Map>> entry : groupedByTodoId.entrySet()) {
            Long todoId = entry.getKey();
            logger.info("todo id : " + todoId);
            List<Map> paymentsPerTodo = entry.getValue();

            StringBuilder paymentIds = new StringBuilder();
            Date firstPaymentDate = null;
            Long contractId = null;
            Long clientId = null;
            String clientName = null;

            for (Map payment : paymentsPerTodo) {
                Long paymentId = (Long) payment.get("paymentId");
                Date paymentDate = (Date) payment.get("dateOfPayment");

                if (paymentIds.length() > 0) {
                    paymentIds.append(",");
                }
                paymentIds.append(paymentId);

                if (firstPaymentDate == null || paymentDate.before(firstPaymentDate)) {
                    firstPaymentDate = paymentDate;
                }

                if (contractId == null) {
                    contractId = (Long) payment.get("contractId");
                }
                if (clientId == null) {
                    clientId = (Long) payment.get("clientId");
                }
                if (clientName == null) {
                    clientName = (String) payment.get("clientName");
                }
            }

            PaymentCollectedAfterTerminationCsv csvRecord = new PaymentCollectedAfterTerminationCsv();
            csvRecord.setId(todoId);
            csvRecord.setContractID(contractId);
            csvRecord.setClientID(clientId);
            csvRecord.setClientName(clientName);
            csvRecord.setPaymentID(paymentIds.length() == 0 ? "N\\A" : paymentIds.toString());
            csvRecord.setPaymentDate(firstPaymentDate == null ? "N\\A" : new LocalDate(firstPaymentDate).toString("yyyy-MM-dd"));
            csvRecord.setProfileLink(paymentsTabLink + clientId);

            resultList.add(csvRecord);
        }

        return resultList;
    }
}