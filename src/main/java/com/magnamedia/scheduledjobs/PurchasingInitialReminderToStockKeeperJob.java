package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Parameter;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.repository.ItemRepository;
import com.magnamedia.service.PurchasingReminderService;

import java.time.DayOfWeek;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

// one time a day

/**
 * <PERSON> (Feb 02, 2021)
 */
public class PurchasingInitialReminderToStockKeeperJob implements MagnamediaJob {
    ParameterRepository parameterRepository;
    CategoryRepository categoryRepository;
    PurchasingReminderService purchasingReminderService;

    @Override
    public void run(Map<String, ?> map) {
        try {
            purchasingReminderService = Setup.getApplicationContext().getBean(PurchasingReminderService.class);
            categoryRepository = Setup.getRepository(CategoryRepository.class);
            parameterRepository = Setup.getRepository(ParameterRepository.class);

            runJob();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private void runJob() {
        int daysNumBeforeStartMonthly = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_MONTHLY));
        logger.info("daysNumBeforeStartMonthly : " + daysNumBeforeStartMonthly);
        logger.info("isToStartMonthlyCycleٌReminders : " + isToStartMonthlyCycleٌReminders(daysNumBeforeStartMonthly));
        if (isToStartMonthlyCycleٌReminders(daysNumBeforeStartMonthly)) {
            List<Category> categories = purchasingReminderService.getMonthlyCategories();
            logger.info("categories size : " + categories.size());
            if (categories.size() > 0) {
                categories = saveCurrentQuantities(categories);
                purchasingReminderService.sendMailToStockKeeper(categories);
                setMonthlyReminderNumberToFirstReminder();
            }
        }

        int daysNumBeforeStartWeekly = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_WEEKLY));

        if (isToStartWeeklyCycleReminders(daysNumBeforeStartWeekly)) {
            List<Category> categories = purchasingReminderService.getWeeklyCategories();
            if (categories.size() > 0) {
                categories = saveCurrentQuantities(categories);
                purchasingReminderService.sendMailToStockKeeper(categories);
                setWeeklyReminderNumberToFirstReminder();
            }
        }
    }


    private void setWeeklyReminderNumberToFirstReminder() {
        Parameter weeklyReminderIndexParameter = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), AccountingModule.STOCK_KEEPER_WEEKLY_REMINDER_INDEX);
        weeklyReminderIndexParameter.setValue("1");
        parameterRepository.save(weeklyReminderIndexParameter);
    }

    private void setMonthlyReminderNumberToFirstReminder() {
        Parameter monthlyReminderIndexParameter = parameterRepository.findByModuleAndCode(Setup.getCurrentModule(), AccountingModule.STOCK_KEEPER_MONTHLY_REMINDER_INDEX);
        monthlyReminderIndexParameter.setValue("1");
        parameterRepository.save(monthlyReminderIndexParameter);
    }


    private List<Category> saveCurrentQuantities(List<Category> categories) {
        List<Category> categoriesHavingItems = new ArrayList<>();
        for (Category category : categories) {
            List<Item> items = Setup.getRepository(ItemRepository.class).findByCategory(category);
            if (items.size() == 0)
                continue;
            categoriesHavingItems.add(category);
            category.setInActiveCycle(true);
            categoryRepository.save(category);

            for (Item item : items) {
                item.setQuantityOnCycleBegin(item.getQuantity());
                Setup.getRepository(ItemRepository.class).save(item);
            }
        }
        return categoriesHavingItems;
    }


    private boolean isToStartWeeklyCycleReminders(int daysNumBeforeStart) {
        DayOfWeek dayOfWeek = Instant.now().plus(daysNumBeforeStart, ChronoUnit.DAYS)
                .atZone(ZoneId.systemDefault()).getDayOfWeek();
        return dayOfWeek.equals(DayOfWeek.SATURDAY);
    }

    private boolean isToStartMonthlyCycleٌReminders(int daysNumBeforeStart) {
        ZonedDateTime zonedDateTime = Instant.now().plus(daysNumBeforeStart + 1, ChronoUnit.DAYS)
                .atZone(ZoneId.systemDefault());
        if (zonedDateTime.getDayOfMonth() == 1) {
            if (Instant.now().atZone(ZoneId.systemDefault())
                    .getDayOfWeek().equals(DayOfWeek.THURSDAY))
                return true;
        }

        zonedDateTime = Instant.now().plus(daysNumBeforeStart, ChronoUnit.DAYS)
                .atZone(ZoneId.systemDefault());
        return zonedDateTime.getDayOfMonth() == 1;
    }
}
