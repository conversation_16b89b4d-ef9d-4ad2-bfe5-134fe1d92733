/*
package com.magnamedia.scheduledjobs;

import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.repository.HousemaidRepository;
import java.util.Calendar;
import java.util.List;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

*/
/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 13 2019
 *         Jirra ACC-1135
 *//*

@Component
public class MaidVisaContractCancellationMaidSalaryHoldScheduledJob {

    private static final Logger logger =
            Logger.getLogger(MaidVisaContractCancellationMaidSalaryHoldScheduledJob.class.getName());

    @Autowired
    private HousemaidRepository housemaidRepository;

//    @com.magnamedia.core.annotation.MultiTenantsAdvised @Scheduled(cron = "0 0 0 4 * ?")
    public void run() {
        Calendar c = Calendar.getInstance();
        SelectQuery<Contract> query = new SelectQuery<>(Contract.class);
        query.filterBy(
                new SelectFilter()
                        .or("status", "IN", "Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)")
                        .or("scheduledDateOfTermination", "IS NOT NULL", null));
        query.filterBy("housemaid.excludedFromPayroll", "=", Boolean.FALSE);
        List<Contract> contracts = query.execute();
        if (contracts != null && contracts.isEmpty()){
            for (Contract contract : contracts){
                if (contract.getHousemaid() != null){
                    Housemaid housemaid = contract.getHousemaid();
                    housemaid.setExcludedFromPayroll(Boolean.TRUE);
                    housemaidRepository.save(housemaid);
                }
            }
        }
    }
}
*/
