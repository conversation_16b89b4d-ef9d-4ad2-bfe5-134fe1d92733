package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.MessagingService;
import com.magnamedia.repository.PayrollAccountantTodoRepository;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on April 13, 2021
 *         Jirra ACC-3315
 */

public class BankTransferConfirmationEmailJob implements MagnamediaJob {

    private static final Logger logger = Logger.getLogger(BankTransferConfirmationEmailJob.class.getName());

    @Override
    public void run(Map<String, ?> map) {

        if (!Boolean.parseBoolean(Setup.getRepository(PayrollAccountantTodoRepository.class)
                .existsBankTransferWaitingCooAction())) return;

        logger.log(Level.INFO, "start");

        Map<String, String> parameters = new HashMap();
        String baseURL = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/accounting/";
        parameters.put("link", baseURL + "coo-control?tab=BANK_TRANSFER_CONFIRMATION");

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("bank_transfer_confirmation_coo",
                        parameters, Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BANK_CONFIRMATION_AND_NIGHT_REVIEW_RECIPIENTS),
                        "Bank Confirmation " + new DateTime().toString("EEEE, MMM dd, yyyy"));
    }
}
