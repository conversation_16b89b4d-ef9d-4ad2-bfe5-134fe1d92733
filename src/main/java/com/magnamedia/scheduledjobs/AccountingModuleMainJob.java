package com.magnamedia.scheduledjobs;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.MvHousemaidNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.*;
import com.magnamedia.service.paymentstate.handlers.received.PaymentReceivedNotificationHandler;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR> Karra
 * Created on Dec 3, 2024
 */
public class AccountingModuleMainJob implements MagnamediaJob {
    private static final Logger logger = Logger.getLogger(AccountingModuleMainJob.class.getName());

    public static final String SEND_MESSAGE = "send_message";

    private BackgroundTaskHelper backgroundTaskHelper;
    private PaymentRepository paymentRepository;
    private ContractRepository contractRepository;
    private DirectDebitCancellationService directDebitCancellationService;
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    private PaymentReceivedNotificationHandler paymentReceivedNotificationHandler;
    private ObjectMapper objectMapper;
    private static boolean isRunning = false;
    private MaidVisaFailedMedicalCheckService maidVisaFailedMedicalCheckService;
    private EmailTemplateService emailTemplateService;
    private BankDirectDebitActivationRecordService bankDirectDebitActivationRecordService;
    public static final String DELAYED_REFUND = "delayed_refund";

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Started job");
        if (isRunning) {
            logger.info("another instance is running");
            return;
        }

        isRunning = true;

        try {
            run();
        } catch (Exception e) {
            e.printStackTrace();
        }

        isRunning = false;
        logger.info( "Ended job");
    }

    public void run() {
        objectMapper = Setup.getApplicationContext().getBean(ObjectMapper.class);
        paymentRepository = Setup.getRepository(PaymentRepository.class);
        contractRepository = Setup.getRepository(ContractRepository.class);
        directDebitCancellationService = Setup.getApplicationContext().getBean(DirectDebitCancellationService.class);
        maidVisaFailedMedicalCheckService = Setup.getApplicationContext().getBean(MaidVisaFailedMedicalCheckService.class);
        accountingEntityPropertyRepository = Setup.getApplicationContext().getBean(AccountingEntityPropertyRepository.class);
        objectMapper = Setup.getApplicationContext().getBean(ObjectMapper.class);
        emailTemplateService = Setup.getApplicationContext().getBean(EmailTemplateService.class);
        bankDirectDebitActivationRecordService = Setup.getApplicationContext().getBean(BankDirectDebitActivationRecordService.class);
        accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        paymentReceivedNotificationHandler = Setup.getApplicationContext().getBean(PaymentReceivedNotificationHandler.class);
        objectMapper = Setup.getApplicationContext().getBean(ObjectMapper.class);
        backgroundTaskHelper = Setup.getApplicationContext().getBean(BackgroundTaskHelper.class);

        // ACC-9005 ACC-9004 ACC-9272
        handleRunningBGTsInSequential();

        handleSendReportEmailAfterBackgroundTasksFinished();

        // ACC-9220
        handlePropertiesForConfirmDDsActivationRecords();

        // ACC-8918
        checkCancelledMVContracts();

        // ACC-8193
        removePaymentRequiredFlag();

        // ACC-8121
        processDelayedRefunds();

        // ACC-8796
        sendPostponeMessages();
    }

    private void handleRunningBGTsInSequential() {
        List<AccountingEntityProperty> result = accountingEntityPropertyRepository.findByKeyAndDoesntHaveRunningBGT(
                AccountingModule.RUN_BACKGROUND_TASK_IN_SEQUENTIAL,
                Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished));

        result.forEach(a -> {
            try {
                logger.info("a id: " + a.getId());
                HashMap<String, Object> payload = objectMapper.readValue(a.getValue(), HashMap.class);

                BackgroundTaskHelper.createBGTParsingStatementUploaded(UploadStatementEntityType.valueOf(a.getPurpose()),
                        a.getPurpose(), payload);

                accountingEntityPropertyRepository.delete(a);

            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void handlePropertiesForConfirmDDsActivationRecords() {
        try {
            AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndDoesntHaveRunningBGTRelatedToEntityType(
                    AccountingModule.RUN_BACKGROUND_TASK_AFTER_CHECK_RELATED_ENTITY_TYPE,
                    Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished))
                    .stream()
                    .findFirst()
                    .orElse(null);

            if (a == null) return;

            logger.info("a id: " + a.getId());
            bankDirectDebitActivationRecordService.createBGTForConfirmActivationRecords(a.getValue());
            accountingEntityPropertyRepository.delete(a);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleSendReportEmailAfterBackgroundTasksFinished() {
        List<AccountingEntityProperty> result = accountingEntityPropertyRepository.findByKeyAndDoesntHaveRunningBGTAndPurpose(
                AccountingModule.SEND_REPORT_EMAIL_AFTER_BACKGROUND_TASKS_FINISHED,
                Arrays.asList(BackgroundTaskStatus.Failed, BackgroundTaskStatus.Finished));

        result.forEach(a -> {
            try {
                logger.info("a id: " + a.getId());
                emailTemplateService.sendEmailForBankDDReport(objectMapper.readValue(a.getValue(), HashMap.class));
                accountingEntityPropertyRepository.delete(a);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void removePaymentRequiredFlag() {
        Long lastId = -1L;
        Page<Payment> p;
        do {
            p = paymentRepository.findByReplacedTrueAndRequiredForBouncingTrue(lastId, PageRequest.of(0, 100));

            for (Payment payment : p.getContent()) {
                logger.info("payment id: " + payment.getId());
                try {
                    payment.setRequiredForBouncing(false);
                    paymentRepository.silentSave(payment);

                    if (payment.getContract() != null && payment.getContract().isMaidVisa() &&
                            (ContractStatus.CANCELLED.equals(payment.getContract().getStatus()) ||
                                    ContractStatus.EXPIRED.equals(payment.getContract().getStatus()))) {
                        directDebitCancellationService.maidVisaCancelDds(payment.getContract());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (!p.getContent().isEmpty()) {
                lastId = p.getContent().get(p.getContent().size() - 1).getId();
            }
        } while (!p.getContent().isEmpty());
    }

    private void checkCancelledMVContracts() {
        Long lastId = -1L;
        Page<Contract> contracts;

        do {
            contracts = contractRepository.findCancelledMVContractsWithNoRequiredPayments(
                    lastId, DirectDebitService.notAllowedStatuses, PageRequest.of(0, 100));

            for (Contract contract : contracts.getContent()) {
                try {
                    logger.info("cancelled MV contract: " + contract.getId());
                    directDebitCancellationService.maidVisaCancelDds(contract);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (!contracts.getContent().isEmpty()) {
                lastId = contracts.getContent().get(contracts.getContent().size() - 1).getId();
            }
        } while (!contracts.getContent().isEmpty());
    }

    @Transactional
    public void processDelayedRefunds() {
        logger.info("Processing delayed refunds");

        int delayHours = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_REFUND_DELAY_HOURS));

        // Get all properties that were created delayHours ago
        List<AccountingEntityProperty> properties = accountingEntityPropertyRepository
                .findByKeyAndCreationDateAndDeletedFalse(
                        DELAYED_REFUND, new DateTime().minusHours(delayHours).toDate());

        PaymentService paymentService = Setup.getApplicationContext()
                .getBean(PaymentService.class);
        DirectDebitCancelationToDoController directDebitCancelationToDoController =
                Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        for (AccountingEntityProperty property : properties) {
            try {
                // Get the contract and payment info from the property
                Payment payment = (Payment) property.getOrigin();

                accountingEntityPropertyRepository.delete(property);

                if (!ContractStatus.CANCELLED.equals(payment.getContract().getStatus()) &&
                        !ContractStatus.EXPIRED.equals(payment.getContract().getStatus())) {
                    logger.info("Skipping refund for non-cancelled payment Id: " + payment.getId());
                    continue;
                }

                Map<String, Object> map;
                if (property.getPurpose().equals("checkIfContractCancelledWithinFirstXDays")) {

                    map = paymentService.doFullRefund(payment.getContract(), payment.getContract().getClient(),
                            payment, payment.getAmountOfPayment());
                    directDebitCancelationToDoController.cancelAllContractDDs(payment.getContract());
                } else {
                    // Start the refund flow
                    map = paymentService.maidCCFlow(payment, payment.getContract());

                }

                if (!map.isEmpty()) {
                    map.put("id", payment.getId());
                    paymentService.updatePaymentDirectCall(map);
                }

            } catch (Exception e) {
                logger.severe("Error processing delayed refund: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public void sendPostponeMessages() {
        PaymentReceivedNotificationHandler paymentReceivedNotificationHandler = Setup.getApplicationContext()
                .getBean(PaymentReceivedNotificationHandler.class);
        Page<AccountingEntityProperty> l = accountingEntityPropertyRepository.findByKeyAndOriginTypeAndMessageSentDateBeforeAndDeletedFalse(
                    SEND_MESSAGE,
                    "Contract",
                    new Date(),
                    PageRequest.of(0, 200));

        l.getContent().forEach(a -> {
            try {
                Contract c = (Contract) a.getOrigin();
                logger.info("a id: " + a.getId());
                Map<String, String> m = objectMapper.readValue(a.getValue(), Map.class);

                switch (m.get("templateBaseName")) {
                    case "MV_SECOND_FAILED_MEDICAL_CHECK_SALARY":
                        maidVisaFailedMedicalCheckService.sendClientFailedMedicalCheckMessages(c,
                                MvNotificationTemplateCode.MV_SECOND_FAILED_MEDICAL_CHECK_SALARY_FOR_DIRECT_DEBIT_CONTRACT);

                        maidVisaFailedMedicalCheckService.
                                sendMaidFailedMedicalCheckMessages(c,
                                        MvHousemaidNotificationTemplateCode.MV_HOUSEMAID_SECOND_FAILED_MEDICAL_CHECK_SALARY);
                        break;
                    case "PAYMENT_RECEIVED_NOTIFICATION":
                        paymentReceivedNotificationHandler.sendPaymentReceivedNotification(
                                c, objectMapper.readValue(m.get("payments"), List.class));
                        break;
                }

                accountingEntityPropertyRepository.delete(a);

            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
}