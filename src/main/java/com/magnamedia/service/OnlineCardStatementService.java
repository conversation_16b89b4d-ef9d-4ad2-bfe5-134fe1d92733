package com.magnamedia.service;


import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.epayment.EPaymentProvider;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.ContractPaymentWrapper;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementFile;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementRecord;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementTransaction;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.*;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementFileRepository;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementRecordRepository;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementTransactionRepository;
import com.opencsv.CSVReader;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Paths;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementRecord.BreakdownType.*;


/**
 * <AUTHOR> Mahfoud
 */

// ACC-5587
@Service
public class OnlineCardStatementService {

    protected static final Logger logger = Logger.getLogger(OnlineCardStatementService.class.getName());

    @Autowired
    private OnlineCardStatementRecordRepository onlineCardStatementRecordRepository;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private OnlineCardStatementTransactionRepository onlineCardStatementTransactionRepository;
    @Autowired
    private OnlineCardStatementFileRepository onlineCardStatementFileRepository;

    public static final List<OnlineCardStatementRecord.BreakdownType> CHECKOUT_FEES_TYPE_TRANSACTION = Arrays.asList(
            AUTHORIZATION_FIXED_FEE, REFUND_FIXED_FEE, PREMIUM_VARIABLE_FEE,
            SCHEME_VARIABLE_FEE, SCHEME_FIXED_FEE, INTER_CHANGE_FIXED_FEE,
            SCHEME_VARIABLE_FEE_TAX, SCHEME_FIXED_FEE_TAX,
            PREMIUM_VARIABLE_FEE_TAX, INTERCHANGE_FIXED_FEE_TAX,
            AUTHORIZATION_FIXED_FEE_TAX);

    public static final List<OnlineCardStatementRecord.BreakdownType> CHECKOUT_ALLOWED_SAVE_TYPES = Arrays.asList(
            CAPTURE, REFUND);

    public void parseFile(Map<String, Object> payload) {
        OnlineCardStatementFile entity = onlineCardStatementFileRepository.findOne(Long.parseLong(payload.get("entityId").toString()));

        extractRecords(entity);
        processRecords(entity);
        entity.setUploadedDate(new Date());
        onlineCardStatementFileRepository.save(entity);
    }

    public void extractRecords(OnlineCardStatementFile file) {

        switch (file.getProvider()){
            case PAYTABS:
                paytabsExtractRecords(file);
                break;
            case CHECKOUT:
                checkoutExtractRecords(file);
                break;
        }
    }

    public void paytabsExtractRecords(OnlineCardStatementFile file) {

        double commissionSum = 0.0; // Sum of column P
        double transactionFeeSum = 0.0; // Sum of column AH
        double vatOnFeeSum = 0.0; // Sum of column AI
        double vatOnCommissionSum = 0.0; // Sum of column AJ
        double salesAmount = 0.0; // Sum of column O
        double netAmount = 0.0; // Sum of column Q
        Date relatedToDate = null; // The date in the column J
        double commissionLastRow = 0.0; // The value of the last row of column P
        double vatOnFeeLastRow = 0.0; // The value of the last row of column AI
        double vatOnCommissionLastRow = 0.0; // The value of the last row of column AJ

        InputStream attachmentInputStream = null;
        try {
            List<OnlineCardStatementRecord> records = new ArrayList<>();
            NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);

            attachmentInputStream = Storage.getStream(file.getAttachment("online_card_statement"));
            CSVReader reader = new CSVReader(new InputStreamReader(attachmentInputStream));
            String[] nextLine;

            logger.log(Level.INFO,"File id: {0}", file.getId());

            //skip the header of the file
            reader.readNext();

            while ((nextLine = reader.readNext()) != null) {
                try {
                    boolean finalRecord = nextLine[3].trim().toLowerCase().contains("merchant totals");

                    // Pos transaction
                    if (records.isEmpty()) {
                        relatedToDate = new Date(DateUtil.resolveDateFromCsvFile(nextLine[9].trim()).getTime());
                    }

                    if (finalRecord) {
                        if (!nextLine[15].trim().isEmpty()) {
                            commissionLastRow = nf_in.parse(nextLine[15].trim()).doubleValue();
                        }
                        if (!nextLine[34].trim().isEmpty()) {
                            vatOnFeeLastRow = nf_in.parse(nextLine[34].trim()).doubleValue();
                        }
                        if (!nextLine[35].trim().isEmpty()) {
                            vatOnCommissionLastRow = nf_in.parse(nextLine[35].trim()).doubleValue();
                        }
                        break;
                    } else {
                        if (!nextLine[15].trim().isEmpty()) {
                            commissionSum += nf_in.parse(nextLine[15].trim()).doubleValue();
                        }
                        if (!nextLine[33].trim().isEmpty()) {
                            transactionFeeSum += nf_in.parse(nextLine[33].trim()).doubleValue();
                        }
                        if (!nextLine[34].trim().isEmpty()) {
                            vatOnFeeSum += nf_in.parse(nextLine[34].trim()).doubleValue();
                        }
                        if (!nextLine[35].trim().isEmpty()) {
                            vatOnCommissionSum += nf_in.parse(nextLine[35].trim()).doubleValue();
                        }
                        if (!nextLine[14].trim().isEmpty()) {
                            salesAmount += nf_in.parse(nextLine[14].trim()).doubleValue();
                        }
                        if(!nextLine[16].trim().isEmpty()) {
                            netAmount += nf_in.parse(nextLine[16].trim()).doubleValue();
                        }
                    }

                    if (nextLine[9].trim().isEmpty()) break;
                    OnlineCardStatementRecord record = new OnlineCardStatementRecord();
                    record.setOnlineCardStatementFile(file);
                    // date
                    record.setPaymentDate(new Date(DateUtil.resolveDateFromCsvFile(nextLine[9].trim()).getTime()));
                    // authorization code
                    String authorizationCode = nextLine[12].trim();
                    record.setAuthorizationCode(authorizationCode.replaceFirst("^0+(?!$)", ""));
                    // amount
                    record.setAmount(nf_in.parse(nextLine[14].trim()).doubleValue());
                    if (record.getAmount() < 0) {
                        record.setStatus(OnlineCardStatementRecord.OnlineCardStatementRecordStatus.UNMATCHED_REFUND);
                    }
                    //transferReference
                    record.setTransferReference(nextLine[17].trim());
                    records.add(record);

                } catch (ParseException ex) {
                    logger.log(Level.SEVERE, "Parsing Exception: " + ex.getMessage());
                    ex.printStackTrace();
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                    ex.printStackTrace();
                }
            }

            if (!records.isEmpty()) {
                onlineCardStatementRecordRepository.save(records);
            }
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            file.setCommissionSum(commissionSum);
            file.setTransactionFeeSum(transactionFeeSum);
            file.setVatOnFeeSum(vatOnFeeSum);
            file.setVatOnCommissionSum(vatOnCommissionSum);
            file.setSalesAmount(salesAmount);
            file.setNetAmount(netAmount);
            file.setRelatedToDate(relatedToDate);
            file.setCommissionLastRow(commissionLastRow);
            file.setVatOnFeeLastRow(vatOnFeeLastRow);
            file.setVatOnCommissionLastRow(vatOnCommissionLastRow);

            StreamsUtil.closeStream(attachmentInputStream);
        }
    }

    public void checkoutExtractRecords(OnlineCardStatementFile file) {

        double paymentsSum = 0.0; // Sum of column AF when AD = Capture
        double refundsSum = 0.0; // Sum of Column AF when AD = Refund
        double gatewayFeesSum = 0.0; // Sum of Column AF when AD = " Authorization Fixed Fee" Or "Refund Fixed Fee"
        double acquiringPremiumsSum = 0.0; // Sum of Column AF when AD = "Premium Variable Fee"
        double schemeFeesSum = 0.0; // Sum of Column AF when AD = "Scheme Variable Fee" or " Scheme Fixed fee"
        double interchangeFeeSum = 0.0; // Sum of Column AF when AD = " Interchange Fixed Fee"
        double networkTokenFee = 0.0; // Sum of Column AF when AD = "Network Token Provisioning Fixed Fee" or "Network Token Update Fixed Fee"
        double vatSum = 0.0; // Sum of column AI
        double holdingCurrencyAmount = 0.0; // Sum of column AF
        String checkoutID = null ; // Column S
        Date relatedToDate = null; // Column M - without the time

        InputStream attachmentInputStream = null;
        try {
            List<OnlineCardStatementRecord> records = new ArrayList<>();
            NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);

            attachmentInputStream = Storage.getStream(file.getAttachment("online_card_statement"));
            CSVReader reader = new CSVReader(new InputStreamReader(attachmentInputStream));
            String[] nextLine;

            logger.log(Level.INFO,"File id: {0}", file.getId());

            //skip the header of the file
            reader.readNext();
            int index = 1;

            while ((nextLine = reader.readNext()) != null) {
                try {
                    logger.info("row id: " + index++);
                    if (nextLine[0] == null || nextLine[0].trim().isEmpty()) break;
                    if (nextLine[12].trim().isEmpty()) break;
                    logger.info("reach 260");
                    // Pos transaction
                    if (records.isEmpty()) {
                        relatedToDate = new LocalDateTime(DateUtil.resolveDateFromCsvFile(nextLine[12].trim()))
                                .toLocalDate()
                                .toDate();
                    }
                    logger.info("reach 267");
                    OnlineCardStatementRecord.BreakdownType type = null;
                    if (nextLine[29] != null && nextLine[31] != null &&
                        !nextLine[29].trim().isEmpty() && !nextLine[31].trim().isEmpty()) {

                        type = OnlineCardStatementRecord.BreakdownType.getEnum(nextLine[29].trim());
                        if(type != null) {
                            switch (type) {
                                case CAPTURE:
                                    paymentsSum += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                case REFUND:
                                    refundsSum += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                case VOID_FIXED_FEE:
                                case AUTHORIZATION_FIXED_FEE:
                                case REFUND_FIXED_FEE:
                                    gatewayFeesSum += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                case PREMIUM_VARIABLE_FEE:
                                    acquiringPremiumsSum += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                case SCHEME_VARIABLE_FEE:
                                case SCHEME_FIXED_FEE:
                                    schemeFeesSum += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                case INTER_CHANGE_FIXED_FEE:
                                    interchangeFeeSum += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                case NETWORK_TOKEN_UPDATE_FIXED_FEE:
                                case NETWORK_TOKEN_PROVISIONING_FIXED_FEE:
                                    networkTokenFee += nf_in.parse(nextLine[31].trim()).doubleValue();
                                    break;
                                default:
                                    break;
                            }
                        }
                    }

                    logger.info("reach 301");

                    if (!nextLine[34].trim().isEmpty()) {
                        vatSum += nf_in.parse(nextLine[34].trim()).doubleValue();
                    }
                    if (!nextLine[18].trim().isEmpty()) {
                        checkoutID = nextLine[18].trim();
                    }
                    if (!nextLine[31].trim().isEmpty()) {
                        holdingCurrencyAmount += nf_in.parse(nextLine[31].trim()).doubleValue();
                    }

                    OnlineCardStatementRecord record = new OnlineCardStatementRecord();
                    record.setOnlineCardStatementFile(file);
                    // date
                    record.setPaymentDate(new LocalDateTime(DateUtil.resolveDateFromCsvFile(nextLine[12].trim()))
                            .toLocalDate()
                            .toDate());
                    // amount
                    record.setAmount(nf_in.parse(nextLine[30].trim()).doubleValue());
                    if (type == null || !CHECKOUT_ALLOWED_SAVE_TYPES.contains(type)) {
                        continue;
                    }

                    if (record.getAmount() < 0 && type != null && type.equals(OnlineCardStatementRecord.BreakdownType.REFUND)) {
                        record.setStatus(OnlineCardStatementRecord.OnlineCardStatementRecordStatus.UNMATCHED_REFUND);
                    }
                    //Payment ID
                    record.setTransferReference(nextLine[11].trim());
                    // BreakdownType
                    record.setBreakdownType(OnlineCardStatementRecord.BreakdownType.getEnum(nextLine[29].trim()));
                    logger.info("reach 331");

                    records.add(record);
                } catch (ParseException ex) {
                    logger.log(Level.SEVERE, "Parsing Exception: " + ex.getMessage());
                    ex.printStackTrace();
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                    ex.printStackTrace();
                }
            }

            if (!records.isEmpty()) {
                onlineCardStatementRecordRepository.save(records);
            }
        } catch (Exception ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            file.setCheckoutPaymentsSum(paymentsSum);
            file.setCheckoutRefundsSum(refundsSum);
            file.setCheckoutGatewayFeesSum(gatewayFeesSum);
            file.setCheckoutAcquiringPremiumsSum(acquiringPremiumsSum);
            file.setCheckoutSchemeFeesSum(schemeFeesSum);
            file.setCheckoutInterchangeFeeSum(interchangeFeeSum);
            file.setCheckoutNetworkTokenFee(networkTokenFee);
            file.setCheckoutVatSum(vatSum);
            file.setCheckoutHoldingCurrencyAmount(holdingCurrencyAmount);
            file.setCheckoutID(checkoutID);
            file.setRelatedToDate(relatedToDate);

            StreamsUtil.closeStream(attachmentInputStream);
        }
    }

    public void processRecords(OnlineCardStatementFile file) {

        List<OnlineCardStatementRecord> records = file.getProvider().equals(EPaymentProvider.PAYTABS) ?
                onlineCardStatementRecordRepository.findByOnlineCardStatementFile(file) :
                onlineCardStatementRecordRepository.findByOnlineCardStatementFileAndBreakdownTypeIn(
                        file, Arrays.asList(OnlineCardStatementRecord.BreakdownType.CAPTURE,
                                            OnlineCardStatementRecord.BreakdownType.REFUND));
        if (records.isEmpty()) return;

        List<ContractPaymentConfirmationToDo> todosForClientRefund = contractPaymentConfirmationToDoRepository
                .findByOnlineCardPaymentAndTransferReference(records.stream()
                        .filter(r -> r.getAmount() < 0)
                        .map(OnlineCardStatementRecord::getTransferReference).collect(Collectors.toList()));

        List<Map> todos = Setup.getApplicationContext()
                .getBean(QueryService.class)
                .creditCardStatementGetErpPayments(records.stream()
                        .filter(r -> r.getAmount() > 0)
                        .map(OnlineCardStatementRecord::getTransferReference)
                        .collect(Collectors.toList()));

        processRecordsMatchedReceivedAndRefund(records, todosForClientRefund, todos);
    }

    private void processRecordsMatchedReceivedAndRefund(
            List<OnlineCardStatementRecord> records,
            List<ContractPaymentConfirmationToDo> todosForClientRefund,
            List<Map> todos) {

        Set<Long> s = new HashSet<>();
        records.forEach(r -> {
            logger.info("record id: " + r.getId());

            ContractPaymentConfirmationToDo todo = r.getAmount() < 0 ?
                    getMatchedRefund(todosForClientRefund, r) :
                    getMatchedPayment(todos, r);

            if (todo == null)  {
                logger.info("There is no match -> set status unmatched");
                return;
            }

            if (s.contains(todo.getId())) {
                logger.info("Todo id: " + todo.getId() + " already matched");
                return;
            }
            s.add(todo.getId());

            if (r.getAmount() < 0) todosForClientRefund.remove(todo);

            confirmTodoBGT(r, todo);
        });
    }

    private ContractPaymentConfirmationToDo getMatchedRefund(
            List<ContractPaymentConfirmationToDo> l, OnlineCardStatementRecord r) {
        logger.info("Has negative amount set as refund");

        return l.stream()
                .filter(t -> t.getTransferReference().equals(r.getTransferReference()) &&
                             t.getTotalAmount().equals(Math.abs(r.getAmount())))
                .findFirst()
                .orElse(null);
    }

    private ContractPaymentConfirmationToDo getMatchedPayment(List<Map> l, OnlineCardStatementRecord r) {

        return l.stream()
                .filter(m -> paymentIsMatched(m, r))
                .map(m -> (ContractPaymentConfirmationToDo) m.get("todo"))
                .findFirst()
                .orElse(null);
    }

    private boolean paymentIsMatched(Map m, OnlineCardStatementRecord r) {

        ContractPaymentConfirmationToDo t = (ContractPaymentConfirmationToDo) m.get("todo");
        return t.getTransferReference().equals(r.getTransferReference()) &&
                t.getTotalAmount().equals(r.getAmount());
    }

    public AccountingPage searchByFileStatementAndGridName(
            OnlineCardStatementFile onlineCardStatementFile, Pageable pageable,
            Map<String, Object> map) {

        Map<String, Object> para = new HashMap<>();
        String gridName = (String) map.get("gridName");
        String clientName = (String) map.get("clientName");
        Long contractId = (Long) map.get("contractId");
        Long paymentId = (Long) map.get("paymentId");
        PicklistItem paymentType = (PicklistItem) map.get("paymentType");
        String authorizationCode = (String) map.get("authorizationCode");
        String transferReference = (String) map.get("transferReference");

        StringBuilder select = new StringBuilder("select new map(");
        String countSelect = "select count(o.id) from OnlineCardStatementRecord o ";
        String totalAmountSelect = "select sum(o.amount) from OnlineCardStatementRecord o ";

        boolean searchByRecord = true;
        String todoAlias = "o.contractPaymentConfirmationToDo";
        switch (gridName) {
            case "MatchedOnlineCardRecord":
                select.append("o.id as recordId, o.paymentDate as paymentDate, o.transferReference as transferReference, " +
                                "o.amount as paymentAmount, o.authorizationCode as authorizationCode, " +
                                "c.id as contractId, cl.name as clientName, cl.id as clientId)")
                        .append("from OnlineCardStatementRecord o ")
                        .append("join o.contractPaymentConfirmationToDo.contractPaymentTerm.contract as c ")
                        .append("join c.client as cl ");
                para.put("status", OnlineCardStatementRecord.OnlineCardStatementRecordStatus.MATCHED);
                para.put("file", onlineCardStatementFile);
                break;
            case "UnmatchedOnlineCardRecord":
                select.append("o.id as recordId, o.paymentDate as paymentDate, o.amount as paymentAmount, o.authorizationCode as authorizationCode, o.transferReference as transferReference) ")
                        .append("from OnlineCardStatementRecord o ");
                para.put("status", OnlineCardStatementRecord.OnlineCardStatementRecordStatus.UNMATCHED);
                para.put("file", onlineCardStatementFile);
                break;
            case "MatchedClientRefunds":
                select.append("o.id as recordId, o.paymentDate as paymentDate, o.amount as paymentAmount, o.authorizationCode as authorizationCode, " +
                              "o.transferReference as transferReference, c.id as contractId, cl.name as clientName, cl.id as clientId, ot.transaction.id as transactionId)")
                        .append("from OnlineCardStatementRecord o ")
                        .append("inner join OnlineCardStatementTransaction ot on ot.onlineCardStatementRecord.id = o.id ")
                        .append("join o.contractPaymentConfirmationToDo.contractPaymentTerm.contract as c ")
                        .append("join c.client as cl ");
                para.put("status", OnlineCardStatementRecord.OnlineCardStatementRecordStatus.MATCHED_REFUND);
                para.put("file", onlineCardStatementFile);
                break;
            case "UnmatchedRefundOnlineCardRecord":
                select.append("o.id as recordId, o.paymentDate as paymentDate, abs(o.amount) as paymentAmount, o.authorizationCode as authorizationCode, " +
                                "o.transferReference as transferReference )")
                        .append("from OnlineCardStatementRecord o ");
                para.put("status", OnlineCardStatementRecord.OnlineCardStatementRecordStatus.UNMATCHED_REFUND);
                para.put("file", onlineCardStatementFile);
                break;
            case "UnmatchedInErpOnly":
                searchByRecord = false;
                todoAlias = "todo";
                countSelect = "select count(todo.id) from ContractPaymentConfirmationToDo todo ";
                totalAmountSelect = "select sum(cpw.amount) from ContractPaymentWrapper cpw " +
                        "join cpw.contractPaymentConfirmationToDo todo ";

                select.append("todo.id as todoId, todo.creationDate as paymentDate, " +
                                QueryService.getDateChangedToReceivedViaWrapperStrWithAlias("todo") +
                                "todo.cleanAuthorizationCode as authorizationCode, todo.transferReference as transferReference, " +
                                "c.id as contractId, cl.name as clientName, cl.id as clientId) ")
                        .append("from ContractPaymentConfirmationToDo todo ")
                        .append("join todo.contractPaymentTerm.contract as c ")
                        .append("join c.client as cl ");
                break;
        }

        StringBuilder cond = searchByRecord ?
                new StringBuilder(" where o.onlineCardStatementFile = :file and o.status = :status ") :
                new StringBuilder(" where todo.payingOnline = true and todo.paymentMethod = 'Card' " +
                        "and todo.confirmed = false and todo.showOnERP = true and todo.transferReference is not null") ;

        if (clientName != null) {
            cond.append(" and ")
                    .append(todoAlias)
                    .append(".contractPaymentTerm.contract.client.name ")
                    .append("like :clientName");
            para.put("clientName", "%" + clientName + "%");
        }

        if (contractId != null) {
            cond.append(" and ")
                    .append(todoAlias)
                    .append(".contractPaymentTerm.contract.id ")
                    .append("= :contractId");
            para.put("contractId", contractId);
        }

        if (authorizationCode != null) {
            cond.append(" and ( ")
                    .append(searchByRecord ? "o.authorizationCode " : "todo.authorizationCode ")
                    .append("= :authorizationCode");
            if (!searchByRecord) {
                cond.append(" or todo.cleanAuthorizationCode ")
                        .append("= :cleanAuthorizationCode ");
                para.put("cleanAuthorizationCode", authorizationCode);
            }
            cond.append(" ) ");
            para.put("authorizationCode", authorizationCode);
        }

        if (transferReference != null) {
            cond.append(" and ( ")
                    .append(searchByRecord ? "o.transferReference " : "todo.transferReference ")
                    .append("= :transferReference");

            cond.append(" ) ");
            para.put("transferReference", transferReference);
        }

        if (paymentId != null) {
            cond.append(" and exists(select 1 from ContractPaymentWrapper c ")
                    .append("where c.contractPaymentConfirmationToDo = ")
                    .append(todoAlias)
                    .append(" and c.generatedPaymentId = :paymentId) ");
            para.put("paymentId", paymentId);
        }

        if (paymentType != null) {
            cond.append(" and exists(select 1 from ContractPaymentWrapper c ")
                    .append("where c.contractPaymentConfirmationToDo = ")
                    .append(todoAlias)
                    .append(" and c.paymentType.code = :paymentType) ");
            para.put("paymentType", paymentType.getCode());
        }

        Page<Map> result = new SelectQuery<>(
                select.toString() + cond, countSelect + cond, Map.class, para)
                .execute(pageable);

        if (gridName.equals("MatchedOnlineCardRecord") || gridName.equals("MatchedClientRefunds"))
            fillPaymentsForMatchedRecords(result.getContent());

        if (gridName.equals("UnmatchedInErpOnly"))
            fillPaymentsForUnmatchedInErpOnly(result.getContent());

        if (!searchByRecord) {
            QueryService.getDateChangedToReceivedViaResult(result);
        }

        // fill total amount
        SelectQuery<Double> q = new SelectQuery<>(
                totalAmountSelect + cond, countSelect + cond, Double.class, para);
        double totalAmount = Math.abs(q.executeScalar().doubleValue());

        return new AccountingPage(result.getContent(), pageable, result.getTotalElements(), totalAmount);
    }

    public void confirmTodoBGT(OnlineCardStatementRecord r, ContractPaymentConfirmationToDo todo) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "confirmOnlineCardStatementTodo_" + r.getOnlineCardStatementFile().getId(),
                        "accounting",
                        "onlineCardStatementService",
                        "confirmTodo")
                        .withParameters(
                                new Class[] {Long.class, Long.class},
                                new Object[] {r.getId() , todo.getId()})
                        .withRelatedEntity("OnlineCardStatementRecord", r.getId())
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());

    }

    public void confirmTodo(Long recordId, Long todoId) {

        confirmTodo(
                onlineCardStatementRecordRepository.findOne(recordId),
                contractPaymentConfirmationToDoRepository.findOne(todoId),
                false);
    }

    public void confirmTodo(
            OnlineCardStatementRecord r,
            ContractPaymentConfirmationToDo todo,
            boolean forceUpdateTransRef) {

        logger.log(Level.INFO, "record match todo id: {0}", todo.getId());
        // create transaction
        for (ContractPaymentWrapper w : todo.getContractPaymentList()) {
            try {
                // ACC-6411
                Map<String, Object> map = todo.isPayingOnline() ?
                        contractPaymentConfirmationToDoService.confirmOnlineCardPayment(w) :
                        contractPaymentConfirmationToDoService.confirmNonOnlinePaymentAndCreateTransaction(w);

                logger.log(Level.INFO, "w id: {0}", w.getId());

                if (map == null || !map.containsKey("payment") || !map.containsKey("transaction")) {
                    throw new BusinessException("A problem with updating client payment");
                }

                OnlineCardStatementTransaction rt = new OnlineCardStatementTransaction();
                rt.setOnlineCardStatementRecord(r);
                rt.setPayment(map.containsKey("payment") ? (Payment) map.get("payment") : null);
                rt.setTransaction(map.containsKey("transaction") ? (Transaction) map.get("transaction") : null);
                onlineCardStatementTransactionRepository.save(rt);
            } catch (Exception e) {
                e.printStackTrace();
                throw new BusinessException("A problem with updating client payment");
            }
        }

        todo = contractPaymentConfirmationToDoRepository.findOne(todo.getId());
        if (r.getTransferReference() != null && (todo.getTransferReference() == null || todo.getTransferReference().isEmpty() || forceUpdateTransRef)) {
            todo.setTransferReference(r.getTransferReference());
        }
        todo.setConfirmed(true);
        contractPaymentConfirmationToDoRepository.save(todo);

        r.setStatus(r.getAmount() > 0 ?
                OnlineCardStatementRecord.OnlineCardStatementRecordStatus.MATCHED :
                OnlineCardStatementRecord.OnlineCardStatementRecordStatus.MATCHED_REFUND);
        r.setContractPaymentConfirmationToDo(todo);
        onlineCardStatementRecordRepository.save(r);

        // if(to do.isPayingOnline()) return; ACC-8333

        if (r.getAmount() < 0) return;

        // ACC-5156 ACC-5553
        Setup.getApplicationContext().getBean(FlowProcessorService.class)
                .retractContractTermination(todo.getContractPaymentTerm(), todo);
    }

    private void fillPaymentsForMatchedRecords(
            List<Map> records) {

        List<Map> payments = onlineCardStatementTransactionRepository.findGridInfoByRecordsIds(
                records.stream()
                        .map(r -> (Long)r.get("recordId"))
                        .collect(Collectors.toList()));

        payments.forEach(p -> {
            Map r = records.stream()
                    .filter(o -> o.get("recordId").equals(p.get("rId")))
                    .findFirst()
                    .orElse(null);

            Map rp = (Map) r.get("payments");
            if (rp == null) rp = new HashMap<>();

            if (p.get("paymentId") != null)
                rp.put("paymentId", (rp.containsKey("paymentId") ? rp.get("paymentId") + "/" : "") + p.get("paymentId"));

            if (p.get("paymentType") != null)
                rp.put("paymentType", (rp.containsKey("paymentType") ? rp.get("paymentType") + "/" : "") + p.get("paymentType"));

            if (p.get("transactionId") != null) {
                rp.put("transactionId", (rp.containsKey("transactionId") ? rp.get("transactionId") + "/" : "") + p.get("transactionId"));
            }

            r.put("payments", rp);
        });
    }

    private void fillPaymentsForUnmatchedInErpOnly(
            List<Map> records) {

        List<Map> payments = contractPaymentConfirmationToDoRepository.findOnlineCardStatementGridInfoByTodoIds(
                records.stream()
                        .map(r -> (Long)r.get("todoId"))
                        .collect(Collectors.toList()));

        payments.forEach(p -> {
            Map r = records.stream()
                    .filter(o -> o.get("todoId").equals(p.get("todoId")))
                    .findFirst()
                    .orElse(null);

            Map rp = (Map) r.get("payments");

            Double amount = (Double) p.get("paymentAmount");;
            if (rp != null)  {
                amount += (Double) r.get("paymentAmount");
            }
            else rp = new HashMap<>();

            rp.put("paymentId", (rp.containsKey("paymentId") ? rp.get("paymentId") + "/" : "") + p.get("paymentId"));
            rp.put("paymentType", (rp.containsKey("paymentType") ? rp.get("paymentType") + "/" : "") + p.get("paymentType"));

            r.put("paymentAmount", amount);
            r.put("payments", rp);
        });
    }

    public void generateExcelReport(
            HttpServletResponse response,
            OnlineCardStatementFile f,
            String gridName,
            boolean exportAll,
            Map<String, Object> map) throws IOException {

       boolean payTabsProvider = f.getProvider().equals(EPaymentProvider.PAYTABS);

        List<String> gridNames = exportAll ? Arrays.asList(
                "MatchedOnlineCardRecord",
                "UnmatchedOnlineCardRecord",
                "UnmatchedInErpOnly",
                "MatchedClientRefunds" ,
                "UnmatchedRefundOnlineCardRecord") :
                Collections.singletonList(gridName);

        XSSFWorkbook workbook = new XSSFWorkbook();

        XSSFFont boldTitleFont = workbook.createFont();
        boldTitleFont.setBold(true);
        boldTitleFont.setFontHeightInPoints((short) 16);

        XSSFFont boldFont = workbook.createFont();
        boldFont.setBold(true);

        DataFormat fmt = workbook.createDataFormat();

        XSSFSheet spreadsheet = workbook.createSheet("CreditCardStatementResult");
        spreadsheet.setColumnWidth(0, 25 * 256);
        spreadsheet.setColumnWidth(1, 25 * 256);
        spreadsheet.setColumnWidth(2, 25 * 256);
        spreadsheet.setColumnWidth(3, 25 * 256);
        spreadsheet.setColumnWidth(4, 25 * 256);
        spreadsheet.setColumnWidth(5, 25 * 256);
        spreadsheet.setColumnWidth(6, 25 * 256);
        spreadsheet.setColumnWidth(7, 25 * 256);
        spreadsheet.setColumnWidth(8, 25 * 256);
        spreadsheet.setColumnWidth(9, 25 * 256);

        int rowId = 0;
        Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE);

        CellStyle style = workbook.createCellStyle();
        if (fmt != null) style.setDataFormat(fmt.getFormat("@"));

        if (gridNames.contains("MatchedOnlineCardRecord")) {

            map.put("gridName", "MatchedOnlineCardRecord");
            AccountingPage matchedOnlineCardRecord =
                    searchByFileStatementAndGridName(f, pageable, map);

            buildTableNameRow(workbook, spreadsheet,
                    "Matched and confirmed " + matchedOnlineCardRecord.getTotalElements() +
                            ", Total amount " + matchedOnlineCardRecord.getTotalSum(), fmt, rowId);
            buildHeadTableRow(style,
                    spreadsheet,
                    payTabsProvider ?
                            new String[]{"#", "Date Changed to Received", "Payment Amount", "Payment type", "Payment ID",
                                    "Authorization code", "Tran Ref Number", "Client Name", "Contract ID", "Transaction number"} :
                            new String[]{"#", "Date Changed to Received", "Payment Amount", "Payment type", "Payment ID",
                                    "Checkout Payment ID", "Client Name", "Contract ID", "Transaction number"} ,
                    ++rowId);

            int tableIndex = 0;
            for (Object r : matchedOnlineCardRecord.getContent()) {
                addRowForGrid1AndGrid5((Map) r,
                        style,
                        spreadsheet,
                        ++rowId,
                        ++tableIndex,
                        payTabsProvider);
            }

            buildSpacerRow(spreadsheet, ++rowId);
            rowId++;
        }

        if (gridNames.contains("UnmatchedOnlineCardRecord")) {

            map.put("gridName", "UnmatchedOnlineCardRecord");
            AccountingPage unmatchedOnlineCardRecord =
                    searchByFileStatementAndGridName(f, pageable, map);

            buildTableNameRow(workbook, spreadsheet,
                    "Unmatched - Payments in the bank statement but not in erp " +
                            unmatchedOnlineCardRecord.getTotalElements()+ ", Total amount " +
                            unmatchedOnlineCardRecord.getTotalSum(), fmt, rowId);
            buildHeadTableRow(style,
                    spreadsheet,
                    payTabsProvider ?
                            new String[]{"#", "Date Changed to Received", "Payment Amount", "Authorization code", "Tran Ref Number"} :
                            new String[]{"#", "Date Changed to Received", "Payment Amount", "Checkout Payment ID"} ,
                    ++rowId);

            int tableIndex = 0;
            for (Object r : unmatchedOnlineCardRecord.getContent()) {
                addRowForGrid2((Map) r,
                        style,
                        spreadsheet,
                        ++rowId,
                        ++tableIndex,
                        payTabsProvider);
            }

            buildSpacerRow(spreadsheet, ++rowId);
            rowId++;
        }


        if (gridNames.contains("MatchedClientRefunds")) {

            map.put("gridName", "MatchedClientRefunds");
            AccountingPage matchedClientRefunds =
                    searchByFileStatementAndGridName(f, pageable, map);

            buildTableNameRow(workbook, spreadsheet,
                    "Matched Transactions - Client Refunds "
                            +  matchedClientRefunds.getTotalElements() +
                            ", Total amount " + matchedClientRefunds.getTotalSum(), fmt, rowId);
            buildHeadTableRow(style,
                    spreadsheet,
                    payTabsProvider ?
                        new String[]{"#", "Date Changed to PDP", "Payment Amount", "Payment ID",
                                "Tran Ref Number", "Client Name", "Contract ID"} :
                        new String[]{"#", "Date Changed to PDP", "Payment Amount", "Payment ID",
                                "Checkout Payment ID", "Client Name", "Contract ID"} ,
                    ++rowId);

            int tableIndex = 0;
            for (Object r : matchedClientRefunds.getContent()) {
                addRowForGrid3((Map) r,
                        style,
                        spreadsheet,
                        ++rowId,
                        ++tableIndex);
            }

            buildSpacerRow(spreadsheet, ++rowId);
            rowId++;
        }

        if (gridNames.contains("UnmatchedRefundOnlineCardRecord")) {

            map.put("gridName", "UnmatchedRefundOnlineCardRecord");
            AccountingPage unmatchedRefundOnlineCardRecord =
                    searchByFileStatementAndGridName(f, pageable, map);

            buildTableNameRow(workbook, spreadsheet,
                    "Unmatched - Client Refunds in Bank Statement not in ERP "  +
                            unmatchedRefundOnlineCardRecord.getTotalElements() +
                            ", Total amount " + unmatchedRefundOnlineCardRecord.getTotalSum(), fmt, rowId);
            buildHeadTableRow(style,
                    spreadsheet,
                    payTabsProvider ?
                        new String[]{"#", "Date Changed to PDP", "Payment Amount", "Tran Ref Number"} :
                        new String[]{"#", "Date Changed to PDP", "Payment Amount", "Checkout Payment ID"} ,
                    ++rowId);

            int tableIndex = 0;
            for (Object r : unmatchedRefundOnlineCardRecord.getContent()) {
                addRowForGrid4((Map) r,
                        style,
                        spreadsheet,
                        ++rowId,
                        ++tableIndex);
            }

            buildSpacerRow(spreadsheet, ++rowId);
            rowId++;
        }

        if (gridNames.contains("UnmatchedInErpOnly")) {

            map.put("gridName", "UnmatchedInErpOnly");
            AccountingPage unmatchedInErpOnly =
                    searchByFileStatementAndGridName(f, pageable, map);

            buildTableNameRow(workbook, spreadsheet,
                    "Unmatched - Payments and Refunds in the ERP but not in bank statement " +
                            unmatchedInErpOnly.getTotalElements() + ", Total amount " +
                            unmatchedInErpOnly.getTotalSum(), fmt, rowId);
            buildHeadTableRow(style,
                    spreadsheet,
                    payTabsProvider ?
                        new String[]{"#", "Date Changed to Received", "Payment Amount", "Payment type", "Payment ID",
                            "Authorization code", "Tran Ref Number", "Client Name", "Contract ID"} :
                        new String[]{"#", "Date Changed to Received", "Payment Amount", "Payment type", "Payment ID",
                            "Checkout Payment ID", "Client Name", "Contract ID"},
                    ++rowId);

            int tableIndex = 0;
            for (Object r : unmatchedInErpOnly.getContent()) {
                addRowForGrid1AndGrid5((Map) r,
                        style,
                        spreadsheet,
                        ++rowId,
                        ++tableIndex,
                        payTabsProvider);
            }

            buildSpacerRow(spreadsheet, ++rowId);
            rowId++;
        }

        File file = Paths.get(System.getProperty("java.io.tmpdir"),new Date().getTime() + "")
                .toFile();
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition",
                "attachment; filename=\"CreditCardStatementResult.xlsx\"");
        try {
            IOUtils.copy(
                    new FileInputStream(file),
                    response.getOutputStream());

            response.getOutputStream()
                    .flush();
        } catch (IOException ex) {
            logger.log(
                    Level.SEVERE,
                    ex.getMessage(),
                    ex);
        }
    }

    private void buildSpacerRow(XSSFSheet spreadsheet, int rowId) {
        XSSFRow headLevelSpacerRow = spreadsheet.createRow(rowId);
        headLevelSpacerRow.createCell(0);
        spreadsheet.addMergedRegion(
                new CellRangeAddress(rowId, rowId, 0, 3));
    }

    private void buildTableNameRow(
            XSSFWorkbook workbook,
            XSSFSheet spreadsheet,
            String name,
            DataFormat fmt,
            int rowId) {

        CellStyle style = createStyle(
                workbook, fmt,
                CellStyle.NO_FILL);

        XSSFRow headLevelRow = spreadsheet.createRow(rowId);
        XSSFCell cell = headLevelRow.createCell(0);
        cell.setCellValue(name);
        cell.setCellStyle(style);
        XSSFCell cell1 = headLevelRow.createCell(1);
        cell1.setCellStyle(style);
        XSSFCell cell2 = headLevelRow.createCell(2);
        cell2.setCellStyle(style);
        XSSFCell cell3 = headLevelRow.createCell(3);
        cell3.setCellStyle(style);
        XSSFCell cell4 = headLevelRow.createCell(4);
        cell4.setCellStyle(style);
        XSSFCell cell5 = headLevelRow.createCell(5);
        cell5.setCellStyle(style);
        spreadsheet.addMergedRegion(
                new CellRangeAddress(rowId, rowId, 0, 5));
    }

    private CellStyle createStyle(
            XSSFWorkbook workbook, DataFormat fmt, Short fillPattern) {

        CellStyle style = workbook.createCellStyle();
        if (fmt != null)
            style.setDataFormat(fmt.getFormat("@"));
        if (fillPattern != null)
            style.setFillPattern(fillPattern);
        return style;
    }

    private void buildHeadTableRow(
            CellStyle style,
            XSSFSheet spreadsheet,
            String[] names,
            int rowId) {


        XSSFRow headLevelRow = spreadsheet.createRow(rowId);
        int i = 0;
        for (String name : names) {
            XSSFCell cell = headLevelRow.createCell(i++);
            cell.setCellValue(name);
            cell.setCellStyle(style);
        }
    }

    private void addRowForGrid1AndGrid5(
            Map r,
            CellStyle style,
            XSSFSheet spreadsheet,
            int rowId,
            int tableIndex,
            boolean payTabsProvider) {

        boolean hasPayments = r.containsKey("payments");
        int columnIndex = 0;

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(columnIndex);
        Cell1.setCellValue(tableIndex);
        Cell1.setCellStyle(style);

        XSSFCell Cell2 = Row.createCell(++columnIndex);
        Cell2.setCellValue(new LocalDate(r.get("paymentDate")).toString("yyyy-MM-dd"));
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(++columnIndex);
        Cell3.setCellValue(hasPayments ? ((Double) (r.get("paymentAmount"))) : 0.0);
        Cell3.setCellStyle(style);

        XSSFCell Cell4 = Row.createCell(++columnIndex);
        Cell4.setCellValue(hasPayments && ((Map)r.get("payments")).containsKey("paymentType") ?
                (String) ((Map) r.get("payments")).get("paymentType") : "");
        Cell4.setCellStyle(style);

        XSSFCell Cell5 = Row.createCell(++columnIndex);
        Cell5.setCellValue(hasPayments && ((Map)r.get("payments")).containsKey("paymentId") ?
                (String) ((Map) r.get("payments")).get("paymentId") : "");
        Cell5.setCellStyle(style);

        if (payTabsProvider) {
            XSSFCell Cell6 = Row.createCell(++columnIndex);
            Cell6.setCellValue((String) r.get("authorizationCode"));
            Cell6.setCellStyle(style);
        }

        XSSFCell Cell7 = Row.createCell(++columnIndex);
        Cell7.setCellValue((String) r.get("transferReference"));
        Cell7.setCellStyle(style);

        XSSFCell Cell8 = Row.createCell(++columnIndex);
        Cell8.setCellValue((String) r.get("clientName"));
        Cell8.setCellStyle(style);

        XSSFCell cell9 = Row.createCell(++columnIndex);
        cell9.setCellValue((Long) r.get("contractId"));
        cell9.setCellStyle(style);

        if (hasPayments && ((Map) r.get("payments")).containsKey("transactionId")) {
            XSSFCell cell10 = Row.createCell(++columnIndex);
            cell10.setCellValue((String) ((Map) r.get("payments")).get("transactionId"));
            cell10.setCellStyle(style);
        }
    }

    private void addRowForGrid2(
            Map r,
            CellStyle style,
            XSSFSheet spreadsheet,
            int rowId,
            int tableIndex,
            boolean payTabsProvider) {

        int columnIndex = 0;

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(columnIndex);
        Cell1.setCellValue(tableIndex);
        Cell1.setCellStyle(style);

        XSSFCell Cell2 = Row.createCell(++columnIndex);
        Cell2.setCellValue(new LocalDate(r.get("paymentDate")).toString("yyyy-MM-dd"));
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(++columnIndex);
        Cell3.setCellValue((Double) r.get("paymentAmount"));
        Cell3.setCellStyle(style);

        if (payTabsProvider) {
            XSSFCell Cell4 = Row.createCell(++columnIndex);
            Cell4.setCellValue((String) r.get("authorizationCode"));
            Cell4.setCellStyle(style);
        }

        XSSFCell cell5 = Row.createCell(++columnIndex);
        cell5.setCellValue((String) r.get("transferReference"));
        cell5.setCellStyle(style);
    }

    private void addRowForGrid3(
            Map r,
            CellStyle style,
            XSSFSheet spreadsheet,
            int rowId,
            int tableIndex) {

        boolean hasPayments = r.containsKey("payments");
        int columnIndex = 0;

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell cell1 = Row.createCell(columnIndex);
        cell1.setCellValue(tableIndex);
        cell1.setCellStyle(style);

        XSSFCell cell2 = Row.createCell(++columnIndex);
        cell2.setCellValue(new LocalDate(r.get("paymentDate")).toString("yyyy-MM-dd"));
        cell2.setCellStyle(style);

        XSSFCell cell3 = Row.createCell(++columnIndex);
        cell3.setCellValue(hasPayments ? ((Double) (r.get("paymentAmount"))) : 0.0);
        cell3.setCellStyle(style);

        XSSFCell cell4 = Row.createCell(++columnIndex);
        cell4.setCellValue(hasPayments && ((Map)r.get("payments")).containsKey("paymentId") ?
                (String) ((Map) r.get("payments")).get("paymentId") : "");
        cell4.setCellStyle(style);

        XSSFCell cell5 = Row.createCell(++columnIndex);
        cell5.setCellValue((String) r.get("transferReference"));
        cell5.setCellStyle(style);

        XSSFCell cell6 = Row.createCell(++columnIndex);
        cell6.setCellValue((String) r.get("clientName"));
        cell6.setCellStyle(style);

        XSSFCell cell7 = Row.createCell(++columnIndex);
        cell7.setCellValue((Long) r.get("contractId"));
        cell7.setCellStyle(style);
    }

    private void addRowForGrid4(
            Map r,
            CellStyle style,
            XSSFSheet spreadsheet,
            int rowId,
            int tableIndex) {

        int columnIndex = 0;

        XSSFRow Row = spreadsheet.createRow(rowId);
        XSSFCell Cell1 = Row.createCell(columnIndex);
        Cell1.setCellValue(tableIndex);
        Cell1.setCellStyle(style);

        XSSFCell Cell2 = Row.createCell(++columnIndex);
        Cell2.setCellValue(new LocalDate(r.get("paymentDate")).toString("yyyy-MM-dd"));
        Cell2.setCellStyle(style);

        XSSFCell Cell3 = Row.createCell(++columnIndex);
        Cell3.setCellValue((Double) r.get("paymentAmount"));
        Cell3.setCellStyle(style);

        XSSFCell cell4 = Row.createCell(++columnIndex);
        cell4.setCellValue((String) r.get("transferReference"));
        cell4.setCellStyle(style);

    }

    public void createPaytabsPosTransaction(OnlineCardStatementFile file) {
       if (Setup.getRepository(TransactionRepository.class)
               .existsByBusinessObjectIdAndBusinessObjectType(file.getId().toString(), file.getEntityType())) return;

        logger.info("File id: " + file.getId());

        if (Math.abs(file.getCommissionLastRow()) + Math.abs(file.getTransactionFeeSum()) +
                Math.abs(file.getVatOnCommissionLastRow()) + Math.abs(file.getVatOnFeeLastRow()) == 0) return;

        Transaction transaction = new Transaction();
        transaction.setBusinessObjectId(file.getId().toString());
        transaction.setBusinessObjectType(file.getEntityType());

        transaction.setFromBucket(Setup.getRepository(BucketRepository.class)
                .findByCode(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_FROM_BUCKET)));
        transaction.setExpense(Setup.getRepository(ExpenseRepository.class)
                .findOneByCode(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_EXPENSE)));

        StringBuilder description = new StringBuilder("(Paytabs POS Fees)\n");
        // @sum_of_column_p_+_sum_of_column_AH@ AED + 5% VAT
        description.append(Math.round(
                (Math.abs(file.getCommissionSum()) +
                 Math.abs(file.getTransactionFeeSum())) * 100) / 100.0)
                .append(" AED + 5% VAT\n");

        // @Sum_of_column_AI_+_sum_of_column_AJ@ AED “in positive.”
        description.append(Math.round(
                        (Math.abs(file.getVatOnFeeSum()) +
                         Math.abs(file.getVatOnCommissionSum())) * 100) / 100.0)
                .append(" AED\n");

        // Total card payment amount =  @sum_of_the_column_O@ AED “in positive.”
        description.append("Total card payment amount = ")
                .append(Math.round(Math.abs(file.getSalesAmount()) * 100) / 100.0)
                .append(" AED\n");

        // Total received amount = @sum_of_the_column_Q@ AED “in positive.”
        description.append("Total received amount = ")
                .append(Math.round(Math.abs(file.getNetAmount()) * 100) / 100.0)
                .append(" AED\n");

        // related to @the_date_in_the_column_J@
        description.append("related to ").append(new LocalDate(file.getRelatedToDate()).toString("yyyy-MM-dd"));

        transaction.setDescription(description.toString());

        // @the_value_of_the_last_row_of_column_p_ + _sum_of_column_AH_ +_the_value_of_the_last_row_of_column_AJ_ + _the_value_of_the_last_row_of_column_AI@ in positive.
        transaction.setAmount(Math.round(
                (Math.abs(file.getCommissionSum()) +
                 Math.abs(file.getTransactionFeeSum()) +
                 Math.abs(file.getVatOnCommissionSum()) +
                 Math.abs(file.getVatOnFeeSum())) * 100) / 100.0);

        // @the_value_of_the_last_row_of_column_AJ_ + _the_value_of_the_last_row_of_column_AI@ in positive.
        transaction.setVatAmount(Math.round(
                (Math.abs(file.getVatOnCommissionSum()) +
                 Math.abs(file.getVatOnFeeSum())) * 100) / 100.0);

        transaction.setVatType(VatType.IN);
        transaction.setDate(new java.sql.Date(new Date().getTime()));
        transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        transaction.setPaymentType(PaymentMethod.CARD);
        transaction.setCreationTriggeredAutomatically(false);

        ResponseEntity r = Setup.getApplicationContext()
                .getBean(TransactionsController.class)
                .createEntity(transaction);

        if (r.getStatusCode().equals(HttpStatus.OK) && r.getBody() instanceof Transaction) {
            Transaction t = (Transaction) r.getBody();
            file.setPosTransactionId(t.getId());
            onlineCardStatementFileRepository.save(file);
        }
    }

    public void createCheckOutPosTransaction(OnlineCardStatementFile file) {
        if (Setup.getRepository(TransactionRepository.class)
                .existsByBusinessObjectIdAndBusinessObjectType(file.getId().toString(), file.getEntityType())) return;

        logger.info("File id: " + file.getId());

        if (Math.abs(file.getCheckoutGatewayFeesSum()) + Math.abs(file.getCheckoutAcquiringPremiumsSum()) +
                Math.abs(file.getCheckoutSchemeFeesSum()) + Math.abs(file.getCheckoutInterchangeFeeSum()) +
                Math.abs(file.getCheckoutNetworkTokenFee()) + Math.abs(file.getCheckoutVatSum()) == 0) return;

        Transaction transaction = new Transaction();
        transaction.setBusinessObjectId(file.getId().toString());
        transaction.setBusinessObjectType(file.getEntityType());

        transaction.setFromBucket(Setup.getRepository(BucketRepository.class)
                .findByCode(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_FROM_BUCKET)));
        transaction.setExpense(Setup.getRepository(ExpenseRepository.class)
                .findOneByCode(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_EXPENSE)));

        StringBuilder description = new StringBuilder("(CheckOut POS Fees " + file.getCheckoutID() + ")\n");
        // @G + A + S + I + NT@ AED + 5% VAT @V@ AED
        description.append(
                    Math.round(
                       (Math.abs(file.getCheckoutGatewayFeesSum()) + Math.abs(file.getCheckoutAcquiringPremiumsSum()) +
                        Math.abs(file.getCheckoutSchemeFeesSum()) + Math.abs(file.getCheckoutInterchangeFeeSum()) +
                        Math.abs(file.getCheckoutNetworkTokenFee()))
                    * 100) / 100.0)
                .append(" AED + 5% VAT ")
                .append(Math.round(Math.abs(file.getCheckoutVatSum()) * 100) / 100.0).append(" AED\n");

        // Total card payment amount = @P-R@ AED “in positive.”
        description.append("Total card payment amount = ")
                .append(Math.round((Math.abs(file.getCheckoutPaymentsSum()) - Math.abs(file.getCheckoutRefundsSum())) * 100) / 100.0)
                .append(" AED\n");


        // Total received amount = @sum_of_the_column_AF@ AED “in positive.”
        description.append("Total received amount = ")
                .append(Math.round(Math.abs(file.getCheckoutHoldingCurrencyAmount()) * 100) / 100.0)
                .append(" AED\n");

        // related to @Date@ (Date = Column M - without the time)
        description.append("related to ").append(new LocalDate(file.getRelatedToDate()).toString("yyyy-MM-dd"));

        transaction.setDescription(description.toString());

        // @G+A+S+I+NT+V@ in positive.
        transaction.setAmount(
                Math.round(
                   (Math.abs(file.getCheckoutGatewayFeesSum()) +
                    Math.abs(file.getCheckoutAcquiringPremiumsSum()) +
                    Math.abs(file.getCheckoutSchemeFeesSum()) +
                    Math.abs(file.getCheckoutInterchangeFeeSum()) +
                    Math.abs(file.getCheckoutNetworkTokenFee()) +
                    Math.abs(file.getCheckoutVatSum()))
                * 100) / 100.0);

        // @V@ in positive.
        transaction.setVatAmount(Math.round(Math.abs(file.getCheckoutVatSum()) * 100) / 100.0);

        transaction.setVatType(VatType.IN);
        transaction.setDate(new java.sql.Date(new Date().getTime()));
        transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        transaction.setPaymentType(PaymentMethod.CARD);
        transaction.setCreationTriggeredAutomatically(false);

        ResponseEntity r = Setup.getApplicationContext()
                .getBean(TransactionsController.class)
                .createEntity(transaction);

        if (r.getStatusCode().equals(HttpStatus.OK) && r.getBody() instanceof Transaction) {
            Transaction t = (Transaction) r.getBody();
            file.setPosTransactionId(t.getId());
            onlineCardStatementFileRepository.save(file);
        }
    }

    public boolean isFileFinishedParsing(OnlineCardStatementFile file) {
        return onlineCardStatementRecordRepository.existsRecordsCreatedByFile(file.getId()) &&
                !onlineCardStatementRecordRepository.existsConfirmedTodoBGTRunning(
                    "confirmOnlineCardStatementTodo_" + file.getId(),
                    Arrays.asList(
                            BackgroundTaskStatus.Finished,
                            BackgroundTaskStatus.Failed));
    }
}