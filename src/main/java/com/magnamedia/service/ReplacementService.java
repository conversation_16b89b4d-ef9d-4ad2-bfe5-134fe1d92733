package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.workflow.FlowEventConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */

@Service
public class ReplacementService {

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;

    public String validateCreateNewReplacement(
            SwitchingNationalityService.SwitchingNationalityType switchingNationalityType,
            ContractPaymentTerm cpt, boolean fromCcApp) {

        if (!SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE.equals(switchingNationalityType) && !fromCcApp &&
                (Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class)
                        .isPayingViaCreditCard(cpt.getContract()) ||
                        cpt.getContract().isOneMonthAgreement())) {

            return "changeToNewCptOnly"; // ACC-6484 ACC-7094

        }

        if (switchingNationalityType != null &&
                Setup.getApplicationContext().getBean(DirectDebitService.class)
                        .contractHasOpenMainDdcToDo(cpt.getContract().getId())) {

            return "updateCptViaReplacement";
        }

        return "";
    }
}