package com.magnamedia.service;

import com.magnamedia.controller.ExpenseRequestTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.MaidsAtCandidateWA;
import com.magnamedia.entity.dto.ExpenseRequestForMaidByGPTDTO;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpenseRequestType;
import com.magnamedia.repository.ExpenseRepository;
import com.magnamedia.repository.MaidsAtCandidateWARepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> ka<PERSON>
 * created on : 2025-04-08
 */
@Service
public class ExpenseRequestTodoService {
    @Autowired
    private ExpenseRepository expenseRepository;
    @Autowired
    private HousemaidService housemaidService;
    @Autowired
    private CurrencyExchangeSevice currencyExchangeSevice;
    @Autowired
    private MaidsAtCandidateWARepository maidsAtCandidateWARepository;

    public void addExpensesForMaidByGPT(String mobileNumber, String expenseType, ExpenseRequestForMaidByGPTDTO request) {
        Expense expense = expenseRepository.findOneByCode(expenseType);
        if (expense == null) {
            throw new BusinessException("Expense not exists");
        }

        validateExpenseRequestTodo(expense, request);

        ExpenseRequestTodo toDo = prepareExpenseRequestTodo(mobileNumber, expense, request);

        Setup.getApplicationContext()
                .getBean(ExpenseRequestTodoController.class)
                .createEntity(toDo);
    }

    private void validateExpenseRequestTodo(Expense expense, ExpenseRequestForMaidByGPTDTO request) {
        if (request.getRelatedTo() == null || request.getRelatedTo().isEmpty() ||
                !Arrays.asList("MAID", "APPLICANT").contains(request.getRelatedTo()))
            throw new BusinessException("Related to is required and must be MAID or APPLICANT");
        if (request.getAmount() == null || request.getAmount() <= 0.0D)
            throw new BusinessException("Amount is required and must be more than 0");
        if (request.getPaymentMethod() != null && !request.getPaymentMethod().isEmpty()) {
            try {
                ExpensePaymentMethod.valueOf(request.getPaymentMethod());
            } catch (Exception e) {
                throw new BusinessException("Invalid payment method: " + request.getPaymentMethod());
            }
        } else {
            throw new BusinessException("Payment method is required");
        }
        if (request.getAmountCurrency() == null || request.getAmountCurrency().isEmpty()) throw new BusinessException("Amount Currency is required");

        if ("GCC-E".equals(expense.getCode())) {
            if (request.getNotes() == null || request.getNotes().isEmpty()) throw new BusinessException("Notes are required");
            if (request.getSupplier() == null || request.getSupplier().getId() == null) throw new BusinessException("Supplier id is required");
            if (request.getAttachment() == null || request.getAttachment().isEmpty()) throw new BusinessException("Attachment is required");
        }
    }

    private ExpenseRequestTodo prepareExpenseRequestTodo(String mobileNumber, Expense expense, ExpenseRequestForMaidByGPTDTO request) {
        ExpenseRelatedTo.ExpenseRelatedToType relatedToType = ExpenseRelatedTo.ExpenseRelatedToType.valueOf(request.getRelatedTo());

        ExpenseRequestTodo toDo = new ExpenseRequestTodo();
        if (expense.getCode().equals("GCC-E")) {
            toDo.getAttachments().add(Setup.getRepository(AttachementRepository.class).findByUuid(request.getAttachment()));
        }
        toDo.setExpense(expense);
        toDo.setPaymentMethod(ExpensePaymentMethod.valueOf(request.getPaymentMethod()));
        toDo.setExpenseRequestType(ExpenseRequestType.NEW_REQUEST);
        toDo.setAmount(request.getAmount());
        toDo.setCurrency(currencyExchangeSevice.getSpecificCurrency(request.getAmountCurrency()));

        toDo.setBeneficiaryId(expense.getCode().equals("GCC-E") ? request.getSupplier().getId() : null);
        toDo.setBeneficiaryType(expense.getCode().equals("GCC-E") ? ExpenseBeneficiaryType.SUPPLIER : null);
        toDo.setRelatedToId(expense.getCode().equals("GCC-E") ? getRelatedToIdByType(mobileNumber, relatedToType) : null);
        toDo.setRelatedToType(expense.getCode().equals("GCC-E") ? relatedToType : null);
        toDo.setNotes(expense.getCode().equals("GCC-E") ? request.getNotes() : "");

        return toDo;
    }

    private Long getRelatedToIdByType(String mobileNumber, ExpenseRelatedTo.ExpenseRelatedToType relatedToType) {
        Long relatedToId;
        switch (relatedToType) {
            case MAID:
                List<Housemaid> housemaids =  housemaidService.findMaidByMobileNumber(mobileNumber);

                if (housemaids == null || housemaids.isEmpty()) {
                    throw new BusinessException("Maid not found");
                }
                relatedToId = housemaids.get(0).getId();
                break;
                
            case APPLICANT:
                List<MaidsAtCandidateWA> applicants =  maidsAtCandidateWARepository.findByMobileNumberOrWhatsappNumber(mobileNumber, mobileNumber);

                if (applicants == null || applicants.isEmpty()) {
                    throw new BusinessException("Applicant not found");
                }
                relatedToId = applicants.get(0).getId();
                break;
                
            default:
                throw new BusinessException("Unsupported related to type: " + relatedToType);
        }

        if (relatedToId == null) {
            throw new BusinessException(relatedToType.equals(ExpenseRelatedTo.ExpenseRelatedToType.MAID)
                    ? "Maid not found" : "Applicant not found");
        }

        return relatedToId;
    }
}
