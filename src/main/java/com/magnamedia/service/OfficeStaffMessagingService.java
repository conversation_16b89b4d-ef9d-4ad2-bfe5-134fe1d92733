package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.helper.ConcurrentModificationHelper;
import com.magnamedia.module.AccountingModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;


@Service
public class OfficeStaffMessagingService {
    private final Logger logger = Logger.getLogger(OfficeStaffMessagingService.class.getName());

    @Autowired
    private MessagingService messagingService;

    public void sendEmailForTransactionPostingRules(Map<String, String> param) {
        logger.info("payment type : " + param.get("type_of_payment"));
        ConcurrentModificationHelper.unLockGlobalDataKey(param.get("bgtName"));
        messagingService.sendEmailToOfficeStaff("email_for_transaction_posting_rule",
            new HashMap<String, String>() {{
                put("type_of_payment", param.get("type_of_payment"));
                put("method", param.get("method"));
                put("contract_type", param.get("contract_type"));
                put("is_initial", param.get("is_initial"));
                put("payment_id", param.get("payment_id"));
                put("contract_id", param.get("contract_id"));
                put("link_to_client_profile", Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) +
                        "/main.html#!/client/details/" + param.get("client_id"));}},
            Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_TRANSACTION_POSTING_RULE_NOT_FOUND_EMAIL),
            "Create a Transaction Posting Rules for the payment " + param.get("type_of_payment"));
    }
}