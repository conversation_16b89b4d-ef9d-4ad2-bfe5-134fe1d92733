package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.entity.template.TemplateChannelParameter;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.helper.whatsapp.DeliveryStatus;
import com.magnamedia.core.repository.TagRepository;
import com.magnamedia.core.repository.TemplateAllowedParameterRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.repository.TemplateTranslationRepository;
import com.magnamedia.core.repository.template.ChannelSpecificSettingRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.core.type.template.WhatsAppPlatrom;
import com.magnamedia.entity.DDBankMessaging;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.DDMessagingContract;
import com.magnamedia.helper.PicklistHelper;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR> Mahfoud
 */

@Service
public class AccountingTemplateService {
    protected static final Logger logger = Logger.getLogger(AccountingTemplateService.class.getName());

    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private TemplateAllowedParameterRepository templateAllowedParameterRepository;
    @Autowired
    private ChannelSpecificSettingRepository channelSpecificSettingRepository;
    @Autowired
    private QueryService queryService;
    @PersistenceContext
    private EntityManager entityManager;

    // IMS Templates General Rules
    /*
        1. Notification templates:
        We should define the Message Delay correctly and set the Fail Action as SMS (Unless there was a request to use another channel)
        We must add a WA channel for all notification templates
        The WA channel should be a single parameter template with the following details (Unless other configs were requested):
        Single-parameter template
        The content is exactly the same as the SMS
        Fail Action = nothing
        Liveperson First Sentence: from Maids.cc
        Campaign name: Failed Message Alternative
        Outbound Number: MV Sales Line
        Skill: Notifications WhatsApp Bot
        Append trailing sentence must be the same as the SMS
        Translations are the same as the SMS too
        The Fail Action of the SMS is WA with message Delay = "On The Spot"

        2. WhatsApp templates: The Fail Action is SMS and the Message Delay is "On The Spot"

        3. Upon defining a new template or updating it, new parameters must be added to the Allowed Parameters list related to the template
     */

    @Transactional
    public void createOrUpdateNewModelTemplate(String name, String notificationText, String smsText, int priority) {

        createOrUpdateNewModelTemplate(name, notificationText, smsText, priority, new HashMap<>());
    }

    @Transactional
    public void createOrUpdateNewModelTemplate(String name, String notificationText, String smsText, int priority, Map<String, Object> map) {

        logger.info("template name: " + name +
                "; notificationText: " + notificationText +
                "; smsText: " + smsText +
                "; priority: " + priority +
                "; map: " + map.entrySet());
        Template t = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(name);
        if (t == null) {
            logger.info("Not exists before create new one.");
            t = createNewModelTemplate(name, notificationText, smsText, priority, map);
        } else {
            if (!t.isNewModel()) return;
            t = updateNewModelTemplate(t, notificationText, smsText, priority, map);
        }

        // createAlLowedParameterBGT(t);
        copyBasicInfoFromSmsToWhatsApp(t);
    }

    public void createNewModelTemplateAndAddAllowedParameters(String name, String notificationText, String smsText, int priority) {

        createNewModelTemplateAndAddAllowedParameters(name, notificationText, smsText, priority, new HashMap<>());
    }

    public Template createNewModelTemplateAndAddAllowedParameters(String name, String notificationText, String smsText, int priority, Map<String, Object> map) {

        Template t = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(name);
        if (t != null) return t;

        t = createNewModelTemplate(name, notificationText, smsText, priority, map);
        //createAlLowedParameterBGT(t);
        copyBasicInfoFromSmsToWhatsApp(t);

        return t;
    }

    @Transactional
    public Template createNewModelTemplate(String name, String notificationText, String smsText, int priority, Map<String, Object> map) {
        // Check if notificationText is not null or empty before creating notification channel
        ChannelSpecificSetting nChannel = null;
        if (notificationText != null && !notificationText.trim().isEmpty()) {
            nChannel = updateNotificationChannel(new ChannelSpecificSetting(), notificationText, priority);
        }

        ChannelSpecificSetting smsSetting = null, waChannel = null;
        if (smsText != null) {
            smsSetting = updateSmsChannel(new ChannelSpecificSetting(), smsText, map);
            waChannel = updateWhatsAppChannel(new ChannelSpecificSetting(), smsText, map);
        }

        ChannelSpecificSetting robotCallChannel = null;
        if (map.containsKey("isRobotCall")) {
            robotCallChannel = updateRobotCallChannel(
                    new ChannelSpecificSetting(), (int) map.get("maxRetries"),
                    (Long) map.get("retryDelay"), priority);
        }

        if (nChannel != null && map.containsKey("notificationCanClosedByUser")) {
            nChannel.setNotificationCanClosedByUser((Boolean) map.get("notificationCanClosedByUser"));
        }

        if (!map.containsKey("expressionParameters")) {
            Map<String, String> m = getUpdateTemplateMap(notificationText);
            m.putAll(getUpdateTemplateMap(smsText));
            map.put("expressionParameters", m);
        }

        if (map.containsKey("expressionParameters")) {
            Map<String, String> expressionParameters = (Map<String, String>) map.get("expressionParameters");
            if (nChannel != null) { addExpressionParameters(nChannel, expressionParameters); }
            if (smsSetting != null) { addExpressionParameters(smsSetting, expressionParameters); }
            if (waChannel != null) { addExpressionParameters(waChannel, expressionParameters); }
        }

        Template.TemplateBuilder templateBuilder = new Template.TemplateBuilder()
                .Template(name,"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .notificationLocation(NotificationLocation.HOME)
                .target(map.containsKey("target") ?
                        (PicklistItem) map.get("target") :
                        PicklistHelper.getItem("template_target", "Clients"))
                .method(map.containsKey("method") ? (String) map.get("method") : "sms")
                .newModel();

        if (nChannel != null) { templateBuilder.channelSetting(nChannel); }
        if (smsSetting != null) { templateBuilder.channelSetting(smsSetting); }
        if (waChannel != null) { templateBuilder.channelSetting(waChannel); }
        if (robotCallChannel != null) { templateBuilder.channelSetting(robotCallChannel); }

        if (map.containsKey("sendAsEmail") && (boolean) map.get("sendAsEmail")) {
            templateBuilder.emailChannelSetting(notificationText != null ? notificationText : smsText);
        }

        if (map.containsKey("excludeFromWindow")) {
            templateBuilder.excludeFromWindow((boolean) map.get("excludeFromWindow"));
        }

        // maids.cc, maidvisa, both
        if (map.containsKey("contractType")) {
            templateBuilder.domain(PicklistHelper.getItem(
                    "template_contract_type", (String) map.get("contractType")));
        }

        return templateBuilder.build();
    }

    @Transactional
    public Template updateNewModelTemplate(Template t, String notificationText, String smsText, int priority, Map<String, Object> map) {

        if (notificationText != null && !notificationText.isEmpty()) {
            if (t.isChannelExist(ChannelSpecificSettingType.Notification)) {
                t.getChannelSetting(ChannelSpecificSettingType.Notification).setText(notificationText);
                t.getChannelSetting(ChannelSpecificSettingType.Notification).setOnFailAction(ChannelSpecificSettingType.SMS);
                updateMessageDelayType(priority , t.getChannelSetting(ChannelSpecificSettingType.Notification));
            } else {
                t.getChannelSpecificSettings().add(updateNotificationChannel(new ChannelSpecificSetting(), notificationText, priority));
            }

            if (map.containsKey("notificationCanClosedByUser")) {
                t.getChannelSetting(ChannelSpecificSettingType.Notification)
                        .setNotificationCanClosedByUser((Boolean) map.get("notificationCanClosedByUser"));
            }
        }

        if (smsText != null) {
            if (t.isChannelExist(ChannelSpecificSettingType.SMS)) {
                t.getChannelSetting(ChannelSpecificSettingType.SMS).setText(smsText);
            } else {
                t.getChannelSpecificSettings().add(updateSmsChannel(new ChannelSpecificSetting(), smsText, map));
            }

            t.getChannelSetting(ChannelSpecificSettingType.SMS).setOnFailAction(ChannelSpecificSettingType.Whatsapp);

            if (t.isChannelExist(ChannelSpecificSettingType.Whatsapp)) {
                t.getChannelSetting(ChannelSpecificSettingType.Whatsapp).setOnFailAction(null);
                t.getChannelSetting(ChannelSpecificSettingType.Whatsapp).setText(smsText);
            } else {
                ChannelSpecificSetting c = updateWhatsAppChannel(new ChannelSpecificSetting(), smsText, map);
                t.getChannelSpecificSettings().add(c);
            }
        }

        if (map.containsKey("isRobotCall")) {
            if (t.isChannelExist(ChannelSpecificSettingType.RobotCall)) {
                ChannelSpecificSetting robotCallChannel = t.getChannelSetting(ChannelSpecificSettingType.RobotCall);
                robotCallChannel.setMaxRetries((int) map.get("maxRetries"));
                robotCallChannel.setRetryDelay((Long) map.get("retryDelay"));
            } else {
                ChannelSpecificSetting c = updateRobotCallChannel(
                        new ChannelSpecificSetting(), (int) map.get("maxRetries"),
                        (Long) map.get("retryDelay"), priority);
                t.getChannelSpecificSettings().add(c);
            }
        }

        if (!map.containsKey("expressionParameters")) {
            Map<String, String> m = getUpdateTemplateMap(notificationText);
            m.putAll(getUpdateTemplateMap(smsText));
            map.put("expressionParameters", m);
        }

        if (map.containsKey("expressionParameters")) {
            Map<String, String> expressionParameters = (Map<String, String>) map.get("expressionParameters");
            if (notificationText != null) { addExpressionParameters(t.getChannelSetting(ChannelSpecificSettingType.Notification), expressionParameters); }
            if (smsText != null) {
                addExpressionParameters(t.getChannelSetting(ChannelSpecificSettingType.SMS), expressionParameters);
                addExpressionParameters(t.getChannelSetting(ChannelSpecificSettingType.Whatsapp), expressionParameters);
            }
        }

        // t.getAllowedParameters().forEach(a -> templateAllowedParameterRepository.delete(a));
        // t.setAllowedParameters(new HashSet<>());

        t.getChannelSpecificSettings().forEach(setting -> entityManager.detach(setting));
        return TemplateUtil.updateChannelSpecificSettings(t, new HashSet<>(t.getChannelSpecificSettings()));
    }

    public void updateMessageDelayType(int priority, ChannelSpecificSetting channel) {
        updateMessageDelayType(priority, channel, new HashMap<>());
    }

    public void updateMessageDelayType(int priority, ChannelSpecificSetting channel, Map<String, Object> map) {

        if (map.containsKey("priority")) {
            switch ((String) map.get("priority")) {
                case "TS":
                    switch (channel.getType()) {
                        case SMS:
                            channel.setMessageDelayType(Setup.getItem("template_message_delay", "no_sms"));
                            channel.setOnFailAction(null);
                            break;
                        case Whatsapp:
                            channel.setMessageDelayType(Setup.getItem("template_message_delay", "x_hours_after_notification"));
                            channel.setOnFailWhatsappDeliveryStatus(DeliveryStatus.READ);
                            channel.setMessageDelayMinutes(10);
                            channel.setOnFailAction(ChannelSpecificSettingType.SMS);
                            break;
                    }
                    break;
            }
            return;
        }

        if (priority == 0) return;
        switch (priority) {
            case 1:
                channel.setMessageDelayType(PicklistHelper.getItem("template_message_delay", "with_notification"));
                break;
            case 2:
                channel.setMessageDelayType(Setup.getItem("template_message_delay", "x_hours_after_notification"));
                channel.setMessageDelayMinutes(2 * 60);
                break;
            case 3:
                channel.setMessageDelayType(Setup.getItem("template_message_delay", "specific_time"));
                channel.setMessageDelaySpecificDate(Setup.getItem("template_message_delay_specific_date", "@nextMorning@"));
                channel.setMessageDelaySpecificTime(new DateTime().withTime(10, 0 ,0, 0).toDate());
                break;
            case 4:
                channel.setMessageDelayType(Setup.getItem("template_message_delay", "no_sms"));
                break;
        }
    }

    public ChannelSpecificSetting updateWhatsAppChannel(ChannelSpecificSetting waChannel, String smsText, Map<String, Object> map) {

        waChannel.setType(ChannelSpecificSettingType.Whatsapp);
        waChannel.setActive(true);
        waChannel.setText(smsText);
        waChannel.setLivePersonCampaignName("Failed Message Alternative");
        waChannel.setOnFailAction(null);
        waChannel.setLivePersonTemplateFirstSentence(
                PicklistHelper.getItem("live_person_template_first_sentence", "from_maids.cc"));
        waChannel.setOutboundNumber(
                PicklistHelper.getItem("live_person_outbound_numbers", "***********"));
        waChannel.setSkill(
                PicklistHelper.getItem("template_skill", "Notifications WhatsApp Bot"));
        waChannel.setWhatsAppPlatrom(WhatsAppPlatrom.LivePerson);
        waChannel.setMetaBusinessAccount(PicklistHelper.getItemNoException("meta_business_accounts", "****************"));

        updateMessageDelayType(4, waChannel, map);
        return waChannel;
    }

    private ChannelSpecificSetting updateSmsChannel(ChannelSpecificSetting smsChannel, String smsText, Map<String, Object> map) {

        smsChannel.setType(ChannelSpecificSettingType.SMS);
        smsChannel.setActive(true);
        smsChannel.setText(smsText);
        smsChannel.setOnFailAction(ChannelSpecificSettingType.Whatsapp);
        updateMessageDelayType(1, smsChannel, map);
        return smsChannel;
    }

    private ChannelSpecificSetting updateNotificationChannel(ChannelSpecificSetting nChannel, String notificationText, int priority) {

        nChannel.setType(ChannelSpecificSettingType.Notification);
        nChannel.setActive(true);
        nChannel.setText(notificationText);
        nChannel.setNotificationCanClosedByUser(true);
        nChannel.setShowOnHomePage(true);
        nChannel.setOnFailAction(ChannelSpecificSettingType.SMS);
        updateMessageDelayType(priority , nChannel);
        nChannel.setRemoveFromHomePageMinutes(48 * 60);
        return nChannel;
    }

    private ChannelSpecificSetting updateRobotCallChannel(ChannelSpecificSetting robotCallChannel, int maxRetries, Long retryDelay, int priority) {

        robotCallChannel.setType(ChannelSpecificSettingType.RobotCall);
        robotCallChannel.setActive(true);
        robotCallChannel.setMaxRetries(maxRetries);
        robotCallChannel.setRetryDelay(retryDelay);
        updateMessageDelayType(priority, robotCallChannel);

        return robotCallChannel;
    }

    private void addExpressionParameters(ChannelSpecificSetting c, Map<String, String> expressionParameters) {
        if (c == null || !c.isActive()) return;

        Set<TemplateChannelParameter> parameters = new HashSet<>();
        for (String pName : new HashSet<>(TemplateUtil.getParameters(c.getText()))) {
            TemplateChannelParameter tp = new TemplateChannelParameter();
            tp.setName(pName);
            tp.setSetting(c);
            if (expressionParameters.containsKey(pName)) {
                tp.setExpression(true);
                tp.setValue(expressionParameters.get(pName));
            }
            parameters.add(tp);
        }
        c.setParameters(parameters);
    }

    private void copyBasicInfoFromSmsToWhatsApp(Template t) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "copyBasicInfoFromSmsToWhatsApp_" + t.getId() + "_" + new java.util.Date().getTime(),
                        "accounting",
                        "accountingTemplateService",
                        "copyBasicInfoFromSmsToWhatsApp")
                        .withRelatedEntity("Template", t.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {t.getId()})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(5 * 60 * 1000L)
                        .build());
    }

    public void copyBasicInfoFromSmsToWhatsApp(Long templateId) {
        logger.info("Template id: " + templateId);

        Template t = templateRepository.findOne(templateId);
        if (t != null && t.isChannelExist(ChannelSpecificSettingType.SMS) &&
                t.isChannelExist(ChannelSpecificSettingType.Whatsapp)) {

            t.getChannelSetting(ChannelSpecificSettingType.Whatsapp).setTrailingSentence(t.getChannelSetting(ChannelSpecificSettingType.SMS).getTrailingSentence());

            TemplateTranslationRepository templateTranslationRepository = Setup.getRepository(TemplateTranslationRepository.class);
            SelectQuery<TemplateTranslation> q = new SelectQuery<>(TemplateTranslation.class);
            q.filterBy("channelSpecificSetting", "=", t.getChannelSetting(ChannelSpecificSettingType.SMS));
            List<TemplateTranslation> translations = q.execute();
            if (!translations.isEmpty()) {
                translations.forEach(tr -> {
                    TemplateTranslation translation = new TemplateTranslation();
                    translation.setTemplate(t);
                    translation.setLanguage(tr.getLanguage());
                    translation.setTranslation(tr.getTranslation());
                    translation.setChannelSpecificSetting(t.getChannelSetting(ChannelSpecificSettingType.Whatsapp));
                    templateTranslationRepository.save(translation);
                });
            }
            channelSpecificSettingRepository.save(t.getChannelSetting(ChannelSpecificSettingType.Whatsapp));
        }
    }

    private void createAlLowedParameterBGT(Template t) {

        Set<TemplateAllowedParameter> l = templateAllowedParameterRepository.findByTemplate(t);
        if (!l.isEmpty()) {
            l.forEach(a -> templateAllowedParameterRepository.delete(a));
        }

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "createAllowedParametersForTemplate" + new java.util.Date().getTime(),
                        "accounting",
                        "accountingTemplateService",
                        "createAllowedParametersForTemplate")
                        .withRelatedEntity("Template", t.getId())
                        .withParameters(
                                new Class[] {Long.class},
                                new Object[] {t.getId()})
                        .withQueue(BackgroundTaskQueues.SequentialQueue)
                        .withDelay(1 * 60 * 1000L)
                        .build());
    }

    public void createAllowedParametersForTemplate(Long templateId) {
        logger.info("Template id: " + templateId);
        Template t = templateRepository.findOne(templateId);
        if (t == null) return;

        if (t.getChannelSpecificSettings().isEmpty()) {
            logger.info("Template doesn't have channels");
            return;
        }

        addGenderAndWorkerTypeForTemplate(t);

        Set<String> allowedParameters = new HashSet<>();
        allowedParameters.add("her_him");
        allowedParameters.add("her_his");
        allowedParameters.add("she_he");
        allowedParameters.add("She_He_Capitalized");
        allowedParameters.add("worker_type");

        // Add allowed parameters by the flow
        createAllowedParametersFromTheParameters(t, allowedParameters, getParametersByDdMessagingEvent(t));

        for (ChannelSpecificSetting c : t.getChannelSpecificSettings()) {
            createAllowedParametersFromTheParameters(t, allowedParameters, new ArrayList<>(c.getParameters()));
        }
    }

    private void createAllowedParametersFromTheParameters(Template t, Set<String> allowedParameters, List<TemplateChannelParameter> parameters) {
        logger.info("t id: " + t.getId() + "; parameters size: " + parameters.size());

        parameters.forEach(p -> {
                    logger.info("Template channel parameter id: " + p.getId());
                    if (allowedParameters.contains(p.getName())) return;

                    logger.info("Not found before -> create a new one: " + p.getId());
                    TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
                    allowedParameter.setName(p.getName());
                    allowedParameter.setExpression(p.isExpression());
                    allowedParameter.setValue(p.getValue());
                    allowedParameter.setTemplate(t);
                    templateAllowedParameterRepository.save(allowedParameter);

                    allowedParameters.add(p.getName());
                });
    }

    private List<TemplateChannelParameter> getParametersByDdMessagingEvent(Template t) {
        DDMessagingContract d = getDDMessagingContractViaTemplate(t);
        if (d == null) return new ArrayList<>();

        if (d.getClientTemplate() != null && d.getClientTemplate().getId().equals(t.getId())) {
            return queryService.getDdMessagingParametersBasedOnEventAndSubEvent(
                    d.getDdMessaging().getEvent(), d.getDdMessaging().getSubType(), "clientTemplate");
        }

        if (d.getMaidTemplate() != null && d.getMaidTemplate().getId().equals(t.getId())) {
            return queryService.getDdMessagingParametersBasedOnEventAndSubEvent(
                    d.getDdMessaging().getEvent(), d.getDdMessaging().getSubType(), "maidTemplate");
        }

        if (d.getMaidWhenRetractCancellationTemplate() != null && d.getDdMessaging().getMaidWhenRetractCancellationTemplate().getId().equals(t.getId())) {
            return queryService.getDdMessagingParametersBasedOnEventAndSubEvent(
                    d.getDdMessaging().getEvent(), d.getDdMessaging().getSubType(), "maidWhenRetractCancellationTemplate");
        }

        return new ArrayList<>();
    }

    private DDMessagingContract getDDMessagingContractViaTemplate(Template t) {
        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy(new SelectFilter("clientTemplate", "=", t)
                .or("maidTemplate", "=", t)
                .or("maidWhenRetractCancellationTemplate", "=", t));
        q.setLimit(1);

        List<DDMessaging> ddMessagingList = q.execute();

        if (!ddMessagingList.isEmpty()) return ddMessagingList.get(0);

        SelectQuery<DDBankMessaging> ddBank = new SelectQuery<>(DDBankMessaging.class);
        ddBank.filterBy(new SelectFilter("clientTemplate", "=", t)
                .or("maidTemplate", "=", t)
                .or("maidWhenRetractCancellationTemplate", "=", t));
        ddBank.setLimit(1);

        List<DDBankMessaging> ddBankMessagingList = ddBank.execute();

        return ddBankMessagingList.isEmpty() ? null : ddBankMessagingList.get(0);
    }

    public void addGenderAndWorkerTypeForTemplate(Template t){

        generateGenderExpressions(t,"her_him","her","him");
        generateGenderExpressions(t,"her_his","her","his");
        generateGenderExpressions(t,"she_he","she","he");
        generateGenderExpressions(t,"She_He_Capitalized","She","He");
        generateWorkerTypeExpression(t);
    }

    public void generateGenderExpressions(Template template , String name , String v1 , String v2){

        String exp = "#root == null ? " +
                "\"" + v1 + "\" : " +
                "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                    "housemaid == null ? " +
                        "\"" + v1 + "\" : " +
                        "housemaid.getGender() != null ? " +
                            "housemaid.getGender().equals(T(com.magnamedia.extra.Gender).Female) ? " +
                            "\"" + v1 + "\" : " +
                            "\"" + v2 + "\": " +
                        "\"" + v1 + "\": " +
                "\"" + v1 + "\"" ;

        TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
        allowedParameter.setName(name);
        allowedParameter.setExpression(true);
        allowedParameter.setValue(exp);
        allowedParameter.setTemplate(template);

        templateAllowedParameterRepository.save(allowedParameter);
    }

    public void generateWorkerTypeExpression(Template template){

        String exp = "#root == null ? " +
                "\"maid\" : " +
                "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                    "workerType != null ? " +
                        "workerType.getCode().equals(\"private_driver\") ? " +
                            "\"driver\" : " +
                            "\"maid\" : " +
                    "\"maid\" : " +
                "\"maid\"";

        TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
        allowedParameter.setName("worker_type");
        allowedParameter.setExpression(true);
        allowedParameter.setValue(exp);
        allowedParameter.setTemplate(template);

        templateAllowedParameterRepository.save(allowedParameter);
    }

    public void cloneConfigs(Template source, Template target) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "cloneConfigs_" + target.getId() + "_" + new java.util.Date().getTime(),
                        "accounting",
                        "accountingTemplateService",
                        "cloneConfigs")
                        .withRelatedEntity("Template", target.getId())
                        .withParameters(
                                new Class[] {Long.class, Long.class},
                                new Object[] {source.getId(), target.getId()})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    public Template cloneConfigs(Long sourceId, Long targetId) {

        Template target = templateRepository.findOne(targetId);
        Template source = templateRepository.findOne(sourceId);

        return Setup.getApplicationContext()
                .getBean(TemplateUtil.class)
                .cloneConfigs(source, target);
    }

    private Map<String, String> getUpdateTemplateMap(String text) {
        Map<String, String> expressionParameters = new HashMap<>();
        if (text == null) return expressionParameters;

        if (text.contains("@worker_type@")) {
            expressionParameters.put("worker_type", "#root == null ? " +
                    "\"maid\" : " +
                    "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                    "workerType != null ? " +
                    "workerType.getCode().equals(\"private_driver\") ? " +
                    "\"driver\" : " +
                    "\"maid\" : " +
                    "\"maid\" : " +
                    "\"maid\"");
        }

        if (text.contains("@her_him@")) {
            expressionParameters.put("her_him", getGenderExpressions("her_him","her","him"));
        }

        if (text.contains("@her_his@")) {
            expressionParameters.put("her_his", getGenderExpressions("her_his","her","his"));
        }

        if (text.contains("@she_he@")) {
            expressionParameters.put("she_he", getGenderExpressions("she_he","she","he"));
        }

        if (text.contains("@She_He_Capitalized@")) {
            expressionParameters.put("She_He_Capitalized", getGenderExpressions("She_He_Capitalized","She","He"));
        }

        return expressionParameters;
    }

    public String getGenderExpressions(String name , String v1 , String v2){


        return  "#root == null ? " +
                "\"" + v1 + "\" : " +
                "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                "housemaid == null ? " +
                "\"" + v1 + "\" : " +
                "housemaid.getGender() != null ? " +
                "housemaid.getGender().equals(T(com.magnamedia.extra.Gender).Female) ? " +
                "\"" + v1 + "\" : " +
                "\"" + v2 + "\": " +
                "\"" + v1 + "\": " +
                "\"" + v1 + "\"" ;
    }

    public void updateNewModelTemplateForAcc6444(String name, String notificationText, String smsText, int priority) {

        Template t = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(name);
        if (t == null) return;
        if (!t.isNewModel()) return;

        if (t.isChannelExist(ChannelSpecificSettingType.Notification)) {
            t.getChannelSetting(ChannelSpecificSettingType.Notification).setText(notificationText);
            t.getChannelSetting(ChannelSpecificSettingType.Notification).setOnFailAction(ChannelSpecificSettingType.Whatsapp);
        } else {
            ChannelSpecificSetting c = new ChannelSpecificSetting();
            c.setType(ChannelSpecificSettingType.Notification);
            c.setActive(true);
            c.setText(notificationText);
            c.setNotificationCanClosedByUser(true);
            c.setControls(new ArrayList<>());
            c.setShowOnHomePage(true);
            c.setOnFailAction(ChannelSpecificSettingType.Whatsapp);
            t.getChannelSpecificSettings().add(c);
        }

        updateMessageDelayType(priority, t.getChannelSetting(ChannelSpecificSettingType.Notification));


        if (t.isChannelExist(ChannelSpecificSettingType.SMS)) {
            t.getChannelSetting(ChannelSpecificSettingType.SMS).setText(smsText);
        }

        if (t.isChannelExist(ChannelSpecificSettingType.Whatsapp)) {
            t.getChannelSetting(ChannelSpecificSettingType.Whatsapp).setText(smsText);
        } else {
            ChannelSpecificSetting c = new ChannelSpecificSetting();
            c.setType(ChannelSpecificSettingType.Whatsapp);
            c.setActive(true);
            c.setText(smsText);
            t.getChannelSpecificSettings().add(c);
        }
        t.getChannelSetting(ChannelSpecificSettingType.Whatsapp).setOnFailAction(ChannelSpecificSettingType.SMS);

        t.getAllowedParameters().forEach(a -> templateAllowedParameterRepository.delete(a));
        t.setAllowedParameters(new HashSet<>());

        t.setMethod(Setup.getItem("template_methods", "Push Notification"));
        templateRepository.save(t);

        TemplateUtil.updateChannelSpecificSettings(t, t.getChannelSpecificSettings());
        copyBasicInfoFromSmsToWhatsApp(t);
        createAlLowedParameterBGT(t);
    }
}
