package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.AbstractPaymentTypeConfig;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.ContractPaymentTermDetails;
import com.magnamedia.entity.ContractPaymentType;
import com.magnamedia.repository.ContractPaymentTermDetailsRepository;
import com.magnamedia.repository.ContractPaymentTermRepository;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

@Service
public class ContractPaymentTermDetailsService {

    private static final Logger logger = Logger.getLogger(ContractPaymentTermDetailsService.class.getName());

    @Autowired
    private ContractPaymentTermDetailsRepository contractPaymentTermDetailsRepository;

    // ACC-7151
    public void generateCreditNoteForNewCpt(Long cptId) {
        generateCreditNoteForNewCpt(Setup.getRepository(ContractPaymentTermRepository.class)
                .findOne(cptId));
    }

    // ACC-7151
    public void generateCreditNoteForNewCpt(ContractPaymentTerm cpt) {
        logger.info("cpt id: " + (cpt != null ? cpt.getId() : "NULL") +
                "; contract id: " + (cpt != null && cpt.getContract() != null ? cpt.getContract().getId() : "NULL"));

        List<ContractPaymentTermDetails> l = contractPaymentTermDetailsRepository.findByContractPaymentTerm_ContractAndSource(
                cpt.getContract(), ContractPaymentTermDetails.Source.CREDIT_NOTE);

        l.forEach(d -> {
            logger.info("delete details id: " + d.getId());
            d.setDeleted(true);
            contractPaymentTermDetailsRepository.save(d);
        });

        if (cpt.getCreditNoteMonths() == null || cpt.getCreditNoteMonths() <= 0 ||
                cpt.getCreditNote() == null || cpt.getCreditNote() <= 0) {
            return;
        }

        logger.info("start generate credit note details for cpt id: " + cpt.getId());

        ContractPaymentType t = cpt.getContractPaymentTypes()
                .stream().filter(AbstractPaymentTypeConfig::getAffectedByAdditionalDiscount)
                .findFirst().orElse(null);

        int startOn = t != null ?  t.getStartsOn() : 0;
        DateTime contractStartDate = new DateTime(cpt.getContract().getStartOfContract())
                .dayOfMonth().withMinimumValue();

        if (t != null && t.getType().getCode().equals("monthly_payment")) {
            if (cpt.getContract().isOneMonthAgreement()) {
                contractStartDate = new DateTime(cpt.getContract().getStartOfContract());
            } else if (cpt.getContract().getIsProRated() && !cpt.getContract().getProRatedPlusMonth()) {
                startOn = startOn + 1;
            }
        }

        ContractPaymentTermDetails details = new ContractPaymentTermDetails();
        details.setContractPaymentTerm(cpt);
        details.setDetailType(ContractPaymentTermDetails.DetailType.DISCOUNT);
        details.setSource(ContractPaymentTermDetails.Source.CREDIT_NOTE);
        details.setAmount(cpt.getCreditNote());
        details.setDiscountMonths(cpt.getCreditNoteMonths());
        details.setStartDate(java.sql.Date.valueOf(contractStartDate.plusMonths(startOn).toString("yyyy-MM-dd")));
        details.setDetailLevel(ContractPaymentTermDetails.Level.CONTRACT);

        contractPaymentTermDetailsRepository.saveAndFlush(details);
    }

    public List<ContractPaymentTermDetails> getContractPaymentTermDetails(ContractPaymentTerm cpt) {
        if (cpt == null || cpt.getId() == null) return new ArrayList<>();

        return contractPaymentTermDetailsRepository
                .findByDetailLevel(cpt.getContract(), cpt);

    }
}