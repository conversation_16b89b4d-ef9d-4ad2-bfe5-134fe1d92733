package com.magnamedia.service;


import com.magnamedia.repository.AppsServiceDDApprovalTodoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 19/10/2024 - 1:37 PM
 * ACC-7321_ACC-8205
 */
@Service
public class AppsServiceDDApprovalTodoService {
    private static final Logger logger = Logger.getLogger(AppsServiceDDApprovalTodoService.class.getName());

    @Autowired
    private AppsServiceDDApprovalTodoRepository appsServiceDDApprovalTodoRepository;

    public boolean existsClosedDdcWithConfirmation(Long ddcId) {
        boolean isExists = ddcId != null && appsServiceDDApprovalTodoRepository.existsClosedDdcWithConfirmation(ddcId);

        logger.info("ddcId: " + ddcId + " , isExists: " + isExists);

        return isExists;
    }
}