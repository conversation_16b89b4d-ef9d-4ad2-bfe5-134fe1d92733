package com.magnamedia.service;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.*;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.module.type.ContractPaymentTermReason;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DirectDebitRepository;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.logging.Logger;
/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 14, 2021
 */
@Service
public class OecAmendDDsService {
    
    protected static final Logger logger = Logger.getLogger(OecAmendDDsService.class.getName());
    
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    
    @Autowired
    private AccountingEntityPropertyRepository accountingEntityPropertyRepository;
    
    @Autowired
    DirectDebitController directDebitController;
    
    @Autowired
    private DirectDebitRepository ddRepo;
    
    @Autowired
    private ContractRepository contractRepo;
    
    @Autowired
    private ContractPaymentTermController cptCtrl;

    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    
    @Transactional
    public void closeOecAmendDDsFlow(Long contractId){
         closeOecAmendDDsFlow(contractId, false);
    }
    
    @Transactional
    public void closeOecAmendDDsFlow(Long contractId, boolean amended){
        
        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "Continue oec request after amend dds done#" + contractId,
                "OECRequestService",
                "visa",
                "amendDdsProcessDone",
                "Contract",
                contractId,
                true,
                false,
                new Class<?>[]{Long.class, Boolean.class},
                new Object[]{contractId, amended});   
    }
    
    public boolean handleOecFlow(DirectDebit dd){
        if (dd.getCategory().equals(DirectDebitCategory.B)
                && (dd.isAddedByOecFlow() || (dd.getDirectDebitRejectionToDo() != null && dd.getDirectDebitRejectionToDo().isDdAddedByOecFlow()))){
            
            logger.info("After resolve conditions");
            AccountingEntityProperty accountingEntityProperty = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                    Contract.OEC_AMEND_DDS, dd.getContractPaymentTerm().getContract());
            
            if(accountingEntityProperty != null && accountingEntityProperty.isJobRunAndDDPending()){
                logger.info("start handleOecFlow accountingEntityProperty "+accountingEntityProperty.getId()); 
                directDebitController.oecAmendCancelOldDDs(dd.getContractPaymentTerm().getContract());
                logger.info("After calling oecAmendCancelOldDDs");
                accountingEntityPropertyRepository.deleteByKeyAndOrigin(Contract.OEC_AMEND_DDS, dd.getContractPaymentTerm().getContract());
                logger.info("After calling deleteByKeyAndOrigin");
                return true;
            } else if (accountingEntityProperty == null){
                directDebitController.oecAmendCancelOldDDs(dd.getContractPaymentTerm().getContract());
                return true;
            }
        }
        return false;
    }
    
    
    public DirectDebit getPaymentDD(Payment payment , ContractPaymentTerm cpt) {
        logger.info("Start getPaymentDD function paynment id "+ payment.getId() + " cpt id is "+cpt.getId());
        List < DirectDebit> directdebits = ddRepo.findByContractPaymentTermAndCategory(cpt, DirectDebitCategory.A);
        if(directdebits != null && directdebits.size() > 0){
            Optional<DirectDebit> o = directdebits
                    .stream()
                    .filter(dd-> DDUtils.doesDDCoverDate(dd, payment.getDateOfPayment()))
                    .findFirst();
            logger.info("Start getPaymentDD function directdebit is "+o);
            if (o != null && o.isPresent()) return o.get();
        }
        return null;
    }
     
     public List<DirectDebit> getDDImages(DirectDebit directDebit) {
        SelectQuery<DirectDebit> ddImagesQuery = new SelectQuery(DirectDebit.class);
        ddImagesQuery.filterBy("imageForDD.id", "=", directDebit.getId());

        return ddImagesQuery.execute();
    }
     
     
     public void handleReceivingOldPaymentStatus(Payment payment) {
        
        if(payment != null && payment.getDirectDebit() != null){
            logger.info("handleReceivingOldPaymentStatus OEC payment "+payment.getId());
            DirectDebit oldDD = ddRepo.findOne(payment.getDirectDebit().getId());
            ContractPaymentTerm cpt = oldDD.getContractPaymentTerm();

            logger.info("handleReceivingOldPaymentStatus OEC Payment Date: " + payment.getDateOfPayment());
            logger.info("handleReceivingOldPaymentStatus OEC Payment Status: " + payment.getStatus());
            logger.info("handleReceivingOldPaymentStatus OEC Payment Replaced: " + BooleanUtils.toBoolean(payment.getReplaced()));
            logger.info("handleReceivingOldPaymentStatus OEC Old DD Category: " + oldDD.getCategory());
            AccountingEntityProperty accountingEntityProperty = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.OEC_AMEND_DDS, cpt.getContract());
            logger.info("handleReceivingOldPaymentStatus OEC accountingEntityProperty " + accountingEntityProperty);
            if (cpt.getReason() != null && cpt.getReason().equals(ContractPaymentTermReason.OEC_AMEND_DDS) && accountingEntityProperty == null) {
                if (oldDD.getCategory().equals(DirectDebitCategory.B)) {
                    logger.info("handleReceivingOldPaymentStatus OEC in if oldDD.getCategory().equals(DirectDebitCategory.B) ");
                    if (payment.getStatus().equals(PaymentStatus.BOUNCED) && !BooleanUtils.toBoolean(payment.getReplaced())) {
                        logger.info("Payment got Bounced and covers switching month payment -> Generate One Time DD + Cancel old DDB");
                        if (getPaymentDD(payment , cpt) == null) {
                            logger.info("Payment isn't Covered in new DDs");
                            try {
                                addNewOneTimeDD(payment.getContract().getId(), payment.getDateOfPayment(),
                                        payment.getAmountOfPayment(), oldDD , payment);
                            } catch (Exception e) {
                                throw new RuntimeException("exception while creating One Time DD for uncovered Bounced Payment, after Switching Bank Account", e);
                            }
                        }
                    }
                }
            }
        }
    } 
     
    public DirectDebit addNewOneTimeDD(
            Long contractId,
            Date ddStartDate,
            Double amount,
            DirectDebit imageFor,
            Payment bouncedPayment) throws Exception {

        Contract contract = contractRepo.findOne(contractId);
        DirectDebit oneTimeDD = null;
        if(contract != null){
            oneTimeDD = contractPaymentTermServiceNew.addNewDD(contract, ddStartDate, ddStartDate,
                    null, null, null,
                    Math.floor(amount), null, DirectDebitType.ONE_TIME,
                    Setup.getItem("TypeOfPayment", "monthly_payment"),
                    true, bouncedPayment, true, false, true, null, true);

            if (imageFor != null) {
                oneTimeDD.setImageForDD(imageFor);
                ddRepo.save(oneTimeDD);
            }
        }
        return oneTimeDD;
    }
}