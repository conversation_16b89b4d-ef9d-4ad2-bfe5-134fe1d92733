package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.helper.PicklistHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */

@Service
public class AccountingHousemaidTemplateService {
    protected static final Logger logger = Logger.getLogger(AccountingHousemaidTemplateService.class.getName());

    /* template_languages
     * en	English	--
     * ar	Arabic	--
     * hi	Hindi	--
     * tl	Tagalog	--
     * az	Oromo	--
     * am	Amharic	--
     * ml	Malayalam --
     * */

    @Autowired
    private AccountingTemplateService accountingTemplateService;

    public void createNewModelTemplate(String name, String smsText, Map<String, Object> map) {

        Template t = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(name);
        if (t != null) return;

        ChannelSpecificSetting smsSetting = updateSmsChannel(new ChannelSpecificSetting(), smsText);
        ChannelSpecificSetting waChannel = accountingTemplateService.updateWhatsAppChannel(new ChannelSpecificSetting(), smsText, new HashMap<>());

        Template.TemplateBuilder templateBuilder = new Template.TemplateBuilder()
                .Template(name,"", "")
                .showInReceiverLog(true)
                .unicode(true)
                .channelSetting(smsSetting)
                .channelSetting(waChannel)
                .target(PicklistHelper.getItem("template_target", "Housemaids"))
                .method("sms")
                .newModel();

        // maids.cc, maidvisa, both
        if (map.containsKey("contractType")) {
            templateBuilder.domain(PicklistHelper.getItem(
                    "template_contract_type", (String) map.get("contractType")));
        }

        templateBuilder.build();

        if (map.containsKey("translation")) {
            Map<String, String> translation = (Map<String, String>) map.get("translation");
            translation.keySet().forEach(lang -> {
                TemplateUtil.addTranslation(name,
                        ChannelSpecificSettingType.SMS,
                        lang,
                        translation.get(lang),
                        new HashMap<>());

                TemplateUtil.addTranslation(name,
                        ChannelSpecificSettingType.Whatsapp,
                        lang,
                        translation.get(lang),
                        new HashMap<>());
            });
        }
    }

    private ChannelSpecificSetting updateSmsChannel(ChannelSpecificSetting smsChannel, String smsText) {

        smsChannel.setType(ChannelSpecificSettingType.SMS);
        smsChannel.setActive(true);
        smsChannel.setText(smsText);
        smsChannel.setOnFailAction(ChannelSpecificSettingType.Whatsapp);
        accountingTemplateService.updateMessageDelayType(4, smsChannel);
        return smsChannel;
    }
}
