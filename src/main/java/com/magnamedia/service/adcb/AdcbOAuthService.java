package com.magnamedia.service.adcb;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Service for handling ADCB OAuth token authentication
 * 
 * <AUTHOR>
 * Created on Dec 2024
 */
@Service
public class AdcbOAuthService {
    
    private static final Logger logger = Logger.getLogger(AdcbOAuthService.class.getName());
    
    private static final String TOKEN_URL = "https://devmag.adcb.com/auth/oauth/v2/token";
    private static final String GRANT_TYPE = "client_credentials";
    private static final String SCOPE = "CorporateOwnFTBulk CorporateAdhocFTBulk CorporateEnquireFTBulk CorporateAccountStatement DDSMandateManage DDSMandateEnq DDSCollection DDSCollectionEnq DDSFileUpload";
    public static AdcbTokenResponse adcbTokenResponse;
    public static String token;

    @Autowired
    private ObjectMapper objectMapper;

    public AdcbTokenResponse getAccessToken() {
        return adcbTokenResponse;
    }

    /**
     * Get OAuth access token with custom credentials
     *
     * @param clientId The client ID
     * @param clientSecret The client secret
     * @return OAuth token response
     */
    public String getAccessToken(String clientId, String clientSecret) {
        try {
            logger.log(Level.INFO, "Requesting OAuth token from ADCB API with custom credentials");
            
            RestTemplate restTemplate = new RestTemplate();//createRestTemplate();
            
            // Create Basic Auth header
            String credentials = clientId + ":" + clientSecret;
            String encodedCredentials = java.util.Base64.getEncoder().encodeToString(credentials.getBytes());
            
            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("Authorization", "Basic " + encodedCredentials);
            
            // Prepare form data
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("grant_type", GRANT_TYPE);
            formData.add("scope", SCOPE);
            
            // Create request entity
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    TOKEN_URL,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class);

            logger.info("response status : " + response.getStatusCode());
            logger.info("response body : " + response.getBody());

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                logger.log(Level.INFO, "Successfully obtained OAuth token from ADCB API with custom credentials");
                token = (String) response.getBody().get("access_token");
                return token;
            } else {
                logger.log(Level.SEVERE, "Failed to obtain OAuth token. Status: " + response.getStatusCode());
                throw new RuntimeException("Failed to obtain OAuth token from ADCB API");
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error obtaining OAuth token from ADCB API with custom credentials", e);
            throw new RuntimeException("Error obtaining OAuth token from ADCB API", e);
        }
    }
    
    /**
     * Create RestTemplate with proper configuration
     */
    private RestTemplate createRestTemplate() {
        RestTemplate template = new RestTemplate();
        
        // Configure message converters
        List<org.springframework.http.converter.HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        
        // Add JSON converter
        org.springframework.http.converter.json.MappingJackson2HttpMessageConverter jsonConverter = 
            new org.springframework.http.converter.json.MappingJackson2HttpMessageConverter();
        jsonConverter.setObjectMapper(objectMapper);
        messageConverters.add(jsonConverter);
        
        // Add form converter for application/x-www-form-urlencoded
        org.springframework.http.converter.FormHttpMessageConverter formConverter = 
            new org.springframework.http.converter.FormHttpMessageConverter();
        messageConverters.add(formConverter);
        
        // Add string converter
        org.springframework.http.converter.StringHttpMessageConverter stringConverter = 
            new org.springframework.http.converter.StringHttpMessageConverter();
        messageConverters.add(stringConverter);
        
        template.setMessageConverters(messageConverters);
        
        return template;
    }
    
    /**
     * Response DTO for ADCB OAuth token
     */
    public static class AdcbTokenResponse {
        private String access_token;
        private String token_type;
        private int expires_in;
        private String scope;
        
        // Default constructor
        public AdcbTokenResponse() {}
        
        // Constructor with all fields
        public AdcbTokenResponse(String access_token, String token_type, int expires_in, String scope) {
            this.access_token = access_token;
            this.token_type = token_type;
            this.expires_in = expires_in;
            this.scope = scope;
        }
        
        // Getters and Setters
        public String getAccess_token() {
            return access_token;
        }
        
        public void setAccess_token(String access_token) {
            this.access_token = access_token;
        }
        
        public String getToken_type() {
            return token_type;
        }
        
        public void setToken_type(String token_type) {
            this.token_type = token_type;
        }
        
        public int getExpires_in() {
            return expires_in;
        }
        
        public void setExpires_in(int expires_in) {
            this.expires_in = expires_in;
        }
        
        public String getScope() {
            return scope;
        }
        
        public void setScope(String scope) {
            this.scope = scope;
        }
        
        @Override
        public String toString() {
            return "AdcbTokenResponse{" +
                    "access_token='" + (access_token != null ? access_token.substring(0, Math.min(10, access_token.length())) + "..." : "null") + '\'' +
                    ", token_type='" + token_type + '\'' +
                    ", expires_in=" + expires_in +
                    ", scope='" + scope + '\'' +
                    '}';
        }
    }
} 