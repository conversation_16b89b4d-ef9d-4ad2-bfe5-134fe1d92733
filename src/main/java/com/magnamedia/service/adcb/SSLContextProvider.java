package com.magnamedia.service.adcb;

import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import java.io.InputStream;
import java.security.KeyStore;

@Component
public class SSLContextProvider {
    private SSLContext sslContext;

    /*@Value("${adcb.cert.keystore.password}")
    private String password;*/

    public SSLContext getSSLContext(String password) throws Exception {
        if (sslContext != null) return sslContext;

        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        try (InputStream keyInput = getClass().getClassLoader().getResourceAsStream("cert/client-keystore.p12")) {
            keyStore.load(keyInput, password.toCharArray());
        }
        //TODO on production we must use trustStore and load it in loadTrustMaterial in this way.
        // Or we can remove it, in this way the default behaviour will trust only JVM-default trusted CAs
        /*
        File trustStoreFile = new File(getClass().getClassLoader().getResource("client-truststore.p12").toURI());
        SSLContexts.custom()
                .loadKeyMaterial(keyStore, password.toCharArray())
                .loadTrustMaterial(trustStoreFile, password.toCharArray()
                .build()
         */

        sslContext = SSLContexts.custom()
                .loadKeyMaterial(keyStore, password.toCharArray())
                .build();
        return sslContext;
    }
}