package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.extra.ExpenseFlow.ParsedTransportationExpenseAttachmentDto;
import com.magnamedia.extra.ExpenseFlow.TaxiOrderDto;
import com.magnamedia.extra.ExpenseFlow.TransportationExpenseAttachmentRecord;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.type.BillSubStatus;
import com.magnamedia.module.type.BillType;
import com.magnamedia.repository.LogisticsWorkOrderRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class TransportationExpenseAttachmentParser {
    protected static final Logger logger = Logger.getLogger(TransportationExpenseAttachmentParser.class.getName());

    @Autowired
    LogisticsWorkOrderRepository logisticsWorkOrderRepository;

    public List<TransportationExpenseAttachmentRecord> parseAttachment(Attachment attachment , BillType billType){
        List<TransportationExpenseAttachmentRecord> transportationExpenseAttachmentRecords = new ArrayList<>();
        if (billType == null || (!billType.equals(BillType.CAREEM) && !billType.equals(BillType.HALA)))
            throw  new RuntimeException("Bill type should be Hala Or Careem");
        Boolean careem = billType.equals(BillType.CAREEM);
        try {
            DataFormatter formatter = new DataFormatter();
            Workbook workbook = null;
            if (attachment.getExtension().equals("xlsx"))
                workbook = new XSSFWorkbook(Storage.getStream(attachment));
            else if (attachment != null && attachment.getExtension().equals("xls"))
                workbook = new HSSFWorkbook(Storage.getStream(attachment));

            if (workbook == null) return transportationExpenseAttachmentRecords;

            Sheet sheet = workbook.getSheetAt(careem ? 0 : 1);
            Iterator<Row> rowIterator = sheet.iterator();
            NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
            while (rowIterator.hasNext()) {
                try {
                    Row row = rowIterator.next();
                    TransportationExpenseAttachmentRecord transportationExpenseAttachmentRecord = new TransportationExpenseAttachmentRecord();
                    transportationExpenseAttachmentRecord.setBookingId(formatter.formatCellValue(row.getCell(0)));
                    transportationExpenseAttachmentRecord.setPassenger(formatter.formatCellValue(row.getCell(careem ? 19 : 6)));
                    transportationExpenseAttachmentRecord.setDate(formatter.formatCellValue(row.getCell(careem ? 29 : 24)));
                    transportationExpenseAttachmentRecord.setPickupLocation(formatter.formatCellValue(row.getCell(careem? 3 : 9)));
                    transportationExpenseAttachmentRecord.setDropOffLocation(formatter.formatCellValue(row.getCell(careem ? 4: 10)));
                    Double amount = nf_in.parse(formatter.formatCellValue(row.getCell(careem ? 5 : 3))
                        .replace(" ", "").replace("*", "").trim()).doubleValue();
                    Double discount = nf_in.parse(formatter.formatCellValue(row.getCell(careem ? 13 : 4))
                        .replace(" ", "").replace("*", "").trim()).doubleValue();
                    transportationExpenseAttachmentRecord.setBillAmount(billType == BillType.HALA
                        ? (amount - discount) : (amount == 0 ? amount : amount - discount));
                    transportationExpenseAttachmentRecords.add(transportationExpenseAttachmentRecord);
                }  catch (NumberFormatException ex) {
                    logger.log(Level.SEVERE, "Number Parsing Exception: " + ex.getMessage());
                } catch (Exception ex) {
                    logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
                }
            }
        } catch (IOException ex) {
            logger.log(Level.SEVERE, "Exception: " + ex.getMessage());
        }

        return  transportationExpenseAttachmentRecords;
    }

    public ParsedTransportationExpenseAttachmentDto parseAttachmentAndCompareToErp(Attachment attachment , BillType billType , Date from , Date to){
        List<TransportationExpenseAttachmentRecord> allTransportationExpenseAttachmentRecords = parseAttachment(attachment, billType);
        List<LogisticsWorkOrder> allErpTaxiOrders = logisticsWorkOrderRepository
                .findByBillTypeAndLeaveOnBetweenAndBillSubStatusIn(billType, from, to, Arrays.asList(BillSubStatus.BILLED_AND_PICKED_UP, BillSubStatus.BILLED_BUT_CANCELLED));

        List<TransportationExpenseAttachmentRecord> transportationExpenseAttachmentRecords =
                allTransportationExpenseAttachmentRecords.stream().filter(rec -> allErpTaxiOrders.stream()
                        .noneMatch(taxi->taxi.getBookingId() != null
                                && taxi.getBookingId().equals(rec.getBookingId())
                                && taxi.getBillAmount() != null
                                && taxi.getBillAmount().equals(rec.getBillAmount())))
                        .collect(Collectors.toList());
        logger.log(Level.SEVERE, "allTransportationExpenseAttachmentRecords.size: " + allTransportationExpenseAttachmentRecords.size());

        List<TaxiOrderDto> erpTaxiOrders = allErpTaxiOrders.stream().filter(taxi -> allTransportationExpenseAttachmentRecords.stream()
                .noneMatch(rec -> taxi.getBookingId() != null
                        && rec.getBookingId().equals(taxi.getBookingId())
                        && taxi.getBillAmount() != null
                        && taxi.getBillAmount().equals(rec.getBillAmount())))
                .map(taxi -> new TaxiOrderDto(taxi.getBookingId(), taxi.getPassengerName(), taxi.getTaxiWorkOrderPurpose().getName(),
                        taxi.getLeaveOn(), taxi.getBillAmount(), taxi.getAttachments().size() > 0 ? taxi.getAttachments().get(0) : null,
                        taxi.getFromAddress(), taxi.getToAddress()))
                .collect(Collectors.toList());

        return  new ParsedTransportationExpenseAttachmentDto(erpTaxiOrders , transportationExpenseAttachmentRecords);
    }

    public Double getTaxiAmountSum(BillType billType , Date from , Date to){
        List<LogisticsWorkOrder> allErpTaxiOrders = logisticsWorkOrderRepository.
                findByBillTypeAndLeaveOnBetweenAndBillSubStatusIn(
                        billType, from, new LocalDateTime(to)
                                .hourOfDay().withMaximumValue()
                                .minuteOfHour().withMaximumValue()
                                .secondOfMinute().withMaximumValue()
                                .toDate(),
                        Arrays.asList(BillSubStatus.BILLED_AND_PICKED_UP, BillSubStatus.BILLED_BUT_CANCELLED));
        
        if (allErpTaxiOrders == null || allErpTaxiOrders.isEmpty())
            return 0D;
        
        return allErpTaxiOrders.stream().mapToDouble(LogisticsWorkOrder::getBillAmount).sum();
    }

//    public Double getTaxiAmountSum( Date from , Date to){
//        List<LogisticsWorkOrder> allErpTaxiOrders = logisticsWorkOrderRepository.findByLeaveOnBetweenAndBillTypeInAndBillSubStatusIn(from ,to ,Arrays.asList(
//                BillType.HALA, BillType.CAREEM), Arrays.asList(BillSubStatus.BILLED_AND_PICKED_UP, BillSubStatus.BILLED_BUT_CANCELLED));
//        if(allErpTaxiOrders==null || allErpTaxiOrders.isEmpty())
//            return 0D;
//        return allErpTaxiOrders.stream().mapToDouble(LogisticsWorkOrder::getBillAmount).sum();
//    }
}
