package com.magnamedia.report;

import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.helper.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 27, 2020
 *         Jirra ACC-2887
 */
public class PendingForCancellationDDsReport extends BaseReport {
    String[] headers = new String[]{"Application ID", "Account Name", "DD Reference number", "Changed to pending for cancellation Date"};
    Table table;
    private List<PendingForCancellationDDWrapper> pendingForCancellationDDs;

    public PendingForCancellationDDsReport() {
    }

    public PendingForCancellationDDsReport(List<PendingForCancellationDDWrapper> activationRecords) {
        TableTitle tt = new TableTitle("");

        List<Column> columnList = new ArrayList<>();

        String style = "border: 1px solid black  !important; background-color: lightgray !important; text-align: left !important;";
        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header).withBold(true).withStyle(style)));
        }

        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        this.table = new Table(tt, columns).withStyle("width: 100% !important");

        this.pendingForCancellationDDs = activationRecords;
    }

    @Override
    public void build() {

        addSection(table);

        for (PendingForCancellationDDWrapper record : pendingForCancellationDDs) {
            addRow(record);
        }
    }

    public void addRow(PendingForCancellationDDWrapper data) {
        String style = "border: 1px solid black  !important; text-align: left !important;";
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell(data.getApplicationId().toString()).withStyle(style);
        res[1] = new Cell(data.getAccountName()).withStyle(style);
        res[2] = new Cell(data.getDdRefNumber()).withStyle(style);
        res[3] = new Cell(DateUtil.formatClientFullDate(data.getStatusChangeDate())).withStyle(style);

        table.addRow(res);
    }

    public static class PendingForCancellationDDWrapper {
        private String applicationId;
        private String accountName;
        private String ddRefNumber;
        private Date statusChangeDate;

        public PendingForCancellationDDWrapper() {
        }

        public PendingForCancellationDDWrapper(DirectDebitFile ddf) {
            this.applicationId = ddf.getApplicationId();
            this.accountName = ddf.getAccountName();
            this.ddRefNumber = ddf.getDdaRefNo();
            this.statusChangeDate = ddf.getStatusChangeDate();
        }

        public String getApplicationId() {
            return applicationId;
        }

        public void setApplicationId(String applicationId) {
            this.applicationId = applicationId;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public String getDdRefNumber() {
            return ddRefNumber;
        }

        public void setDdRefNumber(String ddRefNumber) {
            this.ddRefNumber = ddRefNumber;
        }

        public Date getStatusChangeDate() {
            return statusChangeDate;
        }

        public void setStatusChangeDate(Date statusChangeDate) {
            this.statusChangeDate = statusChangeDate;
        }
    }
}
