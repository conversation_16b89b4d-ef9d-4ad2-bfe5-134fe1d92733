/*
package com.magnamedia.report;

import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.service.MessagingService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

*/
/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 27, 2020
 *         Jirra ACC-2885
 *//*

public class UnsuccessfulDDsCancellationReport extends BaseReport {
    private static final Logger logger = Logger.getLogger(UnsuccessfulDDsCancellationReport.class.getName());
    private static final String prefix = "MMM ";

    String[] headers = new String[]{"Application ID", "Account Name", "Reference number"};
    Table table;
    private List<NAK_DDCancellationRecords> pendingForCancellationDDs;

    public UnsuccessfulDDsCancellationReport() {
    }

    public UnsuccessfulDDsCancellationReport(List<NAK_DDCancellationRecords> activationRecords) {
        TableTitle tt = new TableTitle("");

        List<Column> columnList = new ArrayList<>();

        String style = "border: 1px solid black  !important; background-color: lightgray !important; text-align: left !important;";
        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header).withBold(true).withStyle(style)));
        }

        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        this.table = new Table(tt, columns).withStyle("width: 100% !important");

        this.pendingForCancellationDDs = activationRecords;
    }

    @Override
    public void build() {

        addSection(table);

        for (NAK_DDCancellationRecords record : pendingForCancellationDDs) {
            addRow(record);
        }
    }

    public void addRow(NAK_DDCancellationRecords data) {
        String style = "border: 1px solid black  !important; text-align: left !important;";
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell(data.getApplicationId().toString()).withStyle(style);
        res[1] = new Cell(data.getAccountName()).withStyle(style);
        res[2] = new Cell(data.getDdRefNumber()).withStyle(style);

        table.addRow(res);
    }

    public static void sendMail(List<UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords> toSendDDs) {
        if (toSendDDs == null || toSendDDs.isEmpty()) return;

        logger.info(prefix + "DDs size: " + toSendDDs.size());

        UnsuccessfulDDsCancellationReport report = new UnsuccessfulDDsCancellationReport(toSendDDs);

        Map<String, String> parameters = new HashMap();

        try {
            parameters.put("html_table", report.render());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("unsuccessful_dds_cancellation",
                        parameters, Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_UNSUCCESSFUL_DD_CANCELLATION_REPORT_MAIL),
                        "Unsuccessful DD Cancellation");
    }

    public static class NAK_DDCancellationRecords {
        private String applicationId;
        private String accountName;
        private String ddRefNumber;

        public NAK_DDCancellationRecords() {
        }

        public NAK_DDCancellationRecords(BankDirectDebitCancelationRecord record) {
            this.applicationId = record.getDirectDebitFile().getApplicationId();
            this.accountName = record.getDirectDebitFile().getAccountName();
            this.ddRefNumber = record.getDdaRefNo();
        }

        public String getApplicationId() {
            return applicationId;
        }

        public void setApplicationId(String applicationId) {
            this.applicationId = applicationId;
        }

        public String getAccountName() {
            return accountName;
        }

        public void setAccountName(String accountName) {
            this.accountName = accountName;
        }

        public String getDdRefNumber() {
            return ddRefNumber;
        }

        public void setDdRefNumber(String ddRefNumber) {
            this.ddRefNumber = ddRefNumber;
        }

        @Override
        public boolean equals(Object obj) {
            if (obj == null || !(obj instanceof NAK_DDCancellationRecords)) return false;

            NAK_DDCancellationRecords ob = (NAK_DDCancellationRecords) obj;

            return this.ddRefNumber.equals(ob.ddRefNumber);
        }
    }
}
*/
