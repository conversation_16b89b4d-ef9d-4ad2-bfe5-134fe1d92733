package com.magnamedia.report;

import com.magnamedia.entity.BankDirectDebitActivationRecord;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Nov 11, 2020
 *         Jirra ACC-2777
 */
public class NewDDRejectionReasonsReport extends BaseReport {
    String[] headers = new String[]{"Contract ID", "Application ID (direct debit ID)"};
    Table table;
    private List<BankDirectDebitActivationRecord> activationRecords;

    public NewDDRejectionReasonsReport() {
    }

    public NewDDRejectionReasonsReport(List<BankDirectDebitActivationRecord> activationRecords) {
        TableTitle tt = new TableTitle("");

        List<Column> columnList = new ArrayList<>();

        String style = "border: 1px solid black  !important; background-color: lightgray !important; text-align: left !important;";
        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header).withBold(true).withStyle(style)));
        }

        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        this.table = new Table(tt, columns).withStyle("width: 100% !important");

        this.activationRecords = activationRecords;
    }

    @Override
    public void build() {

        addSection(table);

        for (BankDirectDebitActivationRecord record : activationRecords) {
            addRow(record);
        }
    }

    public void addRow(BankDirectDebitActivationRecord data) {
        if (data.getDirectDebitFile() == null) return;

        String style = "border: 1px solid black  !important; text-align: left !important;";
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell(data.getDirectDebitFile().getDirectDebit().getContractPaymentTerm().getContract().getId().toString()).withStyle(style);
        res[1] = new Cell(data.getContract()).withStyle(style);

        table.addRow(res);
    }
}
