package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.repository.ContractPaymentConfirmationToDoRepository;
import com.magnamedia.service.*;

import java.util.*;
import java.util.logging.Logger;

@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterUpdate},
        fields = { "id", "status"})
public class AccountingPaymentDeletedBR implements BusinessAction<Payment> {
    Logger logger = Logger.getLogger(AccountingPaymentDeletedBR.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.info("start BR for payment id:" + entity.getId());
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Payment> oldPayments = historyQuery.execute();

        if (oldPayments.isEmpty()) return false;

        logger.info("old id: " +  oldPayments.get(0).getId() + "; old status " + oldPayments.get(0).getStatus() + "; new status " + entity.getStatus());

        return oldPayments.get(0).getStatus() != null &&
                !oldPayments.get(0).getStatus().equals(PaymentStatus.DELETED) &&
                entity.getStatus() != null && entity.getStatus().equals(PaymentStatus.DELETED);
    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent even) {
        Map<String, Object> map = new HashMap<>();
        logger.info("start BR for payment id:" + entity.getId() +
                "; isPassStopConfirmationTodo: " + entity.isPassStopConfirmationTodo());
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
        ContractPaymentConfirmationToDoRepository toDoRepository = Setup.getRepository(ContractPaymentConfirmationToDoRepository.class);
        FlowProcessorEntityRepository flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);

        if (!entity.isPassStopConfirmationTodo()) {
            List<ContractPaymentConfirmationToDo> toDos = toDoRepository.getTodosRelatedToPaymentDeleted(entity.getId());
            toDos.forEach(t -> {
                logger.info("todo id: " + t.getId());
                t.setDisabled(true);
                toDoRepository.silentSave(t);

                flowProcessorEntityRepository.findByContractPaymentConfirmationToDo(t)
                        .forEach(f -> {
                            logger.info("flow ID: " + f.getId());
                            f.setStopped(true);
                            f.setCausedTermination(false);
                            f.setStoppedDueContractTerminated(false);
                            flowProcessorEntityRepository.save(f);
                        });
            });
        }

        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext()
                .getBean(SwitchingBankAccountService.class);
        // ACC-7572
        if (entity.getDirectDebit() != null &&
                entity.getDirectDebit().getCategory().equals(DirectDebitCategory.B) &&
                switchingBankAccountService.isClientSwitchingBankAccount(entity)) {

            Payment paymentImage = switchingBankAccountService.getPaymentImage(entity);
            logger.info("DD id: " +  entity.getDirectDebit().getId());

            if (paymentImage != null && paymentImage.getSentToBankByMDD() &&
                    paymentImage.getDirectDebit().getCategory().equals(DirectDebitCategory.A)) {

                paymentImage.setSentToBankByMDD(false);
                paymentService.forceUpdatePayment(paymentImage);

                // Cancel DDB
                switchingBankAccountService.cancelDDOrShowHiddenCancellationToDos(entity.getDirectDebit());
            }
        }

        if (entity.getRecurring()) {
            List<Map<String, Object>> l = toDoRepository.findFlowsRelatedToRecurringPaymentDeleted(entity.getId(), ClientPayingViaCreditCardService.recurringFailureFlows);
            l.forEach(o -> {
                FlowProcessorEntity failureFlow = (FlowProcessorEntity) o.get("flow");
                ContractPaymentConfirmationToDo todo = (ContractPaymentConfirmationToDo) o.get("t");
                logger.info("failure Flow id: " + failureFlow.getId() + "; todo id: " + todo.getId());

                if (!failureFlow.isStopped() && !failureFlow.isCompleted()) {
                    failureFlow.setStopped(true);
                    flowProcessorEntityRepository.silentSave(failureFlow);
                }

                if (!todo.isDisabled()) {
                    todo.setDisabled(true);
                    toDoRepository.silentSave(todo);
                }
            });
        }

        return map;
    }
}