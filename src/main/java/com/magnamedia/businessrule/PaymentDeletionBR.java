package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.MessagingService;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;

import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @Date 5/13/2023
 */

// From CM ACC-6103
@BusinessRule(moduleCode = "", events = {BusinessEvent.BeforeUpdate } , entity = Payment.class,
    fields = {"id","status","typeOfPayment.id","typeOfPayment.code","contract.id","contract.client.id"})
public class PaymentDeletionBR implements BusinessAction<Payment> {

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        Logger logger = Logger.getLogger(PaymentDeletionBR.class.getName());
        logger.info("PaymentDeletionBR validation start");

        if(entity.getTypeOfPayment() == null || entity.getTypeOfPayment().getId() == null)return false;
        if(!Setup.getRepository(ClientRefundTodoRepository.class).existsByRequiredPayment(entity))return false;

        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        Payment old = paymentRepository.findOne(entity.getId());

        if (old == null) return false;
        logger.info("old id: " +  old.getId());
        logger.info("old status " + old.getStatus());
        logger.info("new status " + entity.getStatus());


        if(old.getStatus() != null && Arrays.asList(PaymentStatus.RECEIVED,PaymentStatus.PDC , PaymentStatus.PRE_PDP).contains(old.getStatus())
                && entity.getStatus() != null && entity.getStatus().equals(PaymentStatus.DELETED)){
            sendEmail(entity,old.getStatus());
            return true;
        }
        return false;


    }

    @Override
    public Map<String, Object> execute(Payment entity, BusinessEvent even) {


        return null;
    }

    private void sendEmail(Payment entity , PaymentStatus oldStatus){
        List<ClientRefundToDo> clientRefundToDos = Setup.getRepository(ClientRefundTodoRepository.class).findByConditionalRefundAndRequiredPayment(entity);
        Logger logger = Logger.getLogger(PaymentDeletionBR.class.getName());
        logger.info("has refund todos = " + clientRefundToDos.size());
        if (clientRefundToDos.isEmpty()) return;

        ClientRefundToDo refundToDo = clientRefundToDos.get(0);
        if(refundToDo.getStatus() == null || refundToDo.getStatus().equals(ClientRefundStatus.STOPPED))return ;

        //entity = Setup.getRepository(PaymentRepository.class).findOne(entity.getId());
        Client client = entity.getContract() != null ? entity.getContract().getClient() : null;
        logger.info("client id = " + (client == null ? "-1" : client.getId()));

        String subject = "A Payment Linked to a Conditional Refund was deleted for Client: "
                + (client != null ? Optional.ofNullable(client.getName()).orElse("") : "") ;
        User user = CurrentRequest.getUser();

        Map<String, String> parameters = new HashMap<>();
        parameters.put("payment_id", entity.getId().toString());
        parameters.put("client_name", client != null ? client.getName() : "");
        parameters.put("client_id", client != null ? client.getId().toString() : null);
        parameters.put("contract_id", entity.getContract() != null ? entity.getContract().getId().toString() : "");
        parameters.put("payment_status", oldStatus != null ? oldStatus.getLabel() : "");
        parameters.put("refund_id", clientRefundToDos.get(0).getId().toString());
        parameters.put("refund_status", clientRefundToDos.get(0).getStatus() != null ? clientRefundToDos.get(0).getStatus().getValue() : "");
        parameters.put("deleted_at_time", new DateTime().toString("yyyy-MM-dd HH:mm:ss"));

        String userEmail = "" , userName = "";

        if(user != null){
            userEmail = user.getEmail();
            userName = user.getName();
            if(userName == null || userName.isEmpty())userName = user.getLoginName();
        }

        boolean putEmail = user != null && user.getLoginName() != null && !(user.getLoginName().equalsIgnoreCase("admin") ||user.getLoginName().equalsIgnoreCase("guest") ) && userEmail != null && !userEmail.isEmpty();
        boolean putName = userName != null && !userName.isEmpty();

        String userInfo = "&#x25CB;&nbsp;The user who deleted the payment: ";

        if(!putName && !putEmail)userInfo = "";
        if(putName){
            userInfo +=  userName ;
        }
        if(putEmail){
            if(putName)userInfo+=", ";
            userInfo+= userEmail ;
        }
        if(!userInfo.isEmpty())userInfo+="<br/>";
        parameters.put("user_info",userInfo);


        try {

            /*
            ACC-6103 old code
            List<EmailRecipient> recipients = EmailHelper.getMailRecipients(
                    Setup.getParameter(Setup.getModule("clientmgmt"), "CONDITIONAL_REFUND_PAYMENT_DELETED_RECIPIENT_EMAILS");
            if (recipients.isEmpty()) return;
            MessagesUtils messagesUtils = Setup.getApplicationContext().getBean(MessagesUtils.class);
            MailService mailService = Setup.getApplicationContext().getBean(MailService.class);

            messagesUtils.sendMessageForStaff(MessagesUtils.getTemplate("conditional_refund_payment_deleted"),subject,recipients,parameters);
            //TemplateEmail templateEmail = new TemplateEmail(subject,"conditional_refund_payment_deleted",parameters);
            //mailService.sendEmail(recipients,templateEmail, EmailReceiverType.Office_Staff);*/
            // updated by ACC-6103
            String recipients = Setup.getParameter(Setup.getModule("clientmgmt"), "CONDITIONAL_REFUND_PAYMENT_DELETED_RECIPIENT_EMAILS");
            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff(
                            "conditional_refund_payment_deleted",
                            parameters,
                            recipients,
                            subject);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}