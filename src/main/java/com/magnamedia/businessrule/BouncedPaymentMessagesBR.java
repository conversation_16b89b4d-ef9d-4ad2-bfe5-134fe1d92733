package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.DDMessagingService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.service.SwitchingBankAccountService;
import org.joda.time.LocalDate;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 15, 2020
 *         Jirra ACC-1611
 */
@BusinessRule(entity = Payment.class, events = {BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate},
        fields = {"id", "contract.id", "dateOfPayment", "replacementFor.id", "replaced", "trials", "reminder",
                "methodOfPayment", "typeOfPayment.id", "amountOfPayment", "status",
                "directDebit.id", "directDebit.category", "directDebit.manualDdfFile.id", "msgBrChecked",
                "causedTermination", "contractCancellationReason"})

public class BouncedPaymentMessagesBR implements BusinessAction<Payment> {
    private static final Logger logger = Logger.getLogger(BouncedPaymentMessagesBR.class.getName());

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "validation");

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        if (contract.getStatus() == ContractStatus.CANCELLED ||
                contract.getStatus() == ContractStatus.EXPIRED) return false;

        logger.log(Level.SEVERE, "payment id " + entity.getId() +
                "; new status " + entity.getStatus() +
                "; new replaced : " + entity.getReplaced() +
                "; Transient scheduleTermDate: " + entity.getContractScheduleDateOfTermination()+
                "; scheduleTermDate: " + contract.getScheduledDateOfTermination());

        if (entity.getMsgBrChecked()) {
            logger.log(Level.SEVERE, "MsgBrChecked");
            return false;
        }

        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);
        if (switchingBankAccountService.isClientSwitchingBankAccount(entity)) {
            logger.log(Level.SEVERE, "client is switching bank account");
            return false;
        }

        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        if (bouncingFlowService.isBouncingFlowStopped(entity)) {
            logger.log(Level.SEVERE, "Bouncing Flow is Stopped");
            return false;
        }
        
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        
        List<Payment> oldPayments = historyQuery.execute();
        Payment old = oldPayments.isEmpty() ? null : oldPayments.get(0);

        logger.log(Level.SEVERE, "Payment old status " + (old != null ? old.getStatus() : "") +
                "; old trials " + (old != null ? old.getTrials() : 0) +
                "; new trials " + entity.getTrials() +
                "; old reminder " + (old != null ? old.getReminder() : 0) +
                "; new reminder " + entity.getReminder() +
                "; getMsgBrChecked: " + entity.getMsgBrChecked() +
                "; old replaced : " + (old != null && old.getReplaced() != null ? old.getReplaced() : null));
        
        if (entity.getStatus().equals(PaymentStatus.BOUNCED)) {
            if (old == null || !old.getStatus().equals(PaymentStatus.BOUNCED)) {
                logger.log(Level.SEVERE, "payment bounced");
                return true;
            }
            
            if (old != null
                    && (!old.getTrials().equals(entity.getTrials()) || !old.getReminder().equals(entity.getReminder()))
                    && (entity.getReplaced() == null || !entity.getReplaced())) {
                
                logger.log(Level.SEVERE, "payment trials or reminder changed");
                return true;
            }
        }
        
        return false;
    }

    @Override
    public Map execute(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "execute starts");

        AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());

        boolean paymentHasNoESignature = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Payment.HAS_NO_E_SIGNATURE, entity) != null;
        if (paymentHasNoESignature) {
            accountingEntityPropertyRepository.deleteByKeyAndOrigin(Payment.HAS_NO_E_SIGNATURE, entity);
        }

        DirectDebitMessagingScheduleTermCategory scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.None;
        if (entity.isCausedTermination()) {
            LocalDate localDate = new LocalDate(contract.getScheduledDateOfTermination());
            scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.GToday;

            if (localDate.getYear() == new LocalDate().getYear()
                    && localDate.getDayOfYear() == new LocalDate().getDayOfYear()) {

                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.EToday;
            }
        }

        logger.log(Level.SEVERE, "execute scheduleTermCategory selected: " + scheduleTermCategory);
        logger.log(Level.SEVERE, "execute payment bounced");

        // check if has approved e-signature before
        if (entity.getDirectDebit() != null && entity.getDirectDebit().getManualDdfFile() != null) {
            logger.log(Level.SEVERE, "execute payment with e-sign and manual dd");

            PicklistItem BPReceived = PicklistHelper.getItem(
                    "BouncedPaymentStatus", "has_e-signature_and_manual_dd");

            SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
            query.filterBy("isActive", "=", true);
            query.filterBy("event", "=", DDMessagingType.BouncedPayment);
            query.filterBy("trials", "like", "%" + (entity.getTrials() == -1 ? 0 : entity.getTrials()) + "%");
            query.filterBy("reminders", "like", "%" + entity.getReminder() + "%");
            query.filterBy("bouncedPaymentStatus", "=", BPReceived);
            query.filterBy("contractProspectTypes", "like", "%" + contract.getContractProspectType().getCode() + "%");
            query.setLimit(1);

            DDMessaging ddMessaging = DDMessagingService.getDdMessaging(query, contract, scheduleTermCategory);

            if (ddMessaging != null) {
                logger.log(Level.SEVERE, "execute has_e-signature_and_manual_dd message founded :" + ddMessaging.getId());

                Setup.getApplicationContext().getBean(DDMessagingService.class)
                        .applyDdMessaging(contract, ddMessaging, entity);
            } else {
                logger.log(Level.SEVERE, "execute has_e-signature_and_manual_dd no message founded");
            }

        } else if (entity.getDirectDebit() == null || entity.getDirectDebit().getCategory() == DirectDebitCategory.B) {
            Map<String, Object> signatureType = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class)
                    .getLastSignatureType(contract.getActiveContractPaymentTerm(), false, true);

            if ((Boolean) signatureType.get("useApprovedSignature") && !paymentHasNoESignature) {
                logger.log(Level.SEVERE, "execute payment with e-sign and no manual dd");

                PicklistItem BPReceived = PicklistHelper.getItem(
                        "BouncedPaymentStatus", "has_e-signature_and_no_manual_dd");

                SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
                query.filterBy("isActive", "=", true);
                query.filterBy("event", "=", DDMessagingType.BouncedPayment);
                query.filterBy("trials", "like", "%" + (entity.getTrials() == -1 ? 0 : entity.getTrials()) + "%");
                query.filterBy("reminders", "like", "%" + entity.getReminder() + "%");
                query.filterBy("bouncedPaymentStatus", "=", BPReceived);
                query.filterBy("contractProspectTypes", "like", "%" + contract.getContractProspectType().getCode() + "%");
                query.setLimit(1);

                DDMessaging ddMessaging = DDMessagingService.getDdMessaging(query, contract, scheduleTermCategory);

                if (ddMessaging != null) {
                    logger.info("execute has_e-signature_and_no_manual_dd message founded: " + ddMessaging.getId());

                    Setup.getApplicationContext().getBean(DDMessagingService.class)
                            .applyDdMessaging(contract, ddMessaging, entity);
                } else {
                    logger.info("execute has_e-signature_and_no_manual_dd no message founded");
                }
            } else {
                logger.log(Level.SEVERE, "execute payment with no e-sign");

                PicklistItem BPReceived = PicklistHelper.getItem(
                        "BouncedPaymentStatus", "has_no_e-signature");

                SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
                query.filterBy("isActive", "=", true);
                query.filterBy("event", "=", DDMessagingType.BouncedPayment);
                query.filterBy("trials", "like", "%" + (entity.getTrials() == -1 ? 0 : entity.getTrials()) + "%");
                query.filterBy("reminders", "like", "%" + entity.getReminder() + "%");
                query.filterBy("bouncedPaymentStatus", "=", BPReceived);
                query.filterBy("contractProspectTypes", "like", "%" + contract.getContractProspectType().getCode() + "%");
                query.setLimit(1);

                DDMessaging ddMessaging = DDMessagingService.getDdMessaging(query, contract, scheduleTermCategory);

                if (ddMessaging != null) {
                    logger.log(Level.SEVERE, "execute has_e-signature message found: " + ddMessaging.getId());

                    Setup.getApplicationContext().getBean(DDMessagingService.class)
                            .applyDdMessaging(contract, ddMessaging, entity);
                } else {
                    logger.log(Level.SEVERE, "execute has_e-signature no message found");
                }
            }
        }

        Map<String, Object> map = new HashMap();
        map.put("msgBrChecked", true);
        if(entity.getTrials() == -1) { // trials are set to -1 in DirectDebitFileAcceptedBusinessRule
            map.put("trials", 0);
        }

        logger.log(Level.SEVERE, "execute execution ends");

        return map;
    }
}
