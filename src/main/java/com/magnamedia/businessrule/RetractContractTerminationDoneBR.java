package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.FlowProcessorEntity;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.FlowProcessorEntityRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.DisableAccountingNotificationService;
import com.magnamedia.service.PaymentService;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 23-6-2020
 */

@BusinessRule(moduleCode = "", entity = Contract.class, events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "scheduledDateOfTermination"})
public class RetractContractTerminationDoneBR implements BusinessAction<Contract> {

    private static final Logger logger = Logger.getLogger(RetractContractTerminationDoneBR.class.getName());

    @Override
    public boolean validate(Contract entity, BusinessEvent event) {
        logger.log(Level.INFO, "RetractContractTerminationDoneBR validation");

        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);

        query.filterBy("id", "=", entity.getId());
        query.sortBy("lastModificationDate", false, true);
        query.setLimit(1);

        List<Contract> oldContract = query.execute();
        if(oldContract.isEmpty()) return false;

        Contract old = oldContract.get(0);

        logger.log(Level.INFO, "old: " + old.getScheduledDateOfTermination() +
                "; new: " + entity.getScheduledDateOfTermination());

        return old.getScheduledDateOfTermination() != null && entity.getScheduledDateOfTermination() == null;
    }

    @Override
    public Map execute(Contract entity, BusinessEvent even) {
        // ACC-5214
        Setup.getApplicationContext().getBean(DisableAccountingNotificationService.class)
                .disableOnRetractContractTermination(entity);

        unFlagFlowsMarkedCausedTermination(entity);

        List<DirectDebit> dds = Setup.getRepository(DirectDebitRepository.class)
                .findByContractPaymentTerm_ContractAndDirectDebitRejectionToDoNotNull(entity);

        logger.log(Level.INFO, "dds size: " + dds.size());

        if (dds.isEmpty()) return null;

        Set<Long> affectedTodos = new HashSet<>();
        DirectDebitRejectionFlowService service = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);

        for (int i = 0; i < dds.size(); i++) {
            DirectDebit directDebit = dds.get(i);

            logger.log(Level.INFO, "dd id: " + directDebit.getId());

            DirectDebitRejectionToDo todo = directDebit.getDirectDebitRejectionToDo();

            logger.log(Level.INFO, "todo id: " + todo.getId() +
                    "; completed: " + todo.getCompleted() +
                    "; stopped: " + todo.isStopped());

            if (todo.getCompleted() || todo.isStopped()) {
                if(affectedTodos.contains(todo.getId())) continue;
                if(service.todoExpired(todo)) continue;

                logger.log(Level.INFO, "resuming todo: " + todo.getId());

                todo.setCompleted(false);
                todo.setStopped(false);
                todo.setCausedTermination(false);
                Setup.getRepository(DirectDebitRejectionToDoRepository.class).save(todo);

                affectedTodos.add(todo.getId());
            }
        }
        return null;
    }

    private void unFlagFlowsMarkedCausedTermination(Contract entity) {
        FlowProcessorEntityRepository flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);

        flowProcessorEntityRepository.findByContractAndCausedTermination(entity)
                .forEach(f -> {
                    logger.info("flow id: " + f.getId());
                    f.setCausedTermination(false);
                    flowProcessorEntityRepository.saveAndFlush(f);
          });

        //remove causedForTermination flag on dds
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        List<DirectDebit> dds = directDebitRepository.findByContractPaymentTermAndCausedTerminationTrue(
                entity.getActiveContractPaymentTerm());
        dds.forEach(dd -> {
            dd.setCausedTermination(false);
            directDebitRepository.silentSave(dd);
        });

        // remove causedForTermination flag on payments
        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);
        List<Payment> payments = Setup.getRepository(PaymentRepository.class)
                .findByContractAndCausedTermination(entity, true);
        payments.forEach(p -> {
            p.setCausedTermination(false);
            paymentService.updatePaymentSilent(p);
        });
    }
}
