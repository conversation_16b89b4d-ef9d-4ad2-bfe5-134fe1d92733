package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Jun 02, 2020
 *         Jirra ACC-1633
 */
@BusinessRule(moduleCode = "clientmgmt", entity = Contract.class,
        events = {BusinessEvent.AfterUpdate},
        fields = {"id", "status", "contractProspectType.code", "housemaid.id"})
public class MaidVisaContractCancellationBusinessRule implements BusinessAction<Contract> {

    private static final Logger logger =
            Logger.getLogger(MaidVisaContractCancellationBusinessRule.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Contract entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "MaidVisaContractCancellationBusinessRule Validation.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        Contract old = contractRepository.findOne(entity.getId());

        logger.log(Level.SEVERE, prefix + "ContractProspectType: " + entity.getContractProspectType().getCode());
        logger.log(Level.SEVERE, prefix + "contract status: " + entity.getStatus() + " old: " + old.getStatus());

        return entity.getContractProspectType().getCode().equals(
                PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))
                && ((entity.getStatus().equals(ContractStatus.CANCELLED)
                && !old.getStatus().equals(ContractStatus.CANCELLED))
                || (entity.getStatus().equals(ContractStatus.EXPIRED)
                && !old.getStatus().equals(ContractStatus.EXPIRED)));
    }

    @Override
    public Map<String, Object> execute(Contract entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "MaidVisaContractCancellationBusinessRule execute.");
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        HousemaidRepository housemaidRepository = Setup.getRepository(HousemaidRepository.class);
    
        // ACC-5214
        Setup.getApplicationContext().getBean(DisableAccountingNotificationService.class)
            .handleContractCancelled(entity);

        Housemaid housemaid = null;
        boolean unfitToWork = false;
        // ACC-1633
        if (entity.getHousemaid() != null && entity.getHousemaid().getId() != null) {
            housemaid = housemaidRepository.findOne(entity.getHousemaid().getId());
            if (housemaid != null && housemaid.getFailedMedical() != null && housemaid.getFailedMedical()) {
                unfitToWork = true;
            }
        }

        Contract c = Setup.getRepository(ContractRepository.class).findOne(entity.getId());
        if (housemaid != null && unfitToWork) {
            // ACC-8596
            Setup.getApplicationContext()
                    .getBean(ClientRefundService.class)
                    .checkIfSdrReceivedDueFailedMedical(c);
        }

        Setup.getApplicationContext()
                .getBean(DirectDebitCancellationService.class)
                .maidVisaCancelDds(c);

        return null;
    }
}
