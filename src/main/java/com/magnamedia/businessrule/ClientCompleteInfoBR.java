package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ContractPaymentTermServiceNew;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on May 5, 2020
 *         Jirra SAL-2034
 * 
 */

@BusinessRule(entity = Client.class,
        events = {BusinessEvent.AfterUpdate},
        fields = {"id", "name", "mobileNumber", "email", "normalizedMobileNumber", "normalizedSpouseMobileNumber"})
public class ClientCompleteInfoBR implements BusinessAction<Client> {

    private static final Logger logger = Logger.getLogger(ClientCompleteInfoBR.class.getName());

    @Override
    public boolean validate(Client entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "ClientCompleteInfoBR Validation.");
        ContractPaymentTermHelper contractPaymentTermHelper = Setup.getApplicationContext().getBean(ContractPaymentTermHelper.class);

        // get the active contract
        List<Contract> contracts = Setup.getRepository(ContractRepository.class).findActiveContractByClient(entity);
        if (contracts == null || contracts.isEmpty()) return false;
        Contract contract = contracts.get(0);

        HistorySelectQuery<Client> historyQuery = new HistorySelectQuery(Client.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.filterByChanged("name");
        historyQuery.sortBy("lastModificationDate", false, true);

        List<Client> oldClients = historyQuery.execute();

        boolean nameChanged = oldClients == null || oldClients.isEmpty() || oldClients.get(0).getName() == null || oldClients.get(0).getName().isEmpty();

        HistorySelectQuery<Client> historyQuery2 = new HistorySelectQuery(Client.class);
        historyQuery2.filterBy("id", "=", entity.getId());
        historyQuery2.filterByChanged("normalizedMobileNumber");
        historyQuery2.sortBy("lastModificationDate", false, true);

        List<Client> oldClients2 = historyQuery2.execute();

        boolean mobileNumberChanged = oldClients2 == null || oldClients2.isEmpty() || oldClients2.get(0).getNormalizedMobileNumber() == null ||
                oldClients2.get(0).getNormalizedMobileNumber().isEmpty();

        return ((contract.getIsMaidVisaServiceApplication() && nameChanged) ||
                (contract.getMaidCCServiceApplication() && (nameChanged || mobileNumberChanged))) &&
                contractPaymentTermHelper.isAttachmentInfoCompleted(entity) &&
                contractPaymentTermHelper.isAttachmentInfoCompleted(contract.getHousemaid());
    }

    @Override
    public Map<String, Object> execute(Client entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "ClientCompleteInfoBR execute.");
        Map<String, Object> map = new HashMap();
        ContractPaymentTermServiceNew contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);

        Contract contract = Setup.getRepository(ContractRepository.class).findActiveContractByClient(entity).get(0);
        List<ContractPaymentTerm> cpts = Setup.getRepository(ContractPaymentTermRepository.class)
                .findByContractAndIsActiveOrderByCreationDateDesc(contract, true);
        if (cpts == null || cpts.isEmpty()) return null;
        ContractPaymentTerm cpt = cpts.get(0);

        logger.info("CPT: " + cpt.getId() +
                "; PTC: " + (cpt.getPaymentTermConfig() == null ? "null" : cpt.getPaymentTermConfig().getId()));

        InputStream signatureForReceipt = contractPaymentTermServiceNew.getSignatureForPaymentReceipt(cpt);

        try {
            contractPaymentTermServiceNew.generateAndSavePaymentsReceipt(cpt, entity, null, signatureForReceipt, true);
            contractPaymentTermServiceNew.sendDDFilesToClient(contract, entity);
        } catch (Exception ex) {
            Logger.getLogger(ClientCompleteInfoBR.class.getName()).log(Level.SEVERE, null, ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            StreamsUtil.closeStream(signatureForReceipt);
        }
        return map;
    }
}