package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.entity.Payment;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.PushNotificationHelper;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitMessagingScheduleTermCategory;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DDMessagingRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DDMessagingService;
import com.magnamedia.service.SwitchingBankAccountService;
import org.joda.time.LocalDate;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Apr 15, 2020
 *         Jirra ACC-1611
 */
// Send messages with bounced payment status: bounced_payment_received and bounced_payment_received_-_not_including_worker_salary
@BusinessRule(entity = Payment.class, events = {BusinessEvent.BeforeUpdate},
        fields = {"id", "contract.id", "dateOfPayment", "replacementFor.id", "replaced", "trials", "reminder",
                "methodOfPayment", "typeOfPayment.id", "amountOfPayment", "status", "includeWorkerSalary",
                "directDebit.id", "directDebit.manualDdfFile.id", "msgBr3Checked", "causedTermination"})

public class BouncedPaymentMessagesBR3 implements BusinessAction<Payment> {

    private static final Logger logger = Logger.getLogger(BouncedPaymentMessagesBR3.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 validation");

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());
        if (contract.getStatus() == ContractStatus.CANCELLED || contract.getStatus() == ContractStatus.EXPIRED)
            return false;
        
//        if (contract.getStatus() == ContractStatus.ACTIVE && entity.getDirectDebit() != null && entity.getDirectDebit().getId() != null) {
//            BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
//            boolean futurePDPPaymentsWithSameAmount = bouncingFlowService.isFuturePDPPaymentsWithSameAmount(
//                    entity.getDateOfPayment(), entity.getAmountOfPayment(), entity.getDirectDebit().getId()
//            );
//            if (!futurePDPPaymentsWithSameAmount)
//                return false;
//        }

        logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 payment id " + entity.getId());
        logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 payment new status " + entity.getStatus());
        logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 payment new replaced : " + (entity.getReplaced()));
        logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 execute Transient scheduleTermDate: " + entity.getContractScheduleDateOfTermination());
        logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 execute scheduleTermDate: " + contract.getScheduledDateOfTermination());

        if (entity.getMsgBr3Checked()) {
            return false;
        }

        SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);
        if (switchingBankAccountService.isClientSwitchingBankAccount(entity)) {
            logger.log(Level.SEVERE, "BouncedPaymentMessagesBR3 client is switching bank account");
            return false;
        }

        BouncingFlowService bouncingFlowService = Setup.getApplicationContext().getBean(BouncingFlowService.class);
        if (bouncingFlowService.isBouncingFlowStopped(entity)) {
            logger.log(Level.SEVERE, "Bouncing Flow is Stopped");
            return false;
        }
        
        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", entity.getId());
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        
        List<Payment> oldPayments = historyQuery.execute();
        Payment old = oldPayments.isEmpty() ? null : oldPayments.get(0);
        
        logger.log(Level.SEVERE, "Payment old status " + (old != null ? old.getStatus() : "") +
                "; old trials " + (old != null ? old.getTrials() : 0) +
                "; new trials " + entity.getTrials() +
                "; old reminder " + (old != null ? old.getReminder() : 0) +
                "; new reminder " + entity.getReminder() +
                "; getMsgBr3Checked: " + entity.getMsgBr3Checked() +
                ": old replaced : " + (old != null && old.getReplaced() != null ? old.getReplaced() : null));

        return entity.getStatus().equals(PaymentStatus.BOUNCED) && entity.getReplaced() &&
                (old == null || old.getReplaced() == null || !old.getReplaced());
    }

    @Override
    public Map execute(Payment entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "execute starts");

        DDMessagingRepository ddMessagingRepository = Setup.getRepository(DDMessagingRepository.class);
        PushNotificationHelper pushNotificationHelper = Setup.getApplicationContext().getBean(PushNotificationHelper.class);

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(entity.getContract().getId());

        DirectDebitMessagingScheduleTermCategory scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.None;
        if (entity.isCausedTermination()) {
            LocalDate localDate = new LocalDate(contract.getScheduledDateOfTermination());
            scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.GToday;

            if (localDate.getYear() == new LocalDate().getYear()
                    && localDate.getDayOfYear() == new LocalDate().getDayOfYear()) {
                
                scheduleTermCategory = DirectDebitMessagingScheduleTermCategory.EToday;
            }
        }

        logger.log(Level.SEVERE, "execute scheduleTermCategory selected: " + scheduleTermCategory);
        logger.log(Level.SEVERE, "execute payment received");

        // ACC-8888
        // If pre-collected contracts, Then "always use not-including-worker-salary message"
        // Else "non-pre-collected contracts, use message based on includeWorkerSalary flag"
        PicklistItem BPReceived = PicklistHelper.getItem("BouncedPaymentStatus",
                !ContractService.isPreCollectedSalary(contract) &&
                        entity.getIncludeWorkerSalary() != null &&
                        entity.getIncludeWorkerSalary() ?
                        "bounced_payment_received" :
                        "bounced_payment_received_-_not_including_worker_salary");

        List<DDMessaging> currentBouncedSetup = ddMessagingRepository
                .findByEventAndIsActiveTrueAndBouncedPaymentStatusAndScheduleTermCategory(
                        DDMessagingType.BouncedPayment, BPReceived, scheduleTermCategory);

        for (DDMessaging ddMessaging : currentBouncedSetup) {
            logger.log(Level.SEVERE, "Payment received messages " + ddMessaging.getId());

            if (ddMessaging.getContractProspectTypes().contains(contract.getContractProspectType().getCode())) {
                Setup.getApplicationContext().getBean(DDMessagingService.class)
                        .applyDdMessaging(contract, ddMessaging, entity);
            }
        }

        // acc-2677 close notifications related to this payment
        List<PushNotification> paymentNotifications = pushNotificationHelper.getByOwnerTypeAndId("Payment", entity.getId());

        logger.log(Level.SEVERE, "Found paymentNotifications: " + paymentNotifications.size());
        pushNotificationHelper.stopDisplaying(paymentNotifications);

        Map<String, Object> map = new HashMap();
        map.put("msgBr3Checked", true);
        return map;
    }
}