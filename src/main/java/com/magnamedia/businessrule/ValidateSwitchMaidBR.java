package com.magnamedia.businessrule;

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.TechnicalException;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.ExceptionUtils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.HousemaidRepository;
import com.magnamedia.service.*;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;

import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> Masod <<EMAIL>>
 *         Created on Mar 1, 2020
 *         Jirra ACC-1435
 * 
 */

@BusinessRule(entity = Replacement.class,
        events = {BusinessEvent.AfterCreate},
        fields = {"id", "contract.id", "newHousemaid.id", "keepCurrentDDs", "automatic",
                "addVatDDs", "fromCCApp"})
public class ValidateSwitchMaidBR implements BusinessAction<Replacement> {
    protected static final Logger logger = Logger.getLogger(ValidateSwitchMaidBR.class.getName());

    @Override
    public boolean validate(Replacement entity, BusinessEvent event) {
        return entity.getContract() != null && entity.getContract().getId() != null; // SMM-1718
    }

    @Override
    public Map execute(Replacement replacement, BusinessEvent businessEvent) {
        if(replacement.getNewHousemaid() == null) return null;

        try {
            SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
            AccountingEntityPropertyRepository accountingEntityPropertyRepository = Setup.getRepository(AccountingEntityPropertyRepository.class);

            Long newHousemaidId = replacement.getNewHousemaid() != null ? replacement.getNewHousemaid().getId() : null;
            Contract contract = Setup.getRepository(ContractRepository.class).findOne(replacement.getContract().getId());

            ContractPaymentTerm currentCPT = contract.getActiveContractPaymentTerm();

            Housemaid newHousemaid = newHousemaidId != null ?
                    Setup.getRepository(HousemaidRepository.class).findOne(newHousemaidId) : null;
            DateTime replacementDate = new DateTime();

            SwitchingNationalityService.SwitchingNationalityType switchingNationalityType = newHousemaidId == null ? null :
                    switchingNationalityService.getSwitchingNationalityType(
                            contract, currentCPT.getHousemaid(), newHousemaid);

            //ACC-7313
            AccountingEntityProperty propertyForUnfreezing = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.NO_MAID_AFTER_UNFREEZING, contract);
            if (contract.getFreezingDate() != null || propertyForUnfreezing != null) {
                logger.info("contract frozen changeToNewCptOnly");
                switchingNationalityService.switchNationalityChangeToNewCpt(
                        currentCPT, newHousemaid, false,
                        replacementDate, switchingNationalityType, false);

                if (propertyForUnfreezing != null &&
                        contract.getHousemaid() == null &&
                        replacement.getNewHousemaid() != null){
                    Setup.getApplicationContext()
                            .getBean(ContractService.class)
                            .unFreezingContractProcessBGT(contract);
                }

                return null;
            }
            // ACC-5702
            String v = Setup.getApplicationContext().getBean(ReplacementService.class)
                    .validateCreateNewReplacement(switchingNationalityType, currentCPT, replacement.isFromCCApp());
            switch (v) {
                case "changeToNewCptOnly": // ACC-6216
                    logger.info("changeToNewCptOnly");
                    ContractPaymentTerm newCpt = switchingNationalityService.switchNationalityChangeToNewCpt(
                            currentCPT, newHousemaid, false,
                            replacementDate, switchingNationalityType, false);

                    Setup.getApplicationContext()
                            .getBean(ContractPaymentConfirmationToDoService.class)
                            .disablePayTabLinksBySource(contract, Collections.singletonList(
                                    contract.isPayingViaCreditCard() && !contract.isOneMonthAgreement() ?
                                            ContractPaymentConfirmationToDo.Source.CLIENT_PAYING_VIA_Credit_Card :
                                            contract.isOneMonthAgreement() ?
                                                    ContractPaymentConfirmationToDo.Source.ONE_MONTH_AGREEMENT :
                                                    ContractPaymentConfirmationToDo.Source.AFTER_CASH_FLOW));

                    Setup.getApplicationContext()
                            .getBean(ClientPayingViaCreditCardService.class)
                            .sendEmailForManualSwitchingNationality(currentCPT, newCpt);

                    return null;
                case "updateCptViaReplacement": // ACC-5913
                    logger.info("updateCptViaReplacement");
                    return null;
            }

            // ACC-5902
            if (contract.isOneMonthAgreement() && switchingNationalityType != null) {
                handleOneMonthAgreement(newHousemaid, replacement, currentCPT, switchingNationalityType);
                return null;
            }

            // ACC-5044
            Setup.getApplicationContext()
                    .getBean(ComplaintService.class)
                    .complyWithMOHORE(currentCPT, replacement);

            if(contract.getContractFeesType() == null) return null;

            // ACC-1615
            boolean keepCurrentDDs = BooleanUtils.toBoolean(replacement.getKeepCurrentDDs());

            // ACC-2933
            if (newHousemaid != null && switchingNationalityType.equals(
                    SwitchingNationalityService.SwitchingNationalityType.UPGRADING)) {
                if (Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class)
                        .isPayingViaCreditCard(contract)) { // ACC-5533
                    Setup.getApplicationContext().getBean(ClientPayingViaCreditCardService.class)
                            .handleSwitchNationalityRequest(currentCPT, newHousemaid, replacement,
                                    SwitchingNationalityService.SwitchingNationalityType.UPGRADING);
                    return null;
                }
                logger.info("Switching to a higher grade nationality -> save switching Date and do Nothing");
                AccountingEntityProperty a = accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID, contract);
                if (a != null) {
                    a.setIsDeleted(true);
                    accountingEntityPropertyRepository.save(a);
                }

                if (accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.UPGRADING_NATIONALITY_DATE, contract) == null) {
                    AccountingEntityProperty switchingNationalityToFilipinoDate = new AccountingEntityProperty();
                    switchingNationalityToFilipinoDate.setOrigin(contract);
                    switchingNationalityToFilipinoDate.setKey(Contract.UPGRADING_NATIONALITY_DATE);
                    switchingNationalityToFilipinoDate.setValue(DateUtil.formatDateDashed(replacementDate.toDate()));
                    accountingEntityPropertyRepository.save(switchingNationalityToFilipinoDate);
                }

                return null;
            }

            // ACC-2933
            if (switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING)) {
                if (Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class).isPayingViaCreditCard(contract)) { // ACC-5533
                    Setup.getApplicationContext()
                            .getBean(ClientPayingViaCreditCardService.class)
                            .handleSwitchNationalityRequest(currentCPT, newHousemaid, replacement, SwitchingNationalityService.SwitchingNationalityType.DOWNGRADING);
                    return null;
                }
                AccountingEntityProperty switchingNationalityJobPassedWhileHasNoMaid =
                        accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(
                                Contract.DOWNGRADING_NATIONALITY_JOB_PASSED_WHILE_HAS_NO_MAID,
                        contract);

                if (switchingNationalityJobPassedWhileHasNoMaid == null ||
                        !Boolean.parseBoolean(switchingNationalityJobPassedWhileHasNoMaid.getValue())) {

                    logger.info("Switching to a lower grade nationality -> save switching Date and do Nothing");
                    if (accountingEntityPropertyRepository.findByKeyAndOriginAndDeletedFalse(Contract.DOWNGRADING_NATIONALITY_DATE, contract) == null) {
                        AccountingEntityProperty switchingNationalityFromFilipinoDate = new AccountingEntityProperty();
                        switchingNationalityFromFilipinoDate.setOrigin(contract);
                        switchingNationalityFromFilipinoDate.setKey(Contract.DOWNGRADING_NATIONALITY_DATE);
                        switchingNationalityFromFilipinoDate.setValue(DateUtil.formatDateDashed(replacementDate.toDate()));
                        accountingEntityPropertyRepository.save(switchingNationalityFromFilipinoDate);
                    }

                    return null;
                } else {
                    logger.info("Downgrading Nationality Job Already ran while the contract wasn't Linked to a Maid -> do Switching Directly");
                    logger.info("sending amending DDs email, Contract#" + contract.getId());
                    boolean emailSentSuccessfully = Setup.getApplicationContext().getBean(ContractPaymentTermController.class)
                            .sendSwitchNationalityAmendingDirectDebitFormsEmail(
                                    contract.getId(), newHousemaidId);
                    logger.info("Email Sending Result: " + emailSentSuccessfully);

                    createBgtForSwitching(contract, newHousemaid, replacement.getId(), replacementDate, keepCurrentDDs, replacement.isAddVatDDs());
                    return null;
                }
            }

            //ACC-3941
            if(switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.SAME_GRADE))
                keepCurrentDDs = true;

            handleSwitchingNationalitySameGradeBGT(contract, newHousemaid, replacement, replacementDate, keepCurrentDDs);
        } catch (Throwable e) {
            logger.log(Level.SEVERE, "an error occurred when executing the business rule");
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));

            //Jirra CMA-972
            if (replacement.isFromCCApp()) {
                throw new TechnicalException(e.getMessage());
            } else throw new RuntimeException(e);
        }
        return null;
    }

    private void createBgtForSwitching(Contract contract, Housemaid newHousemaid, Long replacementId, DateTime replacementDate, Boolean keepCurrentDDs, Boolean addVatDDs) {
        Long time = new Date().getTime();

        BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);

        logger.info("Creating BGT to Switch Maid for Contract#" + contract.getId() + ", new Maid#" + newHousemaid.getId());
        backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                "ValidateSwitchMaidBR_SwitchMaidNationality_Contract#" + contract.getId() + "_" + time,
                "switchingNationalityService",
                "accounting",
                "switchMaidAsync",
                "Contract",
                contract.getId(),
                true,
                false,
                new Class<?>[]{Long.class, Long.class, Long.class, Date.class, Boolean.class, Boolean.class, Boolean.class},
                new Object[]{contract.getId(), newHousemaid.getId(), replacementId, replacementDate.toDate(), keepCurrentDDs, false, addVatDDs});
    }

    // ACC-5902
    private void handleOneMonthAgreement(
            Housemaid newHousemaid,
            Replacement replacement,
            ContractPaymentTerm currentCPT,
            SwitchingNationalityService.SwitchingNationalityType switchingNationalityType) throws Exception {

        switch (switchingNationalityType) {
            case UPGRADING:
            case DOWNGRADING:
                Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                        .create(new BackgroundTask.builder(
                                "handleSwitchNationalityViaOMA_" + currentCPT.getContract().getId() + "_" + new java.util.Date().getTime(),
                                "accounting",
                                "oneMonthAgreementFlowService",
                                "handleSwitchNationality")
                                .withRelatedEntity("Replacement", replacement.getId())
                                .withParameters(
                                        new Class[] {Long.class, Long.class, Long.class, Boolean.class},
                                        new Object[] {currentCPT.getId(),
                                                newHousemaid.getId(),
                                                replacement.getId(),
                                                switchingNationalityType.equals(SwitchingNationalityService.SwitchingNationalityType.UPGRADING)})
                                .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                                .build());
                break;
            case SAME_GRADE:
                handleSwitchingNationalitySameGradeBGT(currentCPT.getContract(), newHousemaid, replacement, new DateTime(), true);
                break;
        }
    }

    private void handleSwitchingNationalitySameGradeBGT(
            Contract contract, Housemaid newHousemaid,
            Replacement replacement, DateTime replacementDate,
            boolean keepCurrentDDs) {

        Setup.getApplicationContext()
                .getBean(SwitchingNationalityService.class)
                .validateSwitchMaid(contract, newHousemaid);

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "handleSwitchingNationalitySameGrade_" + contract.getId() + "_" + new java.util.Date().getTime(),
                        "accounting",
                        "switchingNationalityService",
                        "handleSwitchingNationalitySameGrade")
                        .withRelatedEntity("Replacement", replacement.getId())
                        .withParameters(
                                new Class[] {Long.class, Long.class, Long.class, String.class, Boolean.class},
                                new Object[] {contract.getId(), newHousemaid.getId(), replacement.getId(),
                                        replacementDate.toString("yyyy-MM-dd HH:mm:ss"), keepCurrentDDs})
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }
}
