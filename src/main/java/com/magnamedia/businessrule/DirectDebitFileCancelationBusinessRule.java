package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.PaymentService;

import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 13/4/2020
 */

// From CM ACC-6103
@BusinessRule(moduleCode = "", entity = DirectDebitFile.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "ddStatus" , "ddMethod"})
public class DirectDebitFileCancelationBusinessRule implements BusinessAction<DirectDebitFile> {
    private static final Logger logger = Logger.getLogger(DirectDebitFileCancelationBusinessRule.class.getName());

    @Override
    public boolean validate(DirectDebitFile entity, BusinessEvent event) {

        logger.info("validation ddf id: " + (entity.getId() == null ? "NULL" : entity.getId()));
        return entity.getDdStatus().equals(DirectDebitStatus.CANCELED) &&
                (entity.getDdMethod().equals(DirectDebitMethod.AUTOMATIC) ||
                entity.getDdMethod().equals(DirectDebitMethod.MANUAL));
    }

    @Override
    public Map<String, Object> execute(DirectDebitFile entity, BusinessEvent even) {

        logger.info("execute ddf id: " + (entity.getId() == null ? "NULL" : entity.getId()));

        DirectDebitFile directDebitFile = Setup.getRepository(DirectDebitFileRepository.class).findOne(entity.getId());
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        List<Payment> ddfPayments = paymentRepository.findByDirectDebitFileIdAndStatus(directDebitFile.getId(), PaymentStatus.PDC);
        if (ddfPayments.isEmpty()) return null;
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", true);

        PaymentService paymentService = Setup.getApplicationContext().getBean(PaymentService.class);

        Contract contract = null;
        for (Payment p : ddfPayments) {
            if (contract == null) contract = p.getContract();
        }
        paymentService.deleteAllPayments(ddfPayments, true);
        CurrentRequest.addPropertyToCurrentOperation("notFixAdjustedEndDate", false);
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .updatePaidEndDate(contract);
        return null;
    }
}