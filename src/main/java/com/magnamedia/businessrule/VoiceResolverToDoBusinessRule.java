package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.VoiceResolverToDo;
import com.magnamedia.module.type.VoiceResolverToDoReason;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import org.joda.time.LocalDateTime;

import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on July 13, 2020
 * Jirra ACC-2228
 */
@BusinessRule(moduleCode = "", entity = VoiceResolverToDo.class,
        events = {BusinessEvent.AfterUpdate},
        fields = {"id", "completed", "reason"})
public class VoiceResolverToDoBusinessRule implements BusinessAction<VoiceResolverToDo> {

    private static final Logger logger =
            Logger.getLogger(VoiceResolverToDoBusinessRule.class.getName());

    @Override
    public boolean validate(VoiceResolverToDo entity, BusinessEvent event) {

        logger.log(Level.SEVERE, "VoiceResolverToDoBusinessRule validation.");
        logger.log(Level.SEVERE, "VoiceResolverToDoBusinessRule VoiceResolverToDo id: " + entity.getId());
        logger.log(Level.SEVERE, "VoiceResolverToDoBusinessRule VoiceResolverToDo reason: " + entity.getReason());
        logger.log(Level.SEVERE, "VoiceResolverToDoBusinessRule VoiceResolverToDo completed: " + entity.getCompleted());

        HistorySelectQuery<VoiceResolverToDo> historySelectQuery = new HistorySelectQuery(VoiceResolverToDo.class);
        historySelectQuery.filterBy("id", "=", entity.getId());
        historySelectQuery.sortBy("lastModificationDate", false, true);
        historySelectQuery.setLimit(1);
        List<VoiceResolverToDo> oldTransactions = historySelectQuery.execute();

        VoiceResolverToDo old = oldTransactions.get(0);

        return (((old.getCompleted() == null || !old.getCompleted()) && (entity.getCompleted() != null && entity.getCompleted()))
                && entity.getReason() != null && entity.getReason().equals(VoiceResolverToDoReason.REJECTED_DD_FORMS));
    }

    @Override
    public Map<String, Object> execute(VoiceResolverToDo entity, BusinessEvent even) {

        logger.log(Level.SEVERE, "VoiceResolverToDoBusinessRule execute.");

        DirectDebitRejectionToDoRepository repository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);

        SelectQuery<DirectDebitRejectionToDo> query = new SelectQuery<>(DirectDebitRejectionToDo.class);
        query.filterBy("voiceResolverTodoId", "=", entity.getId());
        query.setLimit(1);
        List<DirectDebitRejectionToDo> execute = query.execute();
        if (execute != null && execute.size() > 0) {
            // update last trial date to current date as expert choose client will sign
            DirectDebitRejectionToDo directDebitRejectionToDo = execute.get(0);
            directDebitRejectionToDo.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
            repository.save(directDebitRejectionToDo);

        } else {
            logger.log(Level.SEVERE, "VoiceResolverToDoBusinessRule no DirectDebitRejectionToDo founded with this voiceResolverTodoId");
        }

        return null;
    }
}
