package com.magnamedia.businessrule;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.repository.ContractRepository;
import org.joda.time.DateTime;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 26, 2020
 *         Jirra ACC-2858
 */

@BusinessRule(entity = Contract.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "status", "contractProspectType.code", "isScheduledForTermination", "scheduledDateOfTermination", "client.id"})
public class MaidCCContractScheduleTerminationBR implements BusinessAction<Contract> {

    private static final Logger logger =
            Logger.getLogger(MaidCCContractScheduleTerminationBR.class.getName());
    private static final String prefix = "MMM ";

    @Override
    public boolean validate(Contract entity, BusinessEvent event) {
        logger.log(Level.SEVERE, "MaidCCContractScheduleTerminationBR Validation.");
        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        Contract old = contractRepository.findOne(entity.getId());

        if (old == null) {
            logger.info("Old = NULL -> do nothing");
            return false;
        }


        logger.log(Level.SEVERE, prefix + "ContractProspectType: " + entity.getContractProspectType().getCode());

        Integer eom = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.DDS_CANCELLATION_SCHEDULED_JOB_START_DAY));
        DateTime now = new DateTime();

        return entity.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE)) &&
                entity.getStatus().equals(ContractStatus.ACTIVE) &&
                (now.getDayOfMonth() >= (now.dayOfMonth().withMaximumValue().getDayOfMonth() - eom)) &&
                (old.getIsScheduledForTermination() == null || !old.getIsScheduledForTermination()) &&
                old.getScheduledDateOfTermination() == null;
    }

    @Override
    public Map<String, Object> execute(Contract entity, BusinessEvent even) {
        logger.log(Level.SEVERE, "MaidCCContractScheduleTerminationBR execute.");

        DirectDebitCancelationToDoController directDebitCancelationToDoController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
        directDebitCancelationToDoController.createToDoIfValid(entity, null, DirectDebitCancellationToDoReason.CONTRACT_CANCELLATION);
        return null;
    }
}