package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.BucketReplenishmentTodoStatus;
import com.magnamedia.service.MessagingService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 1/25/2021
 */
@BusinessRule(entity = BucketReplenishmentTodo.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "status", "bucket.id"})
public class BucketReplenishmentsNotificationBusinessRule implements BusinessAction<BucketReplenishmentTodo> {


    @Override
    public boolean validate(BucketReplenishmentTodo bucketReplenishmentTodo, BusinessEvent businessEvent) {
        if (bucketReplenishmentTodo.getStatus() != null && bucketReplenishmentTodo.getStatus().equals(BucketReplenishmentTodoStatus.APPROVED)) {
            List<BucketReplenishmentTodo> result = getRevisions(bucketReplenishmentTodo.getBucket());

            return (result.size() > 2);
        }

        return false;
    }

    @Override
    public Map<String, Object> execute(BucketReplenishmentTodo bucketReplenishmentTodo, BusinessEvent businessEvent) {
        Position position = Setup.getRepository(PositionRepository.class).findByCode(AccountingModule.EXPENSE_AUDIT_MANAGER_USER_POSITION);
        List<User> users = Setup.getRepository(UserRepository.class).findActiveBy(position);

        List<BucketReplenishmentTodo> result = getRevisions(bucketReplenishmentTodo.getBucket());

        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("replenishment_count", String.valueOf(result.size()));
        parameters.put("bucket_name", bucketReplenishmentTodo.getBucket().getName());

        List<Long> excludedUsersIds = Arrays.stream(Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_BUCKET_REPLENISHMENT_ALERT_EXCLUDED_USERS_IDS)
                .split(",")).map(String::trim)
                .filter(id -> !id.isEmpty()).map(Long::parseLong).collect(Collectors.toList());

        MessagingService messagingService = Setup.getApplicationContext()
                .getBean(MessagingService.class);

        for (User user : users) {
            if (!excludedUsersIds.contains(user.getId())) {
                messagingService.sendEmailToOfficeStaff("bucket_replenishment_notification",
                        parameters, user.getEmail(),
                        "Bucket " + bucketReplenishmentTodo.getBucket().getName() + " replenished " + result.size() + " times this week");
            }
        }


        return null;
    }

    private List<BucketReplenishmentTodo> getRevisions(Bucket bucket) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.SUNDAY);
        c.set(Calendar.HOUR_OF_DAY, 0); // ! clear would not reset the hour of day !
        c.clear(Calendar.MINUTE);
        c.clear(Calendar.SECOND);
        c.clear(Calendar.MILLISECOND);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());

        HistorySelectQuery<BucketReplenishmentTodo> historyQuery = new HistorySelectQuery(BucketReplenishmentTodo.class);

        historyQuery.filterByChanged("status");
        historyQuery.filterBy("bucket", "=", bucket);
        historyQuery.filterBy("status", "=", BucketReplenishmentTodoStatus.APPROVED);
        historyQuery.filterBy("lastModificationDate", ">=", c.getTime());

        return historyQuery.execute();
    }
}
