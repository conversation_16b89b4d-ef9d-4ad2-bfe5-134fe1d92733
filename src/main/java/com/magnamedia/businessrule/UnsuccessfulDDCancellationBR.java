//package com.magnamedia.businessrule;
//
//import com.magnamedia.core.Setup;
//import com.magnamedia.core.annotation.BusinessRule;
//import com.magnamedia.core.imc.BusinessAction;
//import com.magnamedia.core.mail.EmailRecipient;
//import com.magnamedia.core.mail.Recipient;
//import com.magnamedia.core.mail.TemplateEmail;
//import com.magnamedia.core.type.BusinessEvent;
//import com.magnamedia.entity.BankDirectDebitCancelationRecord;
//import com.magnamedia.helper.DebugHelper;
//import com.magnamedia.module.AccountingModule;
//import com.magnamedia.report.UnsuccessfulDDsCancellationReport;
//import com.magnamedia.repository.BankDirectDebitCancelationRecordRepository;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.logging.Logger;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> <<EMAIL>>
// *         Created on Jan 27, 2020
// *         Jirra ACC-2885
// */
//@BusinessRule(entity = BankDirectDebitCancelationRecord.class, events = {BusinessEvent.AfterUpdate, BusinessEvent.AfterCreate},
//        fields = {"id", "bankDirectDebitCancelationFile.id"})
//public class UnsuccessfulDDCancellationBR implements BusinessAction<BankDirectDebitCancelationRecord> {
//
//    private static final Logger logger = Logger.getLogger(UnsuccessfulDDCancellationBR.class.getName());
//    private static final String prefix = "MMM ";
//
//
//    @Override
//    public boolean validate(BankDirectDebitCancelationRecord entity, BusinessEvent event) {
//        BankDirectDebitCancelationRecordRepository recordRepo = Setup.getRepository(BankDirectDebitCancelationRecordRepository.class);
//        logger.info(prefix + "Enter Validation");
//        logger.info(prefix + "Record ID: " + entity.getId());
//        logger.info(prefix + "File ID: " + entity.getBankDirectDebitCancelationFile().getId());
//
//        return !recordRepo.existsByCbStatusNotAndBankDirectDebitCancelationFileAndConfirmed(BankDirectDebitCancelationRecord.CB_STATUS_NAK, entity.getBankDirectDebitCancelationFile(),
//                Boolean.FALSE);
//    }
//
//    @Override
//    public Map execute(BankDirectDebitCancelationRecord entity, BusinessEvent event) {
//        BankDirectDebitCancelationRecordRepository recordRepo = Setup.getRepository(BankDirectDebitCancelationRecordRepository.class);
//
//        logger.info(prefix + "Enter Execution");
//
//        List<BankDirectDebitCancelationRecord> records = recordRepo.findByCbStatusAndBankDirectDebitCancelationFile(BankDirectDebitCancelationRecord.CB_STATUS_NAK, entity.getBankDirectDebitCancelationFile());
//        List<UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords> toSendDDs = records.stream()
//                .map(record -> new UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords(record))
//                .collect(Collectors.toList());
//
//        sendMail(toSendDDs);
//
//        return null;
//    }
//
//    private void sendMail(List<UnsuccessfulDDsCancellationReport.NAK_DDCancellationRecords> toSendDDs) {
//        if (toSendDDs == null || toSendDDs.isEmpty()) return;
//
//        logger.info(prefix + "DDs size: " + toSendDDs.size());
//
//        UnsuccessfulDDsCancellationReport report = new UnsuccessfulDDsCancellationReport(toSendDDs);
//
//        String subject = "Unsuccessful DD Cancellation";
//        Map<String, Object> parameters = new HashMap();
//
//        try {
//            parameters.put("html_table", report.render());
//        } catch (Exception e) {
//            DebugHelper.sendExceptionMail("<EMAIL>", e);
//            throw new RuntimeException(e);
//        }
//
//        TemplateEmail templateEmail = new TemplateEmail(subject, "unsuccessful_dds_cancellation", parameters);
//
//        String emails = Setup.getParameter(Setup.getCurrentModule(),
//                AccountingModule.PARAMETER_UNSUCCESSFUL_DD_CANCELLATION_REPORT_MAIL);
//        List<EmailRecipient> recipients = Recipient.parseEmailsString(emails);
//
//        Setup.getMailService().sendEmail(recipients, templateEmail, null);
//    }
//}