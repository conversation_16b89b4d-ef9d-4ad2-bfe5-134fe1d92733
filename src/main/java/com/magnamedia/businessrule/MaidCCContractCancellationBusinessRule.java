package com.magnamedia.businessrule;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BusinessRule;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.BusinessAction;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Contract;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.service.ContractPaymentTermServiceNew;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DisableAccountingNotificationService;
import com.magnamedia.service.MaidCcContractCancellationFlowService;

import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 27, 2019
 *          ACC-1135
 */
@BusinessRule(moduleCode = "clientmgmt", entity = Contract.class, events = {BusinessEvent.AfterUpdate},
        fields = {"id", "status", "contractProspectType.code", "dateOfTermination", "contractFeesType", "specialCase", "expensifyNotes", "reasonOfTerminationList.id", "reasonOfTerminationList.code",
                "scheduledDateOfTermination", "startOfContract", "cancelledWithinFirstXDays"})
public class MaidCCContractCancellationBusinessRule implements BusinessAction<Contract> {

    private static final Logger logger = Logger.getLogger(MaidCCContractCancellationBusinessRule.class.getName());

    @Override
    public boolean validate(Contract entity, BusinessEvent event) {
        logger.log(Level.INFO, "MaidCCContractCancellationBusinessRule Validation.");
        Contract old = Setup.getRepository(ContractRepository.class).findOne(entity.getId());

        logger.log(Level.INFO, "MaidCCContractCancellationBusinessRule Contract ID: " + entity.getId());
        logger.log(Level.INFO, "ContractProspectType: " + entity.getContractProspectType().getCode());
        logger.log(Level.INFO, "contract status: " + entity.getStatus() + " old: " + old.getStatus());
        logger.log(Level.INFO, "contract start of contract: " + entity.getStartOfContract());
        logger.log(Level.INFO, "contract scheduled date of termination: " + entity.getScheduledDateOfTermination());
        logger.log(Level.INFO, "contract date of termination: " + entity.getDateOfTermination());

        return entity.getContractProspectType().getCode().equals(
                PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))
                && !old.getClient().isPayingThroughCheques(old)
                && ((entity.getStatus().equals(ContractStatus.CANCELLED) && !old.getStatus().equals(ContractStatus.CANCELLED))
                || (entity.getStatus().equals(ContractStatus.EXPIRED) && !old.getStatus().equals(ContractStatus.EXPIRED)));
    }

    @Override
    public Map<String, Object> execute(Contract entity, BusinessEvent even) {
        logger.log(Level.INFO, "MaidCCContractCancellationBusinessRule execute.");
        
        // ACC-5214
        Setup.getApplicationContext().getBean(DisableAccountingNotificationService.class)
                .handleContractCancelled(entity);
        ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);

        Map<String, Object> m = Setup.getApplicationContext()
                .getBean(MaidCcContractCancellationFlowService.class)
                .startNewFlow(entity, contractService.getMaidCcCancellationInfo(
                        Setup.getRepository(ContractRepository.class).findOne(entity.getId()),
                        entity.getDateOfTermination(), entity.getScheduledDateOfTermination()));


        logger.log(Level.INFO, "MaidCCContractCancellationBusinessRule execute end.");
        return m;
    }
}