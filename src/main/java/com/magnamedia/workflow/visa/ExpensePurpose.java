package com.magnamedia.workflow.visa;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 17, 2017
 */
public enum ExpensePurpose {
    CREATE_OFFER_LETTER("Offer Letter"),
    MODIFY_OFFER_LETTER("Modification of Offer Letter"),
    WORK_PERMIT("Work Permit"),
    PAY_LABOR_CARD_FEE("Labour Card Fees"),
    ENTRY_VSIA("Entry Visa > 1000 AED"),
    ENTRY_VISA_LESS_THAN_1000("Entry Visa < 1000 AED"),
    IMMIGRATION_CANCELLATION("Immigration Cancellation"),
    CHANGE_OF_STATUS("Change of Status"),
    MEDICAL("Medical"),
    CONTRACT("Contract"),
    EID("E-ID"),
    UPLOAD_TO_TASHEEL("Uploading Contract to Tasheel"),
    APPLY_FOR_RVISA("R-Visa"),
    CANCELLA<PERSON>ON_PAPER("Cancellation Paper"),
    UNUSED_PRE_APPROVAL_WORK_PERMIT_CANCELLATION("Unused Pre-Approval Work Permit Cancellation"),
    CANCELLATION_PRE_APPROVAL_USED_WORK_PERMIT("Cancellation of pre-approval used work permit"),
    ONLINE_CANCELLATION("Online Cancellation"),
    AFTER_ENTRY_CANCELLATION("After Entry Cancellation"),
    OUTSIDE_THE_COUNTRY_ONLINE_CANCELLATION("Outside the country online Cancellation"),
    RENEW_ELECTRONIC_WORK_PERMIT("Renewal of Work Permit"),
    EID_RENEW("EID Renew"),
    MEDICAL_RENEW("Medical Renew"),
    SUBMIT_RENEW_LABOR_CARD_APPICATION("Submit Renew Labour Card Application"),
    RENEW_RESIDENCE("Renew Residence"),
    RESIDENCE_CANCELLATION("Residence Cancellation"),
    SICK_CANCELLATION("Sick Cancellation"),
    CASH_REQUEST("Cash Request"),
    TRAVEL_REPORT("Travel Report"),
    ABSCONDING_IN_MOHRE("Absconding in Mohre"),
    UPDATE_IMMIGRATION_FILE_NUMBER("Update Immigration File Number"),
    CONTRACT_CANCELLATION("Contract Cancellation"),
    OFFER_LATTER_CANCELLATION("Offer Letter Cancellation"),
    REFUND_MEDICAL_APPLICATION_FEES("Refund Medical Application Fees"),
    LABOR_CARD_CANCELLATION_FEES("Labor card cancellation fees"),
    GDRFA_ABSCONDING("GDRFA Absconding"),
    MODIFY_CONTRACT("Modify contract"),
    UPDATE_INFORMATION_IN_IMMIGRATION("Update person information in immigration"),
    GOOD_CONDUCT_CERTIFICATE ("Good Conduct Certificate"),
    UPDATE_PHONE_NUMBER_IN_ICA ("Update Phone Number in ICA"),
    MOHRE_INSURANCE("MOHRE Insurance"),
    UNPAID_LEAVE("Unpaid Leave"),
    REFUND_FOR_ENTRY_VISA("Refund For Entry Visa"),
    CHALLENGE_OVERSTAY_FINES("Challenge overstay fines"),
    MODIFY_PERSON_INFORMATION_IN_MOHRE("Modify Person information in MOHRE"),
    HEPATITIS_VACCINE("Hepatitis Vaccine"),

    // ACC-6912 VPM-5079
    CANCEL_ENTRY_PERMIT ("Cancel Entry Permit") ,
    TAWJEEH("Tawjeeh"),
    MOHRE_INSURANCE_MAIDS("MOHRE Insurance Maids"),
    MOHRE_INSURANCE_EXPAT("MOHRE Insurance Expat"),
    SUBMIT_MOHRE_CONTRACT("Submit Mohre Contract"), // ACC-7968

    // VPM-5021
    ILOE_FINES("ILOE Fines"),
    ILOE_SUBSCRIPTION("ILOE Subscription");

    private final String label;

    private ExpensePurpose(String label) {
            this.label = label;
    }

    @JsonValue
    public String getLabel() {
            return label;
    }
}