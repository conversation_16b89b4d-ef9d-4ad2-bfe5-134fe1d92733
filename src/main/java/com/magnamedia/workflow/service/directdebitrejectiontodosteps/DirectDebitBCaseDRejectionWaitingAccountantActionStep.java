package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.controller.ContractController;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitConfiguration;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.DDUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.*;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Service
public class DirectDebitBCaseDRejectionWaitingAccountantActionStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;

    @Autowired
    private DirectDebitSignatureService directDebitSignatureService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitBCaseDRejectionWaitingAccountantActionStep.class.getName());


    public DirectDebitBCaseDRejectionWaitingAccountantActionStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_CASE_D.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        DirectDebit rejectedDirectDebit = entity.getLastDirectDebit();

        if (rejectedDirectDebit != null) {

            DirectDebitConfiguration ddConfiguration = rejectedDirectDebit.getDdConfiguration();
            logger.info("directDebitConfiguration IsIncludeManuals: " + ddConfiguration.isIncludeManualInDDBFlow());
            rejectedDirectDebit = directDebitRepository.findOne(rejectedDirectDebit.getId());
            if (entity.getSendToClient()) {

                entity.setTrials(entity.getTrials() + 1);
                entity.setReminder(0);
                entity.setDontSendDdMessage(false);
                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                if (rejectedDirectDebit.getCategory() == DirectDebitCategory.A) {
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.REJECTED);
                } else {
                    rejectedDirectDebit.setStatus(DirectDebitStatus.REJECTED);
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.REJECTED);
                }
                for (DirectDebitFile directDebitFile : rejectedDirectDebit.getDirectDebitFiles()) {
                    directDebitFile.setNeedAccountantReConfirmation(false);
                    directDebitFile.setDdStatus(DirectDebitStatus.REJECTED);
                    directDebitFile.setStatus(DirectDebitFileStatus.REJECTED);
                    directDebitFile.setIsFromAccountantAction(true);
                    if (directDebitFile.getRejectCategory().equals(DirectDebitRejectCategory.Signature))
                        directDebitSignatureService.updateSignatureStatus(directDebitFile, DirectDebitSignatureStatus.REJECTED);
                    directDebitFile = Setup.getApplicationContext().getBean(DirectDebitStatusChangeService.class)
                            .ddFileRejected(directDebitFile);
                    directDebitFileRepository.save(directDebitFile);
                }
                directDebitRepository.save(rejectedDirectDebit);


                SwitchingNationalityService switchingNationalityService = Setup.getApplicationContext().getBean(SwitchingNationalityService.class);
                SwitchingBankAccountService switchingBankAccountService = Setup.getApplicationContext().getBean(SwitchingBankAccountService.class);
                DirectDebit newDD;
                SwitchingType switchingType = switchingNationalityService.relatesToSwitching(rejectedDirectDebit.getId(), false) ?
                        SwitchingType.NATIONALITY :
                        switchingBankAccountService.isClientSwitchingBankAccount(rejectedDirectDebit) ? SwitchingType.BANK_ACCOUNT : null;

                if (switchingType != null) {
                    newDD = directDebitRejectionFlowService.getSwitchingNewDD(switchingType, rejectedDirectDebit, false);
                    addNewTask(entity, DDUtils.getDDRejectionToDoNextStep(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE, newDD.getCategory()).toString());
                } else {
                    newDD = rejectedDirectDebit.clone(DirectDebitStatus.PENDING);
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_CASE_D.toString());
                }

                entity.setLeadingRejectionFlow(!directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                        rejectedDirectDebit.getContractPaymentTerm().getContract(), Collections.singletonList(rejectedDirectDebit.getId())));
                logger.info( "leadingRejectionFlow: " + entity.getLeadingRejectionFlow());

                newDD.setGenerateManualDDFs(rejectedDirectDebit.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow());
                newDD.setStatus(DirectDebitStatus.IN_COMPLETE);
                newDD.setMStatus(DirectDebitStatus.IN_COMPLETE);
                newDD.setNonCompletedInfo(true);
                newDD.setConfirmedBankInfo(false);
                newDD.setDirectDebitRejectionToDo(entity);
                newDD.setAttachments(new ArrayList<>());
                if (newDD.getImageForDD() == null)
                    newDD.setImageForDD(rejectedDirectDebit.getImageForDD());
                directDebitRepository.save(newDD);

                Integer maxTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_MAX_TRIALS));

                if (entity.getTrials() > maxTrials) {
                    Contract contract = rejectedDirectDebit.getContractPaymentTerm().getContract();
                    
                    if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                        logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                        entity.setContractScheduleDateOfTermination(
                                directDebitRejectionFlowService
                                        .setContractForTermination(
                                                rejectedDirectDebit.getContractPaymentTerm(),
                                                "direct_debit_rejection_type_b_bank_info_max_trials_reached",
                                                entity));
                        entity.setLeadingRejectionFlow(true);
                        contract.setScheduledDateOfTermination(entity.getContractScheduleDateOfTermination());
                    }
                    entity.setCompleted(true);
                    entity.setStopped(true);
                }

            } else {
                if (entity.getEid() != null) {
                    rejectedDirectDebit.setEid(entity.getEid());
                } else if (entity.getAccountName() != null) {
                    rejectedDirectDebit.setAccountName(entity.getAccountName());
                } else {
                    logger.log(Level.SEVERE, "eid and account name is null");
                    return;
                }
                if (rejectedDirectDebit.getCategory() == DirectDebitCategory.A) {
                    rejectedDirectDebit.setMStatus(DirectDebitStatus.PENDING);
                } else {
                    rejectedDirectDebit.setStatus(DirectDebitStatus.PENDING);
                    if (rejectedDirectDebit.isGenerateManualDDFsFromConfig() && ddConfiguration.isIncludeManualInDDBFlow())
                        rejectedDirectDebit.setMStatus(DirectDebitStatus.PENDING);
                }

                rejectedDirectDebit.setConfirmedBankInfo(true);

                for (DirectDebitFile f : rejectedDirectDebit.getDirectDebitFiles()) {
                    if ((!rejectedDirectDebit.isGenerateManualDDFsFromConfig() || !ddConfiguration.isIncludeManualInDDBFlow())
                            && f.getDdMethod().equals(DirectDebitMethod.MANUAL)) {

                        logger.info("Manual DDF -> step over it");
                        continue;
                    }

                    f.setNeedAccountantReConfirmation(false);
                    f.setDdStatus(DirectDebitStatus.PENDING);
                    f.setStatus(DirectDebitFileStatus.NOT_SENT);
                    f.setConfirmedBankInfo(true);
                    if (entity.getEid() != null) {
                        f.setEid(entity.getEid());
                    } else if (entity.getAccountName() != null) {
                        f.setAccountName(entity.getAccountName());
                    }

                    if (!Setup.getApplicationContext().getBean(DirectDebitService.class)
                            .createDirectDebitActivationAttachmentIfNotExist(f)) {

                        directDebitFileRepository.save(f);
                        directDebitSignatureService.updateSignatureStatus(f,
                                DirectDebitSignatureStatus.UNUSED);
                    }
                }

                if (entity.getTrials() == 0 && entity.getDirectDebits().size() == 1) {
                    entity.setStopped(true);
                    entity.setCompleted(true);
                    entity.setDontSendDdMessage(true);

                    rejectedDirectDebit.setDirectDebitRejectionToDo(null);
                } else {
                    entity.setDontSendDdMessage(true);
                    addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_CASE_D.toString());
                }
                directDebitRepository.save(rejectedDirectDebit);
            }
        } else {
            throw new RuntimeException("lastDirectDebit can't be null");
        }
        super.onDone(entity);
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}