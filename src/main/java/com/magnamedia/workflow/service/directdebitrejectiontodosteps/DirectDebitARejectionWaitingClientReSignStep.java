package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.type.DirectDebitRejectCategory;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@Service
public class DirectDebitARejectionWaitingClientReSignStep extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {
    private static final Logger logger =
            Logger.getLogger(DirectDebitARejectionWaitingClientReSignStep.class.getName());

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    @Autowired
    private DirectDebitRepository directDebitRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    public DirectDebitARejectionWaitingClientReSignStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        super.onDone(entity);

        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingClientReSignStep on done starts ");

        DirectDebit directDebit = entity.getDirectDebits().get(entity.getDirectDebits().size() - 1);

        if (directDebit != null) {
            if (!directDebit.getStatus().equals(DirectDebitStatus.IN_COMPLETE)) {
                logger.log(Level.SEVERE, " client provided new Info");
                entity.setReminder(0);
                entity.setLeadingRejectionFlowForced(Boolean.FALSE);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE.toString());
                directDebitRejectionToDoRepository.save(entity);

            } else if (entity.getLastRejectCategory().equals(DirectDebitRejectCategory.Authorization)) {
                
                logger.log(Level.INFO, " client didn't provide new Info");
                DirectDebit lastRejected = entity.getDirectDebits().get(entity.getDirectDebits().size() - 2);

                directDebit.setStatus(DirectDebitStatus.PENDING);
                directDebit.setMStatus(DirectDebitStatus.PENDING);
                directDebit.setConfirmedBankInfo(true);
                directDebit.setNonCompletedInfo(false);
                directDebitRepository.save(directDebit);

                lastRejected.cloneChildDds(directDebit, 0);

                entity.setReminder(0);
                entity.setLeadingRejectionFlowForced(Boolean.FALSE);
                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE.toString());
                directDebitRejectionToDoRepository.save(entity);

            }
        } else {
            throw new RuntimeException("directDebit can't be null");
        }

    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}
