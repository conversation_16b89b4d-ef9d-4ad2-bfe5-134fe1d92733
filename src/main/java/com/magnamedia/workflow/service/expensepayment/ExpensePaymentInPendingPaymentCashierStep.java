package com.magnamedia.workflow.service.expensepayment;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.helper.AttachmentHelper;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Service
public class ExpensePaymentInPendingPaymentCashierStep extends ExpensePaymentAbstractStep<ExpensePayment> {

    public ExpensePaymentInPendingPaymentCashierStep() {
        this.setId(ExpensePaymentToDoType.TO_DO_IN_PENDING_PAYMENT_CASHIER_SCREEN.toString());
    }

    @Override
    public void onDone(ExpensePayment entity) {
        super.onDone(entity);
        if (isInvoiceMessingOrTaxInvoiceMessing(entity)) {
            entity.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
            addNewTask(entity, ExpensePaymentToDoType.TO_DO_IN_PENDING_INVOICE_SCREEN.toString());
        } else {
            entity.setStatus(ExpensePaymentStatus.PAID);
            addNewTask(entity, ExpensePaymentToDoType.TO_DO_IN_RECONCILIATOR_CONFIRMATION_SCREEN.toString());
        }
    }

    public static boolean isInvoiceMessingOrTaxInvoiceMessing(ExpensePayment expensePayment) {
        if (expensePayment.getRequiresInvoice() == null) return false;
        if (expensePayment.getRequiresInvoice().equals(Boolean.FALSE)) return false;

        Attachment invoice = AttachmentHelper.getRequestAttachment(expensePayment, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
        if (invoice == null) return true;

        if (expensePayment.getTaxable() == null) return true;
        if (expensePayment.getTaxable().equals(Boolean.FALSE)) return false;

        if (expensePayment.getVatAmount() == null) return true;
        if (expensePayment.getAttachedValidVatInvoice() == null) return true;
        if (expensePayment.getAttachedValidVatInvoice().equals(Boolean.TRUE)) return false;

        Attachment vatInvoice = AttachmentHelper.getRequestAttachment(expensePayment, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
        if (vatInvoice == null) return true;

        return false;
    }

}
