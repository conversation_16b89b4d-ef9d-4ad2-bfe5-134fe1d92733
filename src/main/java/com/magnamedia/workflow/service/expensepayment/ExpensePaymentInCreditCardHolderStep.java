package com.magnamedia.workflow.service.expensepayment;

import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.service.ExpensePaymentService;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Service
public class ExpensePaymentInCreditCardHolderStep extends ExpensePaymentAbstractStep<ExpensePayment> {

    @Autowired
    ExpensePaymentService expensePaymentService;

    public ExpensePaymentInCreditCardHolderStep() {
        this.setId(ExpensePaymentToDoType.TO_DO_IN_CREDIT_CARD_HOLDER_SCREEN.toString());
    }

    @Override
    public void onDone(ExpensePayment entity) {
        super.onDone(entity);
        entity.setCompleted(Boolean.TRUE);
        entity.setStatus(ExpensePaymentStatus.PAID);
    }
}
