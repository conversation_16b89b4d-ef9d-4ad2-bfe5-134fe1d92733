package com.magnamedia.workflow.service.expensepayment;

import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.repository.ExpensePaymentRepository;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.PayrollAccountantTodoManagerAction;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Service
public class ExpensePaymentBankTransferStep extends ExpensePaymentAbstractStep<ExpensePayment> {

    public ExpensePaymentBankTransferStep() {
        this.setId(ExpensePaymentToDoType.TO_DO_BANK_TRANSFER_SCREEN.toString());
    }

    @Autowired
    ExpensePaymentRepository expensePaymentRepository;

    @Override
    public void onDone(ExpensePayment entity) {
        super.onDone(entity);
    }

    public void initBankTransferTask(ExpensePayment expensePayment) {
        PayrollAccountantTodo todo = new PayrollAccountantTodo();
        Logger.getLogger(ExpensePaymentBankTransferStep.class.getName()).log(Level.SEVERE,"initBankTransferTask");
        todo.setExpensePayment(expensePayment);
        Setup.getApplicationContext().getBean(PayrollAccountantTodoController.class).createEntity(todo);
    }

    public void initMoneyTransferTask(ExpensePayment expensePayment) {
        PayrollAccountantTodo todo = new PayrollAccountantTodo();
        Logger.getLogger(ExpensePaymentBankTransferStep.class.getName()).log(Level.SEVERE,"initMoneyTransferTask");
        todo.setExpensePayment(expensePayment);
        Setup.getApplicationContext().getBean(PayrollAccountantTodoController.class).createEntity(todo);
    }

    public void doBusinessForExpensePaymentAfterConfirmBankTransfer(PayrollAccountantTodo payrollAccountantTodo) {
        ExpensePayment expensePayment = payrollAccountantTodo.getExpensePayment();
        if (payrollAccountantTodo.getCeoAction().equals(PayrollAccountantTodoManagerAction.APPROVED)) {
            expensePayment.setStatus(ExpensePaymentStatus.PAID);
            expensePayment.setCompleted(Boolean.TRUE);
            expensePaymentRepository.save(expensePayment);
        } else if (payrollAccountantTodo.getCeoAction().equals(PayrollAccountantTodoManagerAction.REJECTED)) {
            expensePayment.setStopped(true);
            expensePayment.updateExpensesStatus(ExpenseRequestStatus.REJECTED);
            expensePaymentRepository.save(expensePayment);
        }
    }
}