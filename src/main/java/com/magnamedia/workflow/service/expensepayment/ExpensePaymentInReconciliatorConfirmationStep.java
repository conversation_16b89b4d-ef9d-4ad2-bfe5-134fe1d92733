package com.magnamedia.workflow.service.expensepayment;

import com.magnamedia.controller.TransactionsController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.repository.TransactionRepository;
import com.magnamedia.service.ExpensePaymentService;
import com.magnamedia.service.ExpenseRequestService;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpensePaymentToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <PERSON> (Jan 19, 2021)
 */
@Service
public class ExpensePaymentInReconciliatorConfirmationStep extends ExpensePaymentAbstractStep<ExpensePayment> {
    @Autowired
    ExpensePaymentService expensePaymentService;

    @Autowired
    ExpenseRequestService expenseRequestService;

    public ExpensePaymentInReconciliatorConfirmationStep() {
        this.setId(ExpensePaymentToDoType.TO_DO_IN_RECONCILIATOR_CONFIRMATION_SCREEN.toString());
    }

    @Override
    public void onSave(ExpensePayment expensePayment) {
        updateTransaction(expensePayment);
        super.onSave(expensePayment);
    }

    public void updateTransaction(ExpensePayment expensePayment) {
        
        expensePayment.setAmountsTrigger();
        TransactionRepository transactionRepository = Setup.getRepository(TransactionRepository.class);
        TransactionsController transactionsController = Setup.getApplicationContext().getBean(TransactionsController.class);
        Transaction transaction = null;
        if(expensePayment.getTransaction() != null
                && expensePayment.getTransaction().getId() != null){
            
            transaction = transactionRepository.findOne(expensePayment.getTransaction().getId());
            if (transaction != null){
                expensePaymentService.updateTransactionObjectFromPayment(expensePayment, transaction, transaction.getDate(), expensePayment.getLocalCurrencyAmount() - (expensePayment.getLoanAmount() != null ?  expensePayment.getLoanAmount() : 0D), expensePayment.getVatAmount());
                transaction.setDescription(expensePayment.getDescription());
                transactionsController.newUpdateEntity(transaction);
            }
        }
        
        List<Transaction> transactions = transactionRepository.findByExpensePaymentId(expensePayment.getId());
        for (Transaction trans : transactions){
            if (transaction == null || !Objects.equals(transaction.getId(), trans.getId())){
                expensePaymentService.updateTransactionObjectFromPayment(expensePayment, trans, trans.getDate(), (expensePayment.getLoanAmount() != null ?  expensePayment.getLoanAmount() : 0D), null);
                //trans.setDescription(expensePayment.getTransaction().getDescription());
                transactionsController.newUpdateEntity(trans);
                break;
            }
        }

    }


    @Override
    public void onDone(ExpensePayment entity) {
        super.onDone(entity);
        expenseRequestService.reconciliatorConfirmationStep(entity);
    }



}
