package com.magnamedia.workflow.service.clientrefundtodosteps;


import com.magnamedia.entity.workflow.ClientRefundToDo;;
import com.magnamedia.workflow.service.ClientRefundManualStep;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @created 06/08/2024 - 4:27 PM
 * ACC-7699
 */

@Service
public class ConditionalRefundWaitingPaymentsBeforeManagerApprovalStep extends ClientRefundManualStep<ClientRefundToDo> {

    private static final Logger logger = Logger.getLogger(ConditionalRefundWaitingPaymentsBeforeManagerApprovalStep.class.getName());

    public ConditionalRefundWaitingPaymentsBeforeManagerApprovalStep() {
        this.setId(ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL.toString());
    }

    @Override
    public void onSave(ClientRefundToDo entity) {
    }

    @Override
    public void postSave(ClientRefundToDo entity) {
    }

    @Override
    public void onDone(ClientRefundToDo entity) {
        logger.log(Level.INFO, "Client Refund Conditional Refund Manager Approval Step Started");
    }

    @Override
    public void postDone(ClientRefundToDo entity) {
    }

    @Override
    public List<String> getTaskHeader() {
        return Arrays.asList("client.label", "purpose.label", "amount", "detail");
    }
}
