package com.magnamedia.workflow.service.expenserequesttodosteps;


import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.ExpenseRequestType;
import com.magnamedia.workflow.service.ExpenseRequestManualStep;
import com.magnamedia.workflow.service.maintenancerequest.ConfirmMaintenanceRequestStep;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.ExpenseRequestTodoFlowActions;
import com.magnamedia.workflow.type.ExpenseRequestTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * Created on Jan 18, 2021
 * Jirra ACC-2913
 */
@Service
public class ExpenseRequestWaitingCooApprovalStep extends ExpenseRequestManualStep<ExpenseRequestTodo> {


    private static final Logger logger =
            Logger.getLogger(ExpenseRequestWaitingCooApprovalStep.class.getName());


    public ExpenseRequestWaitingCooApprovalStep() {
        this.setId(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString());
    }

    @Override
    public void onSave(ExpenseRequestTodo entity) {
    }

    @Override
    public void postSave(ExpenseRequestTodo entity) {
    }

    @Autowired
    ConfirmMaintenanceRequestStep confirmMaintenanceRequestStep;

    @Override
    public void onDone(ExpenseRequestTodo entity) {
        logger.log(Level.INFO, "Expense Request Coo Approval Step Started");

        if (entity.getCooAction() != null) {

            if (entity.getCooAction().equals(ExpenseRequestTodoFlowActions.APPROVE)) {
                if(CurrentRequest.getUser() != null
                        && CurrentRequest.getUser().getUsername() != null
                        && !CurrentRequest.getUser().getUsername().equalsIgnoreCase("guest"))
                    entity.addApproval(CurrentRequest.getUser().getUsername());
                entity.setStatus(ExpenseRequestStatus.PENDING_PAYMENT);

                if (!entity.getPaymentMethod().equals(ExpensePaymentMethod.CHEQUE) || !entity.getPaymentMethod().equals(ExpensePaymentMethod.INVOICED)) {
                    addNewTask(entity, ExpenseRequestTodoType.PAYMENT_OBJECT_CREATED.toString());
                }
                if(isMaintenanceRequest(entity))
                    confirmMaintenanceRequestStep.cooApprovedRequest(entity);


            } else if (entity.getCooAction().equals(ExpenseRequestTodoFlowActions.REJECT)) {

                entity.setStopped(true);
                entity.setCompleted(true);
                entity.setStatus(ExpenseRequestStatus.REJECTED);
                if (isMaintenanceRequest(entity))
                    confirmMaintenanceRequestStep.cooRejectedRequest(entity);

            } else if (entity.getCooAction().equals(ExpenseRequestTodoFlowActions.GET_BETTER_PRICE)) {
                confirmMaintenanceRequestStep.cooGetBetterPriceRequest(entity);

            } else {
                throw new RuntimeException("Coo Action has unexpected value");
            }

        } else {
            throw new RuntimeException("Coo Action can't be null");
        }

        super.onDone(entity);
        // no need for save since complete task method in workflow controller do the save

    }

    private boolean isMaintenanceRequest(ExpenseRequestTodo entity) {
        return entity.getExpenseRequestType() != null && entity.getExpenseRequestType().equals(ExpenseRequestType.MAINTENANCE);
    }

    @Override
    public void postDone(ExpenseRequestTodo entity) {
    }

    public List<String> getTaskHeader() {
        return new ArrayList<>();
    }

}
