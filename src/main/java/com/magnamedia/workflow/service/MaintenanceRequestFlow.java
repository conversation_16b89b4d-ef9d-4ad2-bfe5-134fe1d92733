package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.workflow.service.maintenancerequest.ConfirmMaintenanceRequestStep;
import com.magnamedia.workflow.service.maintenancerequest.GetBetterPriceForMaintenanceRequestStep;
import com.magnamedia.workflow.service.maintenancerequest.GetPriceForMaintenanceRequestStep;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON> (Feb 07, 2021)
 */
@Service
public class MaintenanceRequestFlow extends RoleBasedWorkflow<MaintenanceRequest> {
    private final ConfirmMaintenanceRequestStep confirmMaintenanceRequestStep;
    private final GetBetterPriceForMaintenanceRequestStep getBetterPriceForMaintenanceRequestStep;
    private final GetPriceForMaintenanceRequestStep getPriceForMaintenanceRequestStep;
    public static final String flowName = "maintenanceRequestFlow";

    public MaintenanceRequestFlow(ConfirmMaintenanceRequestStep confirmMaintenanceRequestStep, GetBetterPriceForMaintenanceRequestStep getBetterPriceForMaintenanceRequestStep, GetPriceForMaintenanceRequestStep getPriceForMaintenanceRequestStep) {
        this.confirmMaintenanceRequestStep = confirmMaintenanceRequestStep;
        this.getBetterPriceForMaintenanceRequestStep = getBetterPriceForMaintenanceRequestStep;
        this.getPriceForMaintenanceRequestStep = getPriceForMaintenanceRequestStep;

        setId(flowName);

        this.getWorkflowSteps().addAll(
                Arrays.asList(
                        this.confirmMaintenanceRequestStep,
                        this.getBetterPriceForMaintenanceRequestStep,
                        this.getPriceForMaintenanceRequestStep));

        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });

    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }
}
