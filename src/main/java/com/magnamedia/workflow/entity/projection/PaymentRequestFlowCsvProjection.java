package com.magnamedia.workflow.entity.projection;

import java.sql.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 10, 2019
 * ACC-837
 */
public interface  PaymentRequestFlowCsvProjection {

    java.util.Date getCreationDate();
    String getRecieverName();
    @Value("#{(target.getPurpose() != null) ? "
            + "target.getPurpose().getName() : ''}")
    String getPurpose();
    Double getAmount();
    String getRequesterUserName();
    //String getWorkflowCsv();
    @Value("#{(target.getMethodOfPayment() != null) ? "
            + "target.getMethodOfPayment().getLabel() : ''}")
    String getMethodOfPayment();
    String getNotes();
    String getStatus();
    Date getStatusChangeDate();
    @Value("#{(target.getPurposeAdditionalDescription()!=null)?"
            + "(target.getPurposeAdditionalDescription().getName()):''}")
    String getPurposeAdditionalDescription();
}
