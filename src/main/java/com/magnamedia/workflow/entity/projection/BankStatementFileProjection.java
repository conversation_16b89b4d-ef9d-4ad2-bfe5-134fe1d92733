package com.magnamedia.workflow.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.workflow.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At Apr 19, 2020
 **/

@Projection(name = "bankStatementFileProjection",
        types = BaseEntity.class)
public interface BankStatementFileProjection
        extends Task {

    Long getId();

    Date getCreationDate();

    Long getTotalUnresolvedTransactions();

    Long getTotalResolvedTransactions();

    Long getTotalTransactions();

    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getName():null}")
    public String getFileName();

    public boolean isDeleted();

    List<Attachment> getAttachments();
}
