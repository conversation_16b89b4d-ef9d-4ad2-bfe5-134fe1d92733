package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 22, 2019
 * ACC-837
 */
public enum PaymentRequestStatus implements LabelValueEnum {
    PENDING_MANGER_APPROVAL("Pending Manger Approval"), 
    PENDING_ACCOUNTANT_APPROVAL("Pending Accountant Approval"), 
    PENDING_TO_BE_PAID("Pending To Be Paid");

    private final String label;

    private PaymentRequestStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
