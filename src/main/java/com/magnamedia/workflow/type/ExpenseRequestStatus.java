package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * Created on Jan 18, 2021
 * Jirra ACC-2913
 */

public enum ExpenseRequestStatus implements LabelValueEnum {
    PENDING("Pending"),
    PENDING_PAYMENT("Pending Payment"),
    REJECTED("Rejected"),
    PAID("Paid"),
    CANCELED("Canceled"),
    DISMISSED("Dismissed");

    private final String label;

    ExpenseRequestStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
