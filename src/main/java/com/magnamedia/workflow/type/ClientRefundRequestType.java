package com.magnamedia.workflow.type;

import com.magnamedia.extra.LabelValueEnum;

public enum ClientRefundRequestType implements LabelValueEnum {
    ERP("ERP"),
    CONTRACT_CANCELLATION_REFUND("Contract Cancelation Refund"),
    CONTRACT_FREEZING_FLOW("Contract Freezing Flow"),
    DISCOUNT_REFUND("Discount Refund"),
    DUPLICATED_PAYMENT("Duplicated Payment");

    private final String label;

    private ClientRefundRequestType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
