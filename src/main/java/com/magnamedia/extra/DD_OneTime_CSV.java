package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.module.type.DirectDebitType;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 17, 2020
 *         Jirra ACC-2906
 */
public class DD_OneTime_CSV {
    private Long contractId;
    private Date ddStartDate;
    private Date ddEndDate;
    private Double ddAmount;
    private String ddType;
    private String paymentType;

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Date getDdStartDate() {
        return ddStartDate;
    }

    public void setDdStartDate(Date ddStartDate) {
        this.ddStartDate = ddStartDate;
    }

    public Date getDdEndDate() {
        return ddEndDate;
    }

    public void setDdEndDate(Date ddEndDate) {
        this.ddEndDate = ddEndDate;
    }

    public Double getDdAmount() {
        return ddAmount;
    }

    public void setDdAmount(Double ddAmount) {
        this.ddAmount = ddAmount;
    }

    public String getDdType() {
        return ddType;
    }

    public void setDdType(String ddType) {
        this.ddType = ddType;
    }

    @JsonIgnore
    public DirectDebitType getDdTypeAsEnum() {
        return ddType.equals("One-time") ? DirectDebitType.ONE_TIME : ddType.equals("monthly") ? DirectDebitType.MONTHLY : null;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }
}
