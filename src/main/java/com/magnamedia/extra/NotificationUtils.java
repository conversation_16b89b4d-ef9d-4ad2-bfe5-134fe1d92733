/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 *//*

package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Notification;
import com.magnamedia.core.entity.NotificationType;
import com.magnamedia.core.entity.Position;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.notification.NotificationService;
import com.magnamedia.core.repository.NotificationTypeRepository;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.repository.UserRepository;
import java.io.PrintWriter;
import java.io.StringWriter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import org.springframework.stereotype.Component;

*/
/**
 *
 * <AUTHOR>
 *//*

@Component
public class NotificationUtils {

    @Autowired
    NotificationService notificationService;
    @Autowired
    PositionRepository positionRepository;
    @Autowired
    UserRepository userRepository;
    @Autowired
    NotificationTypeRepository notificationTypeRepository;

    private static Notification createNotification(String title, String body) {
        Notification result = new Notification();
        result.setTitle(title);
        result.setBody(body);
        result.setModule(Setup.getCurrentModule());
        result.setDate(new Date());
        return result;
    }

    public void sendNotificationOfType(String title, String body, String typeCode, Boolean withEmail) {
        NotificationType type = null;
        try {
            type = notificationTypeRepository.findByCode(typeCode);
        } catch (Exception ex) {
            System.out.println(getExceptionStackTrace(ex));
        }

        if (type == null) {
            throw new RuntimeException("Cannot find type: " + typeCode);
        }

        notificationService.sendNotification(title, body, type, withEmail);
    }
    
    public void sendNotificationOfType(String title, String body, String typeCode) {
        sendNotificationOfType(title, body, typeCode, false);
    }

    public Notification sendNotification(String title, String body, String positionCode, Boolean withEmail) {
        Notification result = createNotification(title, body);
        Position position = null;
        try {
            position = positionRepository.findByCode(positionCode);
        } catch (Exception ex) {
            System.out.println(getExceptionStackTrace(ex));
        }

        if (position == null) {
            throw new RuntimeException("Cannot find position: " + positionCode);
        }
        result.setPosition(position);
        notificationService.sendNotification(result, withEmail);
        return result;
    }

    public Notification sendNotification(String title, String body, User user, Boolean withEmail) {
        Notification result = createNotification(title, body);
        result.setUser(user);
        notificationService.sendNotification(result, withEmail);
        return result;
    }

    public Notification sendNotification(String title, String body, Long userId, Boolean withEmail) {
        User user = null;
        try {
            user = userRepository.findOne(userId);
        } catch (Exception ex) {
            System.out.println(getExceptionStackTrace(ex));
        }
        if (user == null) {
            throw new RuntimeException("Cannot find user with id " + userId);
        }

        return sendNotification(title, body, user, withEmail);
    }
    
     public void sendNotification(String title, String body, Long userId) {
        sendNotification(title, body, userId, false);
    }

    public static String getExceptionStackTrace(Exception ex) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        ex.printStackTrace(pw);
        String sStackTrace = sw.toString(); // stack trace as a string
        return sStackTrace;
    }

   

   
}
*/
