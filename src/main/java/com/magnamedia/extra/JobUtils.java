package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.entity.AccountingEntityProperty;
import com.magnamedia.repository.AccountingEntityPropertyRepository;
import org.joda.time.LocalDateTime;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;


public class JobUtils {

    private static final Logger logger = Logger.getLogger(JobUtils.class.getName());

    public static Date getJobLastRunDate(AccountingEntityProperty lastCheckDateProperty, Date lastRunDate) {

        if (lastCheckDateProperty != null) {
            try {
                lastRunDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(lastCheckDateProperty.getValue());
            } catch (Exception e) {
                e.printStackTrace();
                logger.severe("Exception while parsing " + lastCheckDateProperty.getValue() + ", to java.util.Date");
            }
        }

        logger.info("Job last Run Date: " + lastRunDate);
        return lastRunDate;
    }

    public static void setJobLastRunDate(AccountingEntityProperty lastCheckDateProperty, String key, LocalDateTime lastRunDate) {

        logger.info("Save last check date at the end of the job, End Date: " + lastRunDate);
        if (lastCheckDateProperty == null) {
            lastCheckDateProperty = new AccountingEntityProperty();
            lastCheckDateProperty.setOrigin(null);
            lastCheckDateProperty.setKey(key);
        }

        lastCheckDateProperty.setValue(lastRunDate.toString("yyyy-MM-dd HH:mm:ss"));
        Setup.getRepository(AccountingEntityPropertyRepository.class).save(lastCheckDateProperty);
    }
}