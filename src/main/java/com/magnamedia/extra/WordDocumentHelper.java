package com.magnamedia.extra;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.WordTemplate;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.module.AccountingModule;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRow;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 20, 2021
 *         Jirra ACC-2897
 */

public class WordDocumentHelper {

    public static XWPFDocument parseTables(InputStream templateIS, List<StandardEvaluationContext> payments, Map parameters) throws Exception {
        ExpressionParser parser = new SpelExpressionParser();
        XWPFDocument document = new XWPFDocument(templateIS);
        XWPFTable paymentsTable = document.getTables().get(1);
        //get Expressions
        List<String> columnsExp = new ArrayList();
        XWPFTableRow expressionsRow = paymentsTable.getRow(1);
        CTRow ctrow = CTRow.Factory.parse(expressionsRow.getCtRow().newInputStream());
        for (int i = 0; i < expressionsRow.getTableCells().size(); i++) {
            columnsExp.add(expressionsRow.getCell(i).getText());
        }

        //ACC-3952 ACC-3958
        paymentsTable.removeRow(1);

        for (int i = 0; i < payments.size(); i++) {
            XWPFTableRow newRow = new XWPFTableRow(ctrow, paymentsTable);
            newRow.setHeight(5);
            for (int j = 0; j < columnsExp.size(); j++)
                if (!columnsExp.get(j).isEmpty()) {
                    if (!columnsExp.get(j).startsWith("#root"))
                        addValueToCell(newRow.getCell(j),
                                columnsExp.get(j),
                                ParagraphAlignment.CENTER);
                    else
                        addValueToCell(newRow.getCell(j),
                                parser.parseExpression(columnsExp.get(j)).getValue(payments.get(i), String.class),
                                ParagraphAlignment.CENTER);

                }
            paymentsTable.addRow(newRow, i + 1);
        }

        if(document.getTables().size() <= 2) return document;

        XWPFTable termsTable = document.getTables().get(2);
        StandardEvaluationContext parametersContext = new StandardEvaluationContext(parameters);

        boolean emptyRow;
        for (int i = 0; i < termsTable.getRows().size(); i++) {
            XWPFTableRow row = termsTable.getRow(i);
            emptyRow = true;
            for (XWPFTableCell cell : row.getTableCells()) {
                if (cell.getText().contains("#root")) {
                    String cellValue = cell.getText();
                    XWPFParagraph firstParagraph = cell.getParagraphs().get(0);
                    firstParagraph.getRuns().get(0)
                            .setText(parser.parseExpression(cellValue).getValue(parametersContext, String.class), 0);
                    while (firstParagraph.getRuns().size() > 1)
                        firstParagraph.removeRun(1);
                    while (cell.getParagraphs().size() > 1)
                        cell.removeParagraph(1);
                }
                if (!cell.getText().isEmpty())
                    emptyRow = false;
            }
            if (emptyRow)
                termsTable.removeRow(i);
        }

        return document;
    }

    private static void addValueToCell(XWPFTableCell cell, String value, ParagraphAlignment paragraphAlignment) {
        while (cell.getParagraphs().size() > 0)
            cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(paragraphAlignment);
        paragraph.createRun().setText(value);
        paragraph.setSpacingAfter(0);
        paragraph.setSpacingBefore(0);

    }

    public static List<WordTemplate> getTemplatesLikeCodeNotLikeCode(List<String> likeCodes, List<String> notLikeCodes) {
        SelectQuery<WordTemplate> query = new SelectQuery(WordTemplate.class);
        for (String code : likeCodes) {
            query.filterBy("code", "like", code);
        }

        for (String code : notLikeCodes) {
            query.filterBy("code", "not like", code);
        }
        return query.execute();
    }

    public static String getPackageCode(Contract contract, ContractPaymentTerm cpt) {
        if (contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_CC_PROSPECT_TYPE))) {
            return cpt.getPackageType() != null ? cpt.getPackageType().getValue() : null;
        }

        if (contract.getContractProspectType().getCode().equals(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE))) {
            return contract.getWorkerCurrentSituation() != null ? contract.getWorkerCurrentSituation().getCode() : null;
        }

        return null;
    }

    public static WordTemplate findSuitableTemplateByDate(List<WordTemplate> matchedTemplates, Date cptDate) throws ParseException {
        WordTemplate template = null;

        List<WordTemplate> matchedTemplatesWithoutDateTag = matchedTemplates.stream().filter(mt -> !mt.getCode().contains("_sd")).collect(Collectors.toList());
        if (!matchedTemplatesWithoutDateTag.isEmpty()) {
            template = matchedTemplatesWithoutDateTag.get(0);
        } else {
            List<WordTemplate> matchedTemplatesWithDateTag = matchedTemplates.stream().filter(mt -> mt.getCode().contains("_sd")).collect(Collectors.toList());
            matchedTemplatesWithDateTag = matchedTemplatesWithDateTag.stream().sorted(Comparator.comparing(x -> x.getCode(), Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
            for (WordTemplate matchedTemplate : matchedTemplatesWithDateTag) {
                String[] codeParts = matchedTemplate.getCode().split("_sd");
                DateFormat parsingDateFormat = new SimpleDateFormat("yyyyMMddhhmm");
                Date templateDate = null;
                if (codeParts.length == 2)
                    templateDate = parsingDateFormat.parse((codeParts[1]).split("_")[0]);
                if (cptDate.getTime() >= templateDate.getTime()) {
                    template = matchedTemplate;
                }
            }

            if (template == null) {
                throw new RuntimeException("CPT creation date is before all matched templates");
            }
        }

        return template;
    }
}
