package com.magnamedia.extra;

import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.InsuranceAgreement;
import org.joda.time.Days;
import org.joda.time.LocalDate;

import java.sql.Date;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 7, 2019
 * Jirra ACC-818
 */

public class InsuranceNote {

    private Long maidId;
    private String maidName;
    private Date endorsementDate;
    private Date terminationDate;
    private Integer numberOfDays;
    private Double premium;
    //Jirra ACC-818
    private Date insuranceStartDate;
    private Date insuranceEndDate;
    // Jirra ACC-1208
    private String workerType;
    //ACC-1824
    private String emiratesId;

    // acc-2481
    private Double basmaCharge;

    // acc-2442
    private String passportNumber;

    // ACC-4655
    private Double totalAmount;

    private Date joiningDate;

    // ACC-818
    public static InsuranceNote getDebitNote(
            InsuranceAgreement agreement, Long maidId, String maidName, Date insuranceStartDate, String workerType, String emiratesId,
            String passportNumber,
            Date joiningDate) {

        InsuranceNote de = new InsuranceNote();
        de.maidId = maidId;
        de.maidName = maidName;
        de.insuranceStartDate = insuranceStartDate;
        if (de.insuranceStartDate.before(agreement.getStartDate()))
            de.numberOfDays =
                    Days.daysBetween(new LocalDate(agreement.getStartDate()), new LocalDate(agreement.getEndDate())).getDays();
        else
            de.numberOfDays =
                    Days.daysBetween(new LocalDate(de.insuranceStartDate), new LocalDate(agreement.getEndDate())).getDays();
        de.premium = Math.round(((double) de.numberOfDays * agreement.getDebitAmount() / 364) * 100) / 100.0;
        de.workerType = initWorkerType(workerType);
        de.emiratesId = emiratesId;
        de.basmaCharge = agreement.getBasmaCharge();
        de.passportNumber = passportNumber;
        // Jira ACC-4655
        de.totalAmount =  Math.round(((double)(de.premium + de.basmaCharge)) * 100) / 100.0;
        de.joiningDate = joiningDate;
        return de;
    }

    public static InsuranceNote getCreditNote(
            InsuranceAgreement agreement, Long maidId, String maidName, Date insuranceEndDate, String workerType, String emiratesId,
            String passportNumber, Date terminationDate) {

        InsuranceNote de = new InsuranceNote();
        de.maidId = maidId;
        de.maidName = maidName;
        de.insuranceEndDate = insuranceEndDate;
        de.numberOfDays =
                Days.daysBetween(new LocalDate(de.insuranceEndDate), new LocalDate(agreement.getEndDate())).getDays()
                        - agreement.getNumberOfDaysChargedAfterCancelation();
        de.premium = Math.round(((double) de.numberOfDays * agreement.getCreditAmount() / 364) * 100) / 100.0;

        de.workerType = initWorkerType(workerType);
        //acc-1824
        de.emiratesId = emiratesId;
        de.basmaCharge = agreement.getBasmaCharge();
        de.passportNumber = passportNumber;
        //ACC-4655
        de.terminationDate = terminationDate;
        return de;
    }

    public InsuranceNote() {

    }

    public InsuranceNote(Housemaid h, InsuranceAgreement agreement, boolean isDebit) {
        maidId = h.getId();
        maidName = h.getName();
        endorsementDate = h.getDateOfInsuranceEndorsement();
        if (isDebit) {
            if (h.getDateOfInsuranceEndorsement().before(agreement.getStartDate()))
                numberOfDays =
                        Days.daysBetween(new LocalDate(agreement.getStartDate()),
                                new LocalDate(agreement.getEndDate())).getDays();
            else
                numberOfDays =
                        Days.daysBetween(new LocalDate(agreement.getStartDate()),
                                new LocalDate(agreement.getEndDate())).getDays();
        } else {
            numberOfDays =
                    Days.daysBetween(new LocalDate(h.getVisaCancellationDate()),
                            new LocalDate(agreement.getEndDate())).getDays()
                            - agreement.getNumberOfDaysChargedAfterCancelation();
            terminationDate = new Date(h.getVisaCancellationDate().getTime());
        }
        premium = Math.round(((double) numberOfDays * agreement.getPrice() / 364) * 100) / 100.0;
    }

    private static String initWorkerType(String workerType) {
        String wT;

        switch (workerType) {
            case "Cleaner":
            case "cleaner":
            case "Office Staff":
                wT = workerType;
                break;
            default:
                wT = "Housemaid";
                break;
        }

        return wT;
    }

    public Long getMaidId() {
        return maidId;
    }

    public String getMaidName() {
        return maidName;
    }

    public Date getEndorsementDate() {
        return endorsementDate;
    }

    public Date getTerminationDate() {
        return terminationDate;
    }

    public Integer getNumberOfDays() {
        return numberOfDays;
    }

    public Double getPremium() {
        return premium;
    }

    public Date getInsuranceStartDate() {
        return insuranceStartDate;
    }

    public void setInsuranceStartDate(Date insuranceStartDate) {
        this.insuranceStartDate = insuranceStartDate;
    }

    public Date getInsuranceEndDate() {
        return insuranceEndDate;
    }

    public void setInsuranceEndDate(Date insuranceEndDate) {
        this.insuranceEndDate = insuranceEndDate;
    }

    public String getWorkerType() {
        return workerType;
    }

    public void setWorkerType(String workerType) {
        this.workerType = workerType;
    }

    public String getEmiratesId() {
        return emiratesId;
    }

    public Double getBasmaCharge() {
        return basmaCharge;
    }

    public void setBasmaCharge(Double basmaCharge) {
        this.basmaCharge = basmaCharge;
    }

    public String getPassportNumber() {
        return passportNumber;
    }

    public void setPassportNumber(String passportNumber) {
        this.passportNumber = passportNumber;
    }

    // Jira ACC-4655
    public Double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Date getJoiningDate() {
        return joiningDate;
    }

    public void setJoiningDate(Date joiningDate) {
        this.joiningDate = joiningDate;
    }

    public Double getErpAmount(){
        return basmaCharge + premium;
    }
}