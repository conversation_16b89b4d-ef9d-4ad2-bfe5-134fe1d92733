package com.magnamedia.extra;

public class VatInfoWrapper {
    private Boolean includeWorkerSalary;
    private Double amountOfPayment;
    private Long prospectTypeId;
    private Long typeOfPaymentId;
    private Double workerSalary;

    public Double getWorkerSalary() {
        return workerSalary;
    }

    public void setWorkerSalary(Double workerSalary) {
        this.workerSalary = workerSalary;
    }

    public Long getProspectTypeId() {
        return prospectTypeId;
    }

    public void setProspectTypeId(Long prospectTypeId) {
        this.prospectTypeId = prospectTypeId;
    }

    public Boolean getIncludeWorkerSalary() {
        return includeWorkerSalary;
    }

    public void setIncludeWorkerSalary(Boolean includeWorkerSalary) {
        this.includeWorkerSalary = includeWorkerSalary;
    }

    public Double getAmountOfPayment() {
        return amountOfPayment;
    }

    public void setAmountOfPayment(Double amountOfPayment) {
        this.amountOfPayment = amountOfPayment;
    }


    public Long getTypeOfPaymentId() {
        return typeOfPaymentId;
    }

    public void setTypeOfPaymentId(Long typeOfPaymentId) {
        this.typeOfPaymentId = typeOfPaymentId;
    }

    public VatInfoWrapper() {
    }
}