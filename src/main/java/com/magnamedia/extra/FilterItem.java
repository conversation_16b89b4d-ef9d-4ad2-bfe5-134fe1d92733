package com.magnamedia.extra;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.helper.DateUtil;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Dec 27, 2017
 */
public class FilterItem {

    //*** Members ***//
    private String property;
    private List<String> alternatives;
    private String operation;
    private Object value;
    private Object secondValue;
    private Class propertyType;
    private Boolean withTime = false;
    private final Class baseClass = BaseEntity.class;

    //*** Constructors ***//
    public FilterItem() {
    }

    public FilterItem(String property,
                      String operation) {
        this.property = property;
        this.operation = operation;
    }

    public FilterItem(String property,
                      String operation,
                      Object value) {
        this.property = property;
        this.operation = operation;
        this.value = value;
    }

    public FilterItem(String property,
                      List<String> alternatives,
                      String operation,
                      Object value) {
        this.property = property;
        this.alternatives = alternatives;
        this.operation = operation;
        this.value = value;
    }

    public FilterItem(String property,
                      String operation,
                      String value,
                      Object secondValue) {
        this.property = property;
        this.operation = operation;
        this.value = value;
        this.secondValue = secondValue;
    }

    public FilterItem(String property,
                      List<String> alternatives,
                      String operation,
                      Object value,
                      Object secondValue) {
        this.property = property;
        this.alternatives = alternatives;
        this.operation = operation;
        this.value = value;
        this.secondValue = secondValue;
    }
    //**************************//

    //*** Get Select Filter ***//
    public SelectFilter getSelectFilter(Class parentClass) {
        return getSelectFilter(parentClass, true);
    }

    public SelectFilter getSelectFilter(Class parentClass, boolean castToEndDayValue) {

        if (this.value != null)
            this.castFieldValue(parentClass);

        switch (this.operation.toLowerCase()) {
            case ">=":
            case "<":
                if (this.value instanceof Date) {
                    this.value = DateUtil.getStartDayValue((Date) this.value);
                }
                return this.getSelectFilterAlternatives(this.value);

            case "<=":
            case ">":
                if (this.value instanceof Date && castToEndDayValue) {
                    this.setValue(DateUtil.getEndDayValue((Date) this.value));
                }
                return this.getSelectFilterAlternatives(this.value);

            case "between":
                if (this.value instanceof Date) {
                    this.setValue(DateUtil.getStartDayValue((Date) this.value));
                    this.setSecondValue(DateUtil.getEndDayValue((Date) this.secondValue));
                }
                this.setOperation(">=");
                SelectFilter left = this.getSelectFilterAlternatives(this.value);
                this.setOperation("<=");
                SelectFilter right = this.getSelectFilterAlternatives(this.secondValue);
                return left.and(right);

            case "like":
                return this.getSelectFilterAlternatives("%" + this.value + "%");
            case "not like":
                return this.getSelectFilterAlternatives("%" + this.value + "%");
            case "ends with":
                this.setOperation("like");
                return this.getSelectFilterAlternatives("%" + this.value);
            case "starts with":
                this.setOperation("like");
                return this.getSelectFilterAlternatives(this.value + "%");
            case "=":
                if ((this.value instanceof Date) &&
                        !(this.value instanceof java.sql.Date) &&
                        (!this.withTime)) {
                    this.setValue(DateUtil.getStartDayValue((Date) this.value));
                    this.setSecondValue(DateUtil.getEndDayValue((Date) this.value));
                    this.setOperation(">=");
                    SelectFilter left1 = this.getSelectFilterAlternatives(this.value);
                    this.setOperation("<=");
                    SelectFilter right1 = this.getSelectFilterAlternatives(this.secondValue);
                    return left1.and(right1);
                }
                return this.getSelectFilterAlternatives(this.value);
            case "<>":
            case "!=":
                if ((this.value instanceof Date) &&
                        !(this.value instanceof java.sql.Date) &&
                        (!this.withTime)) {
                    this.setValue(DateUtil.getStartDayValue((Date) this.value));
                    this.setSecondValue(DateUtil.getEndDayValue((Date) this.value));
                    this.setOperation("<");
                    SelectFilter left1 = this.getSelectFilterAlternatives(this.value);
                    this.setOperation(">");
                    SelectFilter right1 = this.getSelectFilterAlternatives(this.secondValue);
                    return left1.or(right1);
                }
                return this.getSelectFilterAlternatives(this.value);

            //equal not equal
            default:
                return this.getSelectFilterAlternatives(this.value);
        }
    }

    public SelectFilter getSelectFilterAlternatives(Object value) {
        SelectFilter selectFilter = new SelectFilter(this.property, this.operation, value);
        if ((this.alternatives != null) && (this.alternatives.size() > 0)) {
            for (String alternative : this.alternatives) {
                selectFilter.or(new SelectFilter(alternative, this.operation, value));
            }
        }
        return selectFilter;
    }

    public boolean castFieldValue(Class parentClass) {

        this.setPropertyType(this.getType(parentClass, this.getProperty()));
        if (this.getPropertyType() != null) {
            //Double
            if (this.propertyType == Double.class) {
                if (this.value instanceof Integer)
                    this.value = new Double((Integer) this.value);
                if ((this.secondValue != null) &&
                        (this.secondValue instanceof Integer))
                    this.secondValue = new Double((Integer) this.secondValue);
                if (this.value instanceof List) {
                    List<Double> listValues = new ArrayList<>();
                    for (Object v : (List<?>) this.value)
                        if (v instanceof Integer)
                            listValues.add(new Double((Integer) v));
                        else
                            listValues.add((Double) v);
                    this.value = listValues;
                }
                return true;
            }
            //Integer
            else if (this.propertyType == Integer.class) {
                this.value = (Integer) this.value;
                if (this.secondValue != null)
                    this.secondValue = (Integer) this.secondValue;
                if (this.value instanceof List) {
                    this.value = (List<Integer>) (List<?>) this.value;
                }
                return true;
            }
            //Long
            else if (this.propertyType == Long.class) {
                if (this.value instanceof Integer)
                    this.value = new Long((Integer) this.value);
                else if (this.value instanceof String)
                    this.value = Long.parseLong(this.value.toString());
                if (this.secondValue != null &&
                        this.secondValue instanceof Integer)
                    this.secondValue = new Long((Integer) this.secondValue);
                else if (this.value instanceof String)
                    this.secondValue = Long.parseLong(this.secondValue.toString());
                if (this.value instanceof List) {
                    List<Long> listValues = new ArrayList<>();
                    for (Object v : (List<?>) this.value)
                        if (v instanceof Integer)
                            listValues.add(new Long((Integer) v));
                        else if (v instanceof String)
                            listValues.add(Long.parseLong(v.toString()));
                        else
                            listValues.add((Long) v);
                    this.value = listValues;
                }
                return true;
            }
            //java.sql.Date
            else if (this.propertyType == java.sql.Date.class) {
                if (this.value != null && this.value instanceof String) {
                    try {
                        this.value = java.sql.Date.valueOf((String) this.value);
                        if (this.secondValue != null &&
                                this.secondValue instanceof String)
                            this.secondValue =
                                    java.sql.Date.valueOf(
                                            (String) this.secondValue);
                        return true;
                    } catch (Exception e) {
                        return false;
                    }
                }
            }
            //java.util.Date
            else if (propertyType == Date.class) {
                if (this.value != null && this.value instanceof String) {
                    try {
                        this.value = DateUtil.parseDateTimeDashed((String) this.value);
                        if (this.secondValue != null &&
                                this.secondValue instanceof String)
                            this.secondValue =
                                    DateUtil.parseDateTimeDashed(
                                            (String) this.secondValue);
                        this.withTime = true;
                        return true;
                    } catch (ParseException e) {
                    }
                    try {
                        this.value = DateUtil.parseDateDashed((String) this.value);
                        if (this.secondValue != null &&
                                this.secondValue instanceof String)
                            this.secondValue =
                                    DateUtil.parseDateDashed(
                                            (String) this.secondValue);
                        this.withTime = false;
                        return true;
                    } catch (ParseException e) {
                        return false;
                    }
                }
            }
            //PickListItem
            else if (this.propertyType == PicklistItem.class) {
//                this.value = (PicklistItem) this.value;
//                if (this.secondValue != null)
//                    this.secondValue = (PicklistItem) this.secondValue;
                return true;
            }
            //enums
            else if (this.propertyType.isEnum()) {
                return this.castEnumValue();
            }
        }
        return false;

    }

    private boolean castEnumValue() {
//        if (this.propertyType.equals(PaymentMethod.class))
//            if (this.value instanceof String){
//                    this.value = PaymentMethod.valueOf((String) this.value);
//                if ((this.secondValue != null) &&
//                        (this.secondValue instanceof String))
//                    this.secondValue = PaymentMethod.valueOf((String) this.secondValue);
//                return true;
//            }
//            else if(this.value instanceof List){
//                List<PaymentMethod> paymentMethods=new ArrayList<>();
//                for(String v : (List<String>)(List<?>)this.value)
//                    paymentMethods.add(PaymentMethod.valueOf(v));
//                this.value = paymentMethods;
//            }
//        if (this.propertyType.equals(PaymentStatus.class))
//            if (this.value instanceof String){
//                    this.value = PaymentStatus.valueOf((String) this.value);
//                if ((this.secondValue != null) &&
//                        (this.secondValue instanceof String))
//                    this.secondValue = PaymentStatus.valueOf((String) this.secondValue);
//                return true;
//            }
//            else if(this.value instanceof List){
//                List<PaymentStatus> paymentStatus=new ArrayList<>();
//                for(String v : (List<String>)(List<?>)this.value)
//                    paymentStatus.add(PaymentStatus.valueOf(v));
//                this.value = paymentStatus;
//            }
//        //Jirra  ACC-373
//        if (this.propertyType.equals(ExpectedWireTransferStatus.class))
//            if (this.value instanceof String){
//                    this.value = ExpectedWireTransferStatus.valueOf((String) this.value);
//                if ((this.secondValue != null) &&
//                        (this.secondValue instanceof String))
//                    this.secondValue = ExpectedWireTransferStatus.valueOf((String) this.secondValue);
//                return true;
//            }
//            else if(this.value instanceof List){
//                List<ExpectedWireTransferStatus> expectedWireTransferStatuses=new ArrayList<>();
//                for(String v : (List<String>)(List<?>)this.value)
//                    expectedWireTransferStatuses.add(ExpectedWireTransferStatus.valueOf(v));
//                this.value = expectedWireTransferStatuses;
//            }
//        //Jirra  ACC-456
//        if (this.propertyType.equals(DirectDebitStatus.class))
//            if (this.value instanceof String){
//                    this.value = DirectDebitStatus.valueOf((String) this.value);
//                if ((this.secondValue != null) &&
//                        (this.secondValue instanceof String))
//                    this.secondValue = DirectDebitStatus.valueOf((String) this.secondValue);
//                return true;
//            }
//            else if(this.value instanceof List){
//                List<DirectDebitStatus> directDebitStatuses=new ArrayList<>();
//                for(String v : (List<String>)(List<?>)this.value)
//                    directDebitStatuses.add(DirectDebitStatus.valueOf(v));
//                this.value = directDebitStatuses;
//            }
//        
//        //Jirra  ACC-456
//        if (this.propertyType.equals(VatType.class))
//            if (this.value instanceof String){
//                    this.value = VatType.valueOf((String) this.value);
//                if ((this.secondValue != null) &&
//                        (this.secondValue instanceof String))
//                    this.secondValue = VatType.valueOf((String) this.secondValue);
//                return true;
//            }
//            else if(this.value instanceof List){
//                List<VatType> vatTypes=new ArrayList<>();
//                for(String v : (List<String>)(List<?>)this.value)
//                    vatTypes.add(VatType.valueOf(v));
//                this.value = vatTypes;
//            }

        //Jirra  ACC-1087
        if (this.value instanceof String) {
            this.value = Enum.valueOf(this.propertyType, (String) this.value);
            if ((this.secondValue != null) &&
                    (this.secondValue instanceof String))
                this.secondValue = Enum.valueOf(this.propertyType, (String) this.secondValue);
            return true;
        } else if (this.value instanceof List) {
            List vatTypes = new ArrayList<>();
            for (String v : (List<String>) (List<?>) this.value)
                vatTypes.add(Enum.valueOf(this.propertyType, v));
            this.value = vatTypes;
        }
        return false;
    }

    private Class getType(Class parentClass, String property) {
        String[] propStrings = property.split("\\.", 2);
        Class propertyType = null;
        try {
            propertyType = parentClass.getDeclaredField(propStrings[0]).getType();
        } catch (NoSuchFieldException ex) {
        }
        try {
            if (propertyType == null)
                propertyType = this.baseClass.getDeclaredField(propStrings[0]).getType();
        } catch (NoSuchFieldException ex) {
        }

        if (propertyType != null && property.contains(".")) {
            return this.getType(propertyType, propStrings[1]);
        } else {
            return propertyType;
        }
    }
    //**************************//

    //*** Setters && Getters ***//
    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public List<String> getAlternatives() {
        return alternatives;
    }

    public void setAlternatives(List<String> alternatives) {
        this.alternatives = alternatives;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public Object getSecondValue() {
        return secondValue;
    }

    public void setSecondValue(Object secondValue) {
        this.secondValue = secondValue;
    }

    public Class getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(Class propertyType) {
        this.propertyType = propertyType;
    }

    @Override
    public String toString() {
        return property + operation + getValueAsString();
    }

    private String getValueAsString() {
        if (value instanceof Date) {
            return DateUtil.formatDateDashed(value);
        }

        return value.toString();
    }
}
