package com.magnamedia.extra;


import lombok.Builder;

/**
 * <AUTHOR>
 * @created 17/02/2025 - 12:54 AM
 * ACC-8101
 */

@Builder
public class DirectDebitReportCSV {

    private String contractId;
    private String status;
    private String ddId;
    private String flowId;
    private Boolean payingViaCc;
    private String category;
    private Double amount;
    private String startDate;
    private String endDate;
    private String paymentType;
    private String note;

    public String getContractId() { return contractId; }

    public void setContractId(String contractId) { this.contractId = contractId; }

    public String getDdId() { return ddId; }

    public void setDdId(String ddId) { this.ddId = ddId; }

    public String getFlowId() { return flowId; }

    public void setFlowId(String flowId) { this.flowId = flowId; }

    public Boolean getPayingViaCc() { return payingViaCc; }

    public void setPayingViaCc(Boolean payingViaCc) { this.payingViaCc = payingViaCc; }

    public String getCategory() { return category; }

    public void setCategory(String category) { this.category = category; }

    public Double getAmount() { return amount; }

    public void setAmount(Double amount) { this.amount = amount; }

    public String getStartDate() { return startDate; }

    public void setStartDate(String startDate) { this.startDate = startDate; }

    public String getEndDate() { return endDate; }

    public void setEndDate(String endDate) { this.endDate = endDate; }
    public String getPaymentType() { return paymentType; }

    public void setPaymentType(String paymentType) { this.paymentType = paymentType; }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    public String getNote() { return note; }

    public void setNote(String note) { this.note = note; }
}
