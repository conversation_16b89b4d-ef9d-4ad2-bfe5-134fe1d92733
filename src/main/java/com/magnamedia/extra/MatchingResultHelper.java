/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.magnamedia.entity.CreditCardStatement;
import com.magnamedia.entity.Ticket;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class MatchingResultHelper {
    private List<TicketMatchingLibrary.MatchedTicket> matchedTickets;
    private List<Ticket> tickets;
    private List<CreditCardStatement> statements;
    private Double fareSum;
    private Double debitSum;
    private Double creditSum;
    private int totalRecords;
//    private MatchingAccountingHelper accountingLists;

    public List<TicketMatchingLibrary.MatchedTicket> getMatchedTickets() {
        return matchedTickets;
    }

    public void setMatchedTickets(List<TicketMatchingLibrary.MatchedTicket> matchedTickets) {
        this.matchedTickets = matchedTickets;
    }

    public Double getFareSum() {
        return fareSum;
    }

    public void setFareSum(Double fareSum) {
        this.fareSum = fareSum;
    }

    public Double getDebitSum() {
        return debitSum;
    }

    public void setDebitSum(Double debitSum) {
        this.debitSum = debitSum;
    }

    public Double getCreditSum() {
        return creditSum;
    }

    public void setCreditSum(Double creditSum) {
        this.creditSum = creditSum;
    }

    public int getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(int totalRecords) {
        this.totalRecords = totalRecords;
    }

    public List<Ticket> getTickets() {
        return tickets;
    }

    public void setTickets(List<Ticket> tickets) {
        this.tickets = tickets;
    }

    public List<CreditCardStatement> getStatements() {
        return statements;
    }

    public void setStatements(List<CreditCardStatement> statements) {
        this.statements = statements;
    }

//    public MatchingAccountingHelper getAccountingLists() {
//        return accountingLists;
//    }
//
//    public void setAccountingLists(MatchingAccountingHelper accountingLists) {
//        this.accountingLists = accountingLists;
//    }
    
    
}
