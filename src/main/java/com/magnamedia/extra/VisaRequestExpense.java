package com.magnamedia.extra;


import com.magnamedia.module.type.PaymentType;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Mahfoud
 */
public class VisaRequestExpense {

    private Long visaRequestExpenseID;
    private String visaExpenseType;
    private String status;
    private String name;
    private Date creationDate;
    private EmployeeType employeeType;
    private boolean maidVisaAEContract;
    private String contractType;
    private Map<String, Object> housemaid;
    private Map<String, Object> officeStaff;
    private PaymentType paymentType;
    private String description;
    private Double amount;
    private String equation;
    private String referenceNumber;
    private Map<String, Object> fromBucket;
    private Map<String, Object> expense;
    private Map<String, Object> transaction;
    private List<Map> attachments;
    private String purpose;
    private String creator;
    private Long contractId;


    public VisaRequestExpense(
            Long id, String visaExpenseType, Date creationDate, Map<String, Object> housemaid,
            String description, Map<String, Object> fromBucket, Map<String, Object> expense, PaymentType paymentType,
            Double amount, String referenceNumber, EmployeeType employeeType, String contractType,
            boolean maidVisaAEContract, String equation, Map<String, Object> transaction, String status,
            Map<String, Object> officeStaff, String purpose, String creator, Long contractId) {

        setVisaRequestExpenseID(id);
        setVisaExpenseType(visaExpenseType);
        setCreationDate(creationDate);
        setName(getVisaExpenseType().replace("RequestExpense", "") + "_" + getVisaRequestExpenseID());
        setHousemaid(housemaid);
        setPaymentType(paymentType);
        setDescription(description);
        setFromBucket(fromBucket);
        setExpense(expense);
        setAmount((double) Math.round(amount * 100) / 100);
        setReferenceNumber(referenceNumber);
        setEmployeeType(employeeType);
        setContractType(contractType);
        setMaidVisaAEContract(maidVisaAEContract);
        setEquation(equation);
        setTransaction(transaction);
        setStatus(status);
        setOfficeStaff(officeStaff);
        setContractId(contractId);
        setPurpose(purpose);
        setCreator(creator);
    }

    public Long getVisaRequestExpenseID() { return visaRequestExpenseID; }

    public void setVisaRequestExpenseID(Long visaRequestExpenseID) { this.visaRequestExpenseID = visaRequestExpenseID; }

    public String getVisaExpenseType() { return visaExpenseType; }

    public void setVisaExpenseType(String visaExpenseType) { this.visaExpenseType = visaExpenseType; }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    public String getName() { return name; }

    public void setName(String name) { this.name = name; }

    public Date getCreationDate() { return creationDate; }

    public void setCreationDate(Date creationDate) { this.creationDate = creationDate; }

    public EmployeeType getEmployeeType() { return employeeType; }

    public void setEmployeeType(EmployeeType employeeType) { this.employeeType = employeeType; }

    public boolean isMaidVisaAEContract() { return maidVisaAEContract; }

    public void setMaidVisaAEContract(boolean maidVisaAEContract) { this.maidVisaAEContract = maidVisaAEContract; }

    public String getContractType() { return contractType; }

    public void setContractType(String contractType) { this.contractType = contractType; }

    public Map<String, Object> getHousemaid() { return housemaid; }

    public void setHousemaid(Map<String, Object> housemaid) { this.housemaid = housemaid; }

    public Map<String, Object> getOfficeStaff() { return officeStaff; }

    public void setOfficeStaff(Map<String, Object> officeStaff) { this.officeStaff = officeStaff; }

    public PaymentType getPaymentType() { return paymentType; }

    public void setPaymentType(PaymentType paymentType) { this.paymentType = paymentType; }

    public String getDescription() { return description; }

    public void setDescription(String description) { this.description = description; }

    public Double getAmount() { return amount; }

    public void setAmount(Double amount) { this.amount = amount; }

    public String getEquation() { return equation; }

    public void setEquation(String equation) { this.equation = equation; }

    public String getReferenceNumber() { return referenceNumber; }

    public void setReferenceNumber(String referenceNumber) { this.referenceNumber = referenceNumber; }

    public Map<String, Object> getFromBucket() { return fromBucket; }

    public void setFromBucket(Map<String, Object> fromBucket) { this.fromBucket = fromBucket; }

    public Map<String, Object> getExpense() { return expense; }

    public void setExpense(Map<String, Object> expense) { this.expense = expense; }

    public Map<String, Object> getTransaction() { return transaction; }

    public void setTransaction(Map<String, Object> transaction) { this.transaction = transaction; }

    public Long getContractId() { return contractId; }

    public void setContractId(Long contractId) { this.contractId = contractId; }

    public List<Map> getAttachments() { return attachments; }

    public void setAttachments(List<Map> attachments) { this.attachments = attachments; }

    public String getPurpose() { return purpose; }

    public void setPurpose(String purpose) { this.purpose = purpose; }

    public String getCreator() { return creator; }

    public void setCreator(String creator) { this.creator = creator; }
}