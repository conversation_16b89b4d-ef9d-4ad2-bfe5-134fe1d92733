package com.magnamedia.extra;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.dto.salesbinder.*;
import com.magnamedia.module.AccountingModule;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Mohammad Nosairat (Jan 25, 2021)
 */
@Service
public class SalesBinderApiClient {
    
    private Logger logger = Logger.getLogger(SalesBinderApiClient.class.getName());
    
    private static String salesBinderUrl;
    private static int salesBinderCallsCount = 0;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    public List<SupplierDto> getSalesBinderSuppliers() {
        List<SupplierDto> categorie = new ArrayList<>();
        int numOfPages = 1;
        boolean numOfPageUpdated = false;
        
        for (int page = 1; page <= numOfPages; page++) {
            checkAndSleepSalesBinderRequest();
            logger.log(Level.INFO, "Fetching Data in getSalesBinderSuppliers");
            SupplierResponseDto supplierResponseDto = getSalesBinderRestTemplate().getForObject(salesBinderUrl + "customers.json?contextId=10&page=" + page, SupplierResponseDto.class);

            if (!numOfPageUpdated) {
                numOfPages = Integer.parseInt(supplierResponseDto.getPages());
                numOfPageUpdated = true;
            }
            categorie.addAll(new ArrayList<>(Arrays.asList(supplierResponseDto.getCustomers()[0])));
        }
        return categorie;
    }

    public List<CategoryDto> getSalesBinderCategories() {
        List<CategoryDto> categorie = new ArrayList<>();
        int numOfPages = 1;
        boolean numOfPageUpdated = false;
        
        for (int page = 1; page <= numOfPages; page++) {
            checkAndSleepSalesBinderRequest();
            logger.log(Level.INFO, "Fetching Data in getSalesBinderCategories");
            CategoryResponseDto categoryResponseDto = getSalesBinderRestTemplate().getForObject(salesBinderUrl + "categories.json?page=" + page, CategoryResponseDto.class);

            if (!numOfPageUpdated) {
                numOfPages = Integer.parseInt(categoryResponseDto.getPages());
                numOfPageUpdated = true;
            }
            categorie.addAll(new ArrayList<>(Arrays.asList(categoryResponseDto.getCategories()[0])));
        }
        return categorie;
    }

    public List<ItemDto> getSalesBinderItems() {
        List<ItemDto> items = new ArrayList<>();
        int numOfPages = 1;
        boolean numOfPageUpdated = false;
        
        for (int page = 1; page <= numOfPages; page++) {
            checkAndSleepSalesBinderRequest();
            logger.log(Level.INFO, "Fetching Data in getSalesBinderItems");
            ItemResponseDto itemResponseDto = getSalesBinderRestTemplate().getForObject(salesBinderUrl + "items.json?page=" + page, ItemResponseDto.class);
            
            if (!numOfPageUpdated) {
                numOfPages = Integer.parseInt(itemResponseDto.getPages());
                numOfPageUpdated = true;
            }
            items.addAll(new ArrayList<>(Arrays.asList(itemResponseDto.getItems()[0])));
        }
        return items;
    }


    private RestTemplate getSalesBinderRestTemplate() {
        salesBinderUrl = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.SALES_BINDER_API_URL);
        RestTemplate template = new RestTemplate();
        template.getInterceptors().add(
                new BasicAuthorizationInterceptor(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.SALES_BINDER_API_KEY), "x"));
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(objectMapper);
        template.setMessageConverters(Collections.singletonList(converter));
        return template;
    }

    public void createDocumentForPurchaseOrder(List<PurchaseOrder> purchaseOrders) {
        try {
            List<DocumentHolderDto> documents = purchaseOrders.stream().map(DocumentHolderDto::new).collect(Collectors.toList());
            RestTemplate restTemplate = getSalesBinderRestTemplate();
            documents.forEach(d -> {
                checkAndSleepSalesBinderRequest();
                logger.log(Level.INFO, "Fetching Data in createDocumentForPurchaseOrder");
                Object response = restTemplate.postForObject(salesBinderUrl + "documents.json", d, Object.class);
            });
        } catch (Exception e) {
            throw e;
        }
    }
    
    private void checkAndSleepSalesBinderRequest() {
        logger.log(Level.INFO, "{0}; salesBinderCallsCount: {1}", 
                new Object[]{new DateTime().toString("yyyy-MM-dd HH:mm:ss"), salesBinderCallsCount});
        
        if(salesBinderCallsCount > 10) {
            try {
                logger.log(Level.INFO, "15 requests sent before -> sleeping 5 seconds");
                Thread.sleep(10000);
                
                salesBinderCallsCount = 0;
            } catch (InterruptedException ex) {
                logger.log(Level.SEVERE, null, ex);
            }
        }
        
        salesBinderCallsCount ++;
    }
}
