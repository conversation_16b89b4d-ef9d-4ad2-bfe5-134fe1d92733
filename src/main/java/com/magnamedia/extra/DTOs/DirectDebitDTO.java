package com.magnamedia.extra.DTOs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.DirectDebitType;

import java.util.Date;

/**
 * Created by Mamon.Masod on 6/30/2021.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DirectDebitDTO {
    Double amount;
    Date fromDate;
    Date toDate;
    DirectDebitType type;
    PicklistItem oneTimePaymentType;
    PicklistItem subType;
    Long replacedBouncedPaymentId;
    Boolean generatingPaymentFromOldCCReplacement;
    Double additionalDiscountAmount;
    Double moreAdditionalDiscount;
    Double additionAmount;
    boolean proRated = false;

    public DirectDebitDTO() {
    }
    
    public DirectDebitDTO(Double amount, Date fromDate, Date toDate, DirectDebitType type, PicklistItem oneTimePaymentType, Long replacedBouncedPaymentId) {
        this.amount = amount;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.type = type;
        this.oneTimePaymentType = oneTimePaymentType;
        this.replacedBouncedPaymentId = replacedBouncedPaymentId;
    }
    
    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getFromDate() {
        return fromDate;
    }

    public void setFromDate(Date fromDate) {
        this.fromDate = fromDate;
    }

    public Date getToDate() {
        return toDate;
    }

    public void setToDate(Date toDate) {
        this.toDate = toDate;
    }

    public DirectDebitType getType() {
        return type;
    }

    public void setType(DirectDebitType type) {
        this.type = type;
    }

    public PicklistItem getOneTimePaymentType() {
        return oneTimePaymentType;
    }

    public void setOneTimePaymentType(PicklistItem oneTimePaymentType) {
        this.oneTimePaymentType = oneTimePaymentType;
    }

    public Long getReplacedBouncedPaymentId() {
        return replacedBouncedPaymentId;
    }

    public void setReplacedBouncedPaymentId(Long replacedBouncedPaymentId) {
        this.replacedBouncedPaymentId = replacedBouncedPaymentId;
    }

    public Boolean getGeneratingPaymentFromOldCCReplacement() {
        return generatingPaymentFromOldCCReplacement != null && generatingPaymentFromOldCCReplacement;
    }

    public void setGeneratingPaymentFromOldCCReplacement(Boolean generatingPaymentFromOldCCReplacement) {
        this.generatingPaymentFromOldCCReplacement = generatingPaymentFromOldCCReplacement;
    }

    public PicklistItem getSubType() { return subType; }

    public void setSubType(PicklistItem subType) { this.subType = subType; }

    public Double getAdditionalDiscountAmount() { return additionalDiscountAmount; }

    public void setAdditionalDiscountAmount(Double additionalDiscountAmount) { this.additionalDiscountAmount = additionalDiscountAmount; }

    public Double getMoreAdditionalDiscount() { return moreAdditionalDiscount == null ? 0.0 : moreAdditionalDiscount; }

    public void setMoreAdditionalDiscount(Double moreAdditionalDiscount) { this.moreAdditionalDiscount = moreAdditionalDiscount; }

    public Double getAdditionAmount() { return additionAmount; }

    public void setAdditionAmount(Double additionAmount) { this.additionAmount = additionAmount; }

    public boolean isProrated() { return proRated; }

    public void setProRated(boolean proRated) { this.proRated = proRated; }
}