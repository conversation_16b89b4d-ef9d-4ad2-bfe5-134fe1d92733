/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class BasicSalaryBreakDown implements Serializable{

    private Double basicSalary;
    private Double overTime;
    private Double monthlyLoan;
    private Double holidayPay;
    private Double airFare;
//       private Double basicMonthlhPay;
//       private Double OverTimeAndHolidayPay;

    public BasicSalaryBreakDown() {
        this.basicSalary = 0d;
        this.overTime = 0d;
        this.monthlyLoan = 0d;
        this.holidayPay = 0d;
        this.airFare = 0d;
    }

    public BasicSalaryBreakDown(Double basicSalary, Double overTime, Double monthlyLoan, Double holidayPay, Double airFare) {
        this.basicSalary = basicSalary;
        this.overTime = overTime;
        this.monthlyLoan = monthlyLoan;
        this.holidayPay = holidayPay;
        this.airFare = airFare;
    }

    public Double getBasicSalary() {
        if (basicSalary == null) {
            return 0.0;
        }
        return basicSalary;
    }

    public void setBasicSalary(Double basicSalary) {
        this.basicSalary = basicSalary;
    }

    public Double getOverTime() {
        if (overTime == null) {
            return 0.0;
        }
        return overTime;
    }

    public void setOverTime(Double overTime) {
        this.overTime = overTime;
    }

    public Double getMonthlyLoan() {
        if (monthlyLoan == null) {
            return 0.0;
        }
        return monthlyLoan;
    }

    public void setMonthlyLoan(Double monthlyLoan) {
        this.monthlyLoan = monthlyLoan;
    }

    public Double getHolidayPay() {
        if (holidayPay == null) {
            return 0.0;
        }
        return holidayPay;
    }

    public void setHolidayPay(Double holidayPay) {
        this.holidayPay = holidayPay;
    }

    public Double getAirFare() {
        if (airFare == null) {
            return 0.0;
        }
        return airFare;
    }

    public void setAirFare(Double airFare) {
        this.airFare = airFare;
    }

    
    @JsonIgnore
    public Double getBasicMonthlhPay() {
        return getAirFare() + getBasicSalary() + getHolidayPay() + getOverTime() + getMonthlyLoan();
    }

//        public void setBasicMonthlhPay(Double basicMonthlhPay) {
//            this.basicMonthlhPay = basicMonthlhPay;
//        }
    
    
    @JsonIgnore
    public Double getOverTimeAndHolidayPay() {

        if ((getHolidayPay() == 0.0 && getOverTime() > 0) || (getHolidayPay() > 0 && getOverTime() == 0.0)) {
            return getHolidayPay() != 0.0 ? getHolidayPay() : getOverTime();
        }
        return 0.0;
    }

//    public void setOverTimeAndHolidayPay(Double OverTimeAndHolidayPay) {
//        this.OverTimeAndHolidayPay = OverTimeAndHolidayPay;
//    }
}
