package com.magnamedia.extra;

public enum MvSmsTemplateCode {
    MV_PAYMENT_EXPIRY_4_1_1_SMS,
    MV_PAYMENT_EXPIRY_4_1_2_SMS,

    MV_ACCOUNTING_NOT_OWED_MONEY_FROM_CLIENT_8_1_2_SMS, //row 13 "If we receive an amount from the client when he doesn't owe us any amount (IF client has DD) if he already cancelled"
    MV_ACCOUNTING_WRONGLY_CHARGED_MONEY_ON_CLIENT_8_1_3_SMS, //row 14 If we receive an amount from the client when he doesn't owe us any amount (IF client has DD), IF his contract is “scheduled for termination” AND we don’t need that amount
    // ACC-5214
    MV_DIRECT_DEBIT_GENERATION_PLAN_INSURANCE_SMS,
    MV_DIRECT_DEBIT_GENERATION_PLAN_SAME_DAY_RECRUITMENT_FEE_SMS,
    MV_DIRECT_DEBIT_GENERATION_PLAN_OTHER_DD_TYPE_SMS,
    MV_DD_PENDING_INFO_SMS,
    MV_PAYTABS_THANKS_MESSAGE_SMS,

    // ACC-4591
    MV_ACCOUNTING_PAY_ACCOMMODATION_FEE_SMS,
    MV_ACCOUNTING_PAY_CC_TO_MV_SMS,
    MV_ACCOUNTING_PAY_MONTHLY_PAYMENT_SMS,
    MV_ACCOUNTING_PAY_OVERSTAY_FEES_SMS,
    MV_ACCOUNTING_PAY_PCR_TEST_SMS,
    MV_ACCOUNTING_PAY_URGENT_VISA_CHARGES_SMS,
    MV_ACCOUNTING_PAY_INSURANCE_SMS,
    MV_ACCOUNTING_PAY_OTHER_PAYMENTS_TYPES_SMS,
}
