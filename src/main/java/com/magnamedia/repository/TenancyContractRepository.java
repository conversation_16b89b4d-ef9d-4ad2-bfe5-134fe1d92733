package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.NonClientPDC;
import com.magnamedia.entity.TenancyContract;
import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>> 
 * Created at May 9, 2018
 */
@Repository
public interface TenancyContractRepository extends BaseRepository<TenancyContract> {
     
    List<TenancyContract> findByEndDateBetween( Date first,  Date second);
    List<TenancyContract> findByEndDateBetweenAndActive( Date first,  Date second, boolean active);

}
