package com.magnamedia.repository;

import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.module.type.DDMessagingType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


@Repository
public interface DisablePushNotificationRepository extends BaseRepository<PushNotification> {

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.recepientId = ?1 AND " +
            "((d is not null and d.event = ?2) or (db_dd is not null and db_dd.event = ?2)) and " +
            "p.disabled = false AND p.recepientType = 'Client' AND " +
            "(p.relatedEntityId1 = ?3 and p.relatedEntityType1 = 'Contract') and p.location = 'HOME'")
    List<PushNotification> findActiveHomeNotificationsByDDMessagingType(String clientId, DDMessagingType type, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.recepientId = ?1 AND p.disabled = false AND p.recepientType = 'Client' AND " +
            "((d is not null and d.event in (?2)) or (db_dd is not null and db_dd.event in (?2))) and " +
            "(p.relatedEntityId1 = ?3 and p.relatedEntityType1 = 'Contract') and p.location = 'HOME'")
    List<PushNotification> findActiveHomeNotificationsByDDMessagingType(String clientId, List<DDMessagingType> types, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.recepientId = ?1 AND " +
                "((d is not null and d.event = ?2) or (db_dd is not null and db_dd.event = ?2)) and " +
                "p.disabled = false AND p.recepientType = 'Client' AND " +
            "(p.relatedEntityId1 = ?3 and p.relatedEntityType1 = 'Contract')")
    List<PushNotification> findActiveNotificationsByDDMessagingType(String clientId, DDMessagingType type, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.recepientId = ?1 AND p.disabled = false AND p.recepientType = 'Client' AND " +
                "((d is not null and d.event in (?2)) or (db_dd is not null and db_dd.event in (?2))) and " +
            "(p.relatedEntityId1 = ?3 and p.relatedEntityType1 = 'Contract')")
    List<PushNotification> findActiveNotificationsByDDMessagingType(String clientId, List<DDMessagingType> types, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.disabled = false AND p.recepientType = 'Client' AND " +
                "((d is not null and d.event = ?1) or (db_dd is not null and db_dd.event = ?1))")
    Page<PushNotification> findActiveNotificationsByDDMessagingType(DDMessagingType type, Pageable pageable);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "inner join Payment pa on pa.id = p.ownerId and p.ownerType = 'Payment' " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.id > ?1 and p.disabled = false AND p.recepientType = 'Client' AND " +
                "((d is not null and d.event = 'BouncedPayment') or (db_dd is not null and db_dd.event = 'BouncedPayment')) and " +
                "p.context is not null and trim(replace(replace(p.context, '{', ''), '}', '')) != '' and " +
                "(pa.replaced = true or pa.status = 'DELETED')")
    Page<PushNotification> findActiveNotificationsForBouncedFlow(Long last, Pageable pageable);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "inner join DirectDebit dd on dd.id = p.ownerId and p.ownerType = 'DirectDebit' " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.id > ?1 and p.disabled = false AND p.recepientType = 'Client' AND " +
            "((d is not null and d.event = 'DirectDebitRejected') or (db_dd is not null and db_dd.event = 'DirectDebitRejected')) and " +
            "((dd.category = 'B' and dd.status = 'CANCELED') OR (dd.category = 'A' and dd.MStatus = 'CANCELED'))")
    Page<PushNotification> findActiveNotificationsForRejectionFlow(Long lastId, Pageable pageable);

    @Query("SELECT p FROM PushNotification p " +
            "WHERE p.recepientId = ?1 and p.creatorModule = ?2 and p.disabled = false and  p.recepientType = 'Client' and " +
                "(p.relatedEntityId1 = ?3 and p.relatedEntityType1 = 'Contract')")
    List<PushNotification> findActiveNotificationsByAccountingModuleAndClient(String clientId, Long creatorModuleId, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
            "WHERE p.disabled = false AND p.recepientType = 'Client' AND p.recepientId is not null AND " +
                "p.recepientId <> '-1'  AND p.type.code IN (?1)")
    Page<PushNotification> findActiveNotifications(List<String> types, Pageable pageable);

    @Query("SELECT p FROM PushNotification p " +
            "WHERE p.id > ?1 and p.disabled = false AND p.recepientType = 'Client' AND p.recepientId is not null AND " +
            "p.recepientId <> '-1'  AND p.type.code IN (?2)")
    Page<PushNotification> findActiveNotifications(Long lastId, List<String> types, Pageable pageable);

    @Query("SELECT p " +
            "FROM PushNotification p " +
            "WHERE p.id > ?1 and p.type.code IN (?2) and p.disabled = false AND p.recepientType = 'Client' and " +
                "exists ( select 1 from ContractPaymentConfirmationToDo c where c.contractPaymentTerm.contract.client.id = p.recepientId " +
                        "and c.creationDate > p.creationDate and p.ownerType = 'Contract' and c.contractPaymentTerm.contract.id = p.ownerId)")
    Page<PushNotification> findActiveNotificationsAndProofTransferSubmit(
            Long lastId, List<String> types, Pageable pageable);

    @Query("SELECT p " +
            "FROM PushNotification p " +
            "WHERE p.disabled = false AND p.recepientType = 'Client' AND p.recepientId = ?1 AND " +
                "(p.relatedEntityId1 = ?2 and p.relatedEntityType1 = 'Contract') and p.type.code IN (?3) " +
            "order by p.creationDate desc")
    List<PushNotification> findActiveNotifications(String clientId, Long contractId, List<String> types);

    @Query("select p " +
            "from PushNotification p " +
            "where p.recepientType = 'Client' and p.recepientId = ?1 and " +
            "p.relatedEntityId1 = ?2 and p.relatedEntityType1 = 'Contract' and " +
            "p.type.code in (?3) and (p.received = 0 or p.location = 'Home')")
    List<PushNotification> findActiveNotificationsAndLocationHome(String clientId, Long contractId, List<String> types);

    @Query("SELECT p FROM PushNotification p " +
           "inner join Template t on p.type.code = t.name " +
           "left join DDMessaging d on d.clientTemplate = t " +
           "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
           "WHERE p.recepientId = ?1 AND p.recepientType = 'Client' and p.disabled = false and " +
                "((d is not null and d.scheduleTermCategory in ('EToday', 'GToday')) or " +
                    "(db_dd is not null and db_dd.scheduleTermCategory in ('EToday', 'GToday'))) and " +
                "(p.relatedEntityId1 = ?2 and p.relatedEntityType1 = 'Contract')")
    List<PushNotification> findActiveNotificationsByScheduleTermCategory(String clientId, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.maidTemplate = t " +
            "left join DDBankMessaging db on db.maidTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "WHERE p.recepientId = ?1 and p.location = 'Home' and " +
                "((d is not null and d.scheduleTermCategory in ('EToday', 'GToday')) or " +
                "(db_dd is not null and db_dd.scheduleTermCategory in ('EToday', 'GToday'))) and " +
                "(p.relatedEntityId1 = ?2 and p.relatedEntityType1 = 'Contract')")
    List<PushNotification> findActiveNotificationsByScheduleTermCategoryForHousemaid(String recepientId, Long contractId);

    @Query("SELECT p FROM PushNotification p " +
        "inner join Template t on p.type.code = t.name " +
        "left join ChannelSpecificSetting n on n.template = t and n.type = 'Notification' " +
        "left join TemplateChannelParameter nPara on nPara.setting = n " +
        "left join ChannelSpecificSetting s on s.template = t and s.type = 'Sms' " +
        "left join TemplateChannelParameter sPara on sPara.setting = s " +
        "WHERE (sPara.name = 'link_send_dd_details' or nPara.name in ('signing_offer_clicking_here', 'paying_via_credit_card_sign_now_click_here')) " +
            "and p.disabled = false AND p.recepientType = 'Client' and p.recepientId = ?1 and " +
            "(p.relatedEntityId1 = ?2 and p.relatedEntityType1 = 'Contract')")
    List<PushNotification> findActiveNotificationsBySignLink(String clientId, Long contractId);

    @Query("select p.id from PushNotification p " +
            "inner join Contract c on p.relatedEntityId1 = c.id and p.relatedEntityType1 = 'Contract' " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "where p.id > ?1 and c.payingViaCreditCard = true and c.status ='ACTIVE' and " +
                "p.disabled = false and p.recepientType = 'Client' and  " +
                "((d is not null and d.event = 'ClientsPayingViaCreditCard' and d.subType = 'DD_SIGNING_OFFER') or " +
                "(db_dd is not null and db_dd.event = 'ClientsPayingViaCreditCard' and db_dd.subType = 'DD_SIGNING_OFFER')) and " +
                "exists( select 1 from DirectDebit dd " +
                "inner join ContractPaymentTerm cpt on cpt.id = dd.contractPaymentTerm.id and cpt.contract.id = c.id " +
                "where (dd.category = 'B' and  (dd.status not in ('CANCELED', 'EXPIRED', 'PENDING_FOR_CANCELLATION') or  " +
                                "(dd.status = 'CONFIRMED' and " +
                                        "not exists( select 1 from DirectDebitCancelationToDo todo " +
                                                    "where todo.directDebit = dd and todo.stopped = false and todo.completed = false)))) or " +
                            "(dd.category = 'A' and (dd.MStatus not in ('CANCELED', 'EXPIRED', 'PENDING_FOR_CANCELLATION') or " +
                                "(dd.MStatus = 'CONFIRMED' and not exists( select 1 from DirectDebitCancelationToDo todo " +
                                                    "where todo.directDebit = dd and todo.stopped = false and todo.completed = false)))))")
    Page<Long> findActiveSigningOfferWithActiveDds(Long lastId, Pageable pageable);

    @Query("select p from PushNotification p " +
            "inner join Contract c on p.relatedEntityId1 = c.id and p.relatedEntityType1 = 'Contract' " +
            "inner join Template t on p.type.code = t.name " +
            "left join DDMessaging d on d.clientTemplate = t " +
            "left join DDBankMessaging db on db.clientTemplate = t " +
            "left join DDMessaging db_dd on db.ddMessaging = db_dd " +
            "where p.id > ?1 and c.payingViaCreditCard = true and c.status ='ACTIVE' and " +
                "p.recepientType = 'Client' and  " +
                "((d is not null and d.event = 'ClientsPayingViaCreditCard' and d.subType = 'DD_SIGNING_OFFER') or " +
                "(db_dd is not null and db_dd.event = 'ClientsPayingViaCreditCard' and db_dd.subType = 'DD_SIGNING_OFFER')) and " +
                "not exists( select 1 from DirectDebit dd " +
                    "inner join ContractPaymentTerm cpt on cpt.id = dd.contractPaymentTerm.id and cpt.contract.id = c.id " +
                    "where (dd.category = 'B' and  (dd.status not in ('CANCELED', 'EXPIRED', 'PENDING_FOR_CANCELLATION') or  " +
                                "(dd.status = 'CONFIRMED' and " +
                                        "not exists( select 1 from DirectDebitCancelationToDo todo " +
                                                    "where todo.directDebit = dd and todo.stopped = false and todo.completed = false)))) or " +
                            "(dd.category = 'A' and (dd.MStatus not in ('CANCELED', 'EXPIRED', 'PENDING_FOR_CANCELLATION') or " +
                                "(dd.MStatus = 'CONFIRMED' and not exists( select 1 from DirectDebitCancelationToDo todo " +
                                                    "where todo.directDebit = dd and todo.stopped = false and todo.completed = false))))) " +
            "group by c.id " +
            "having p.id = max(p.id) and sum(p.disabled) = count(p.id)")
    Page<PushNotification> findLastInActiveSigningOfferWithNoActiveDDs(Long lastId, Pageable pageable);

    @Query("SELECT p FROM PushNotification p " +
            "inner join Template t on p.type.code = t.name " +
            "inner join Contract c on c.client.id = p.recepientId and p.recepientType = 'Client' " +
            "left join ChannelSpecificSetting n on n.template = t and n.type = 'Notification' " +
            "left join TemplateChannelParameter nPara on nPara.setting = n " +
            "left join ChannelSpecificSetting s on s.template = t and s.type = 'Sms' " +
            "left join TemplateChannelParameter sPara on sPara.setting = s " +
            "WHERE p.id > ?1 and (sPara.name = 'link_send_dd_details' or nPara.name = 'signing_offer_clicking_here') and " +
                "p.disabled = false and p.context like concat('%' , c.id , '%') and c.signingPaperMode = true and " +
                "exists( select 1 from GraphicDesignerToDo todo where todo.contractId = c.id and " +
                    "todo.creationDate > p.creationDate and " +
                    "todo.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and todo.completed = false)")
    Page<PushNotification> findActiveNotificationsBySignLinkWithOpenGD(Long lastId, Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT p.* FROM `_PUSH_NOTIFICATIONS` p " +
                    "    inner join PICKLISTS_ITEMS i ON p.`_TYPE` = i.ID " +
                    "    inner join TEMPLATES t on i.CODE = t.NAME " +
                    "    left join DDMESSAGINGS d on d.CLIENT_TEMPLATE_ID = t.ID " +
                    "    left join DDBANKMESSAGINGS db on db.CLIENT_TEMPLATE_ID = t.ID " +
                    "    left join DDMESSAGINGS db_dd on db.DD_MESSAGING_ID = db_dd.ID " +
                    "    left join PAYMENTS py ON p.OWNER_TYPE = py.ENTITY_TYPE AND p.OWNER_ID = py.ID " +
                    "WHERE p.IS_DISABLED = false AND p.RECEPIENT_TYPE = 'Client' AND " +
                    "((d.ID is not null and d.EVENT = 'BouncedPayment') or (db_dd.ID is not null and db_dd.EVENT = 'BouncedPayment')) " +
                    "  AND p._CONTEXT IS NOT NULL " +
                    "  AND p._CONTEXT != '{}' " +
                    "  AND p._CONTEXT != '{ }' " +
                    "  AND p._CONTEXT NOT LIKE '%link_send_dd_details%' " +
                    "  AND p._CONTEXT NOT LIKE '%payment_bounced_sign_now_clicking_here%' " +
                    "  AND p._CONTEXT NOT LIKE '%bounced_payment_click%' " +
                    "AND (py.REPLACED or py.STATUS = 'DELETED' or py.STATUS = 'CANCELED') " +
                    "AND p.ID > ?1 " +
                    "LIMIT 200")
    List<PushNotification> findForAcc7505DataCorrection_bouncedPayment(Long id);

    @Query(nativeQuery = true,
            value = "SELECT p.* FROM `_PUSH_NOTIFICATIONS` p " +
                    "WHERE p.CREATOR_MODULE = 8 AND p.RECEPIENT_ID = '-1' AND p.IS_DISABLED = 0 " +
                    "AND p.ID > ?1 " +
                    "LIMIT 200")
    List<PushNotification> findForAcc7505DataCorrection_oldSentToSpouse(Long id);

    @Query("select p from PushNotification p " +
            "where p.ownerId = ?1 and p.ownerType = ?2 and p.disabled = false")
    List<PushNotification> findActiveNotificationsByOwner(Long ownerId, String ownerType);

    @Query("select p.id from PushNotification p " +
            "where p.ownerId in ?1 and p.ownerType = 'ContractPaymentConfirmationToDo' and p.disabled = false")
    List<Long> findActiveNotificationsByDDMessagingTypeAndOwnerTypeAndOwnerId(List<String> todoIds);

    @Query("select pn from PushNotification pn " +
            "where pn.recepientId = ?1 and pn.recepientType = ?2 and (pn.location <> 'INBOX' or pn.disabled = false) and " +
            "pn.ownerId = ?3 and pn.ownerType = ?4 and pn.type.code in ?5")
    List<PushNotification> findByRecepientTypeAndOwnerIdAndOwnerTypeAndTypeIn(
            String var1, String var2, Long ownerId, String ownerType, ArrayList<String> types);
}