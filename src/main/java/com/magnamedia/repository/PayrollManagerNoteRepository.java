/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.PayrollManagerNote;
import com.magnamedia.entity.OfficeStaff;

import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Repository
public interface PayrollManagerNoteRepository extends BaseRepository<PayrollManagerNote>{
    List<PayrollManagerNote> findByHousemaid(Housemaid housemaid);
    List<PayrollManagerNote> findByOfficeStaff(OfficeStaff officeStaff);
        //Jirra ACC-1085
    List<PayrollManagerNote> findByNotFinal(boolean notFinal);

    //Jirra ACC-1561
    List<PayrollManagerNote> findByHousemaidAndNoteDateAndAdditionReasonIn(Housemaid housemaid, Date noteDate, List<PicklistItem> additionReason);
    PayrollManagerNote findFirstByHousemaidAndNoteTypeOrderByCreationDateDesc(Housemaid housemaid, PayrollManagerNote.ManagerNoteType noteType);
    PayrollManagerNote findFirstByOfficeStaffAndNoteTypeOrderByCreationDateDesc(OfficeStaff officeStaff, PayrollManagerNote.ManagerNoteType noteType);
}
