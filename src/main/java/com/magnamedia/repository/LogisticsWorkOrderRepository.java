package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.LogisticsWorkOrder;
import com.magnamedia.module.type.BillSubStatus;
import com.magnamedia.module.type.BillType;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


@Repository
public interface LogisticsWorkOrderRepository extends BaseRepository<LogisticsWorkOrder> {
//    List<LogisticsWorkOrder> findByBillTypeAndLeaveOnBetween(BillType billType , Date d1 , Date d2 );
//    List<LogisticsWorkOrder> findByLeaveOnBetweenAndBillTypeIn( Date d1 , Date d2 , List<BillType> types);

    List<LogisticsWorkOrder> findByBillTypeAndLeaveOnBetweenAndBillSubStatusIn(BillType billType , Date d1 , Date d2, List<BillSubStatus> billSubStatus);
    List<LogisticsWorkOrder> findByLeaveOnBetweenAndBillTypeInAndBillSubStatusIn(Date d1, Date d2, List<BillType> types, List<BillSubStatus> billSubStatus);
}
