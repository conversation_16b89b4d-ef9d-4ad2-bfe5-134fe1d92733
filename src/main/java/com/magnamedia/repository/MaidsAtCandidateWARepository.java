package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.MaidsAtCandidateWA;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */

@Repository
public interface MaidsAtCandidateWARepository extends BaseRepository<MaidsAtCandidateWA>{

    MaidsAtCandidateWA findFirstByName(String name);

    List<MaidsAtCandidateWA> findByMobileNumberOrWhatsappNumber(String mobileNumber, String whatsappNumber);
}
