package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankStatementFile;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created At Apr 18, 2020
 *
 **/


@Repository
public interface BankStatementFileRepository extends BaseRepository<BankStatementFile> {

    public List<BankStatementFile> findByCreationDateBetween(Date fromDate, Date toDate);

    @Query("select count (b.id) > 0 from BankStatementFile b " +
            "where b.resolved = false and b.deleted = false and b.creationDate >= ?1")
    boolean existsUnResolvedBankStatementFile(Date creationDate);
}
