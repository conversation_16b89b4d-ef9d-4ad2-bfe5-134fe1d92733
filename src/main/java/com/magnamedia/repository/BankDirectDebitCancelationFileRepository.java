package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
@Repository
public interface BankDirectDebitCancelationFileRepository
        extends BaseRepository<BankDirectDebitCancelationFile> {

    @Query("select f from BankDirectDebitCancelationFile f " +
            "where f.addedByRPA = true " +
            "order by f.creationDate desc")
    Page<BankDirectDebitCancelationFile> getAllBankDirectDebitCancellationFiles(Pageable pageable);
}