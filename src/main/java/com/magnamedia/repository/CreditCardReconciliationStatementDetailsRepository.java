package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CreditCardReconciliationStatement;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails.CRDRAction;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2/1/2021
 */
@Repository
public interface CreditCardReconciliationStatementDetailsRepository extends BaseRepository<CreditCardReconciliationStatementDetails> {

    List<CreditCardReconciliationStatementDetails> findByRecordTransactionDateAndRecordDescriptionAndRecordAmountAndCrdrAction(Date recordTransactionDate, String recordDescription, Double recordAmount, CRDRAction crdrAction);
    List<CreditCardReconciliationStatementDetails> findByCreditCardReconciliationStatement_Id(Long statementId);
    List<CreditCardReconciliationStatementDetails> findByCreditCardReconciliationStatementAndConfirmed(CreditCardReconciliationStatement statement, boolean confirmed);
}
