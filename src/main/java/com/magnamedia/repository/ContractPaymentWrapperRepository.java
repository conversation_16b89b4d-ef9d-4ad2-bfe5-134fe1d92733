package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentConfirmationToDo;
import com.magnamedia.entity.ContractPaymentWrapper;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Created by hp on 7/8/2021.
 */

@Repository
public interface ContractPaymentWrapperRepository extends BaseRepository<ContractPaymentWrapper> {
    List<ContractPaymentWrapper> findByContractPaymentConfirmationToDo_ContractPaymentTerm_ContractAndProratedAndContractPaymentConfirmationToDo_Confirmed(Contract contract, boolean proRated, boolean confirmed);
    List<ContractPaymentWrapper> findByContractPaymentConfirmationToDo_ContractPaymentTerm_ContractAndInitialAndContractPaymentConfirmationToDo_Confirmed(Contract contract, boolean initial, boolean confirmed);

    //ACC-3968
    @Query("SELECT c FROM ContractPaymentWrapper c WHERE c.generatedPaymentId = ?1")
    List<ContractPaymentWrapper> findForAcc3968(Long paymentId);
    
    List<ContractPaymentWrapper> findByReplacedFuturePaymentId(Long paymentId);
    boolean existsByReplacedBouncedPaymentId(Long paymentId);

    @Query("select count(cpw) > 0 from ContractPaymentWrapper cpw where cpw.generatedPaymentId = ?1")
    boolean existsByGeneratedPaymentId(Long paymentId);

    boolean existsByContractPaymentConfirmationToDoAndContractPayment_AmountAndContractPayment_PaymentType(
            ContractPaymentConfirmationToDo c, Double d, PicklistItem t);

    List<ContractPaymentWrapper> findByGeneratedPaymentId(Long paymentId);

    List<ContractPaymentWrapper> findByContractPayment_GeneratedPaymentIdIn(Collection<Long> generatedPaymentIds);
}
