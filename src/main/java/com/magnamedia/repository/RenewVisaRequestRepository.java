package com.magnamedia.repository;

import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.RenewRequest;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 16, 2017
 */
@Repository
public interface RenewVisaRequestRepository extends VisaRequestRepository<RenewRequest> {
    
    public List<RenewRequest> findByHousemaidAndCompletedAndLastMoveDateGreaterThanEqual
        (Housemaid housemaid, boolean completed, Date lastMoveDate);
        
    //Jirra 1384
    public RenewRequest findFirstOneByHousemaidAndCompletedOrderByCreationDateDesc
        (Housemaid housemaid, boolean completed);
        
    //Jirra 1384
    public RenewRequest findFirstOneByHousemaidOrderByCreationDateDesc(Housemaid housemaid);
        
    public List<RenewRequest> findByCompletedAndLastMoveDateGreaterThanEqual
        (boolean completed, Date lastMoveDate);
        
    public List<RenewRequest> findByCompleted(boolean completed);
}
