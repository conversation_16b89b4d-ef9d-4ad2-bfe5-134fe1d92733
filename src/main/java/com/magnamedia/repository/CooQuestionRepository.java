package com.magnamedia.repository;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.CooQuestion;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by Mamon.Masod on 4/4/2021.
 */

@Repository
public interface CooQuestionRepository extends BaseRepository<CooQuestion> {

    @Query("select cooQ from CooQuestion cooQ where cooQ.questionedPage = :#{#questionedPage} and cooQ.relatedEntityType = :#{#relatedEntity.entityType} and cooQ.relatedEntityId = :#{#relatedEntity.id}")
    List<CooQuestion> findByRelatedEntityAndQuestionedPage(@Param("relatedEntity") BaseEntity relatedEntity, @Param("questionedPage") CooQuestion.QuestionedPage questionedPage);

    List<CooQuestion> findByRelatedEntityIdAndRelatedEntityTypeAndQuestionedPage(Long relatedEntityId, String relatedEntityType, CooQuestion.QuestionedPage questionedPage);
}
