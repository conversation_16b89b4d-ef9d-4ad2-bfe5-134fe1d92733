package com.magnamedia.repository.OnlineCardStatement;

import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementFile;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OnlineCardStatementRecordRepository extends BaseRepository<OnlineCardStatementRecord> {

    List<OnlineCardStatementRecord> findByOnlineCardStatementFile(OnlineCardStatementFile file);

    List<OnlineCardStatementRecord> findByOnlineCardStatementFileAndBreakdownTypeIn(
            OnlineCardStatementFile onlineCardStatementFile, List<OnlineCardStatementRecord.BreakdownType> breakdownTypes);

    @Query("select count(r.id) > 0 from OnlineCardStatementRecord r where r.onlineCardStatementFile.id = ?1")
    boolean existsRecordsCreatedByFile(Long fileId);

    @Query("select count(b.id) > 0 from BackgroundTask b where b.name = ?1 and b.status not in ?2")
    boolean existsConfirmedTodoBGTRunning(String name, List<BackgroundTaskStatus> statuses);
}
