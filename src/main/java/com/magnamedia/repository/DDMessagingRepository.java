package com.magnamedia.repository;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.module.type.DDMessagingSubType;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitMessagingScheduleTermCategory;
import com.magnamedia.module.type.*;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * Created on Apr 13, 2020
 * ACC-1611
 */
@Repository
public interface DDMessagingRepository extends BaseRepository<DDMessaging> {

    List<DDMessaging> findByEventAndIsActiveTrueAndBouncedPaymentStatusAndScheduleTermCategory(DDMessagingType event, PicklistItem picklistItem, DirectDebitMessagingScheduleTermCategory scheduleTermCategory);

    Page<DDMessaging> findByClientTemplateIsNotNull(Pageable pageable);

    Page<DDMessaging> findByMaidTemplateIsNotNullOrMaidWhenRetractCancellationTemplateIsNotNull(Pageable pageable);

    DDMessaging findByClientTemplate(Template template);

    List<DDMessaging> findByContractProspectTypesContainingAndScheduleTermCategory(String contractProspectType, DirectDebitMessagingScheduleTermCategory scheduleTermCategory);

    @Query("select d from DDMessaging d inner join d.maidTemplate t where d.contractProspectTypes like ?1 and t.name in ?2")
    List<DDMessaging> findByContractProspectTypesContainingAndMaidTemplateNameIn(String contractProspectType, List<String> neededTemplatesNames);

    List<DDMessaging> findByEventAndIsActiveTrueAndSubType(DDMessagingType event, DDMessagingSubType subType);

    List<DDMessaging> findByEventAndRejectCategoryAndRemindersIn(DDMessagingType event, DirectDebitRejectCategory rejectCategory, List<String> reminders);

    Boolean existsByEventAndSubTypeAndTrialsAndRemindersAndSendPayTabMessageTrueAndDeletedFalse(
            DDMessagingType event, DDMessagingSubType subType, String trial, String reminder);
}
