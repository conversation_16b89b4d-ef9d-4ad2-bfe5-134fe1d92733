package com.magnamedia.repository;

import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.NewRequestExpense;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Jan 15, 2018
 */
@Repository
public interface NewVisaRequestExpenseRepository extends VisaExpenseRepository<NewRequestExpense> {


    /*@Query(nativeQuery = true,
            value = "Select * from ( " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM NEWREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM RENEWREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM CANCELREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM REPEATEIDREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM CONTRACTMODIFICATIONEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM MODIFYVISAREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM UNPAIDLEAVEEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM MODIFYPERSONINFORMATIONEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    ") as t " +
                    "WHERE not exists (select 1 from VISASTATEMENTTRANSACTIONS tr " +
                                        "where tr.VISA_EXPENSE_TYPE = t.visaExpenseType AND tr.VISA_REQUEST_EXPENSE_ID = t.visaRequestExpenseID " +
                                            "AND tr.STATEMENT_ID = ?1)",
            countQuery =
                    "Select count(visaRequestExpenseID) from ( " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM  NEWREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM RENEWREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM CANCELREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM REPEATEIDREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM CONTRACTMODIFICATIONEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM MODIFYVISAREQUESTEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM UNPAIDLEAVEEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    "UNION " +
                        "SELECT ne.ID visaRequestExpenseID, ne.ENTITY_TYPE visaExpenseType " +
                        "FROM MODIFYPERSONINFORMATIONEXPENSES ne " +
                        "WHERE ne.status IS NOT NULL AND ne.status = 'Pending' AND ne.CREATION_DATE BETWEEN ?2 AND ?3 " +
                    ") as t " +
                    "WHERE not exists (select 1 from VISASTATEMENTTRANSACTIONS tr " +
                                        "where tr.VISA_EXPENSE_TYPE = t.visaExpenseType AND tr.VISA_REQUEST_EXPENSE_ID = t.visaRequestExpenseID " +
                                            "AND tr.STATEMENT_ID = ?1)")
    Page<Object[]> getVisaExpenseMissingFromStatement(
            Long statementId, Date startDate, Date endDate, Pageable pageable);*/

    @Query(nativeQuery = true,
            value = "Select * from ( " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM NEWREQUESTEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM RENEWREQUESTEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM CANCELREQUESTEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM REPEATEIDREQUESTEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM CONTRACTMODIFICATIONEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM MODIFYVISAREQUESTEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM UNPAIDLEAVEEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                            "UNION " +
                            "SELECT ne.ID visaRequestExpenseID, " +
                            "ne.ENTITY_TYPE visaExpenseType, " +
                            "ne.AMOUNT amount, " +
                            "ne.CHARGE CHARGE, " +
                            "ne.VAT_CHARGE VAT_CHARGE " +
                            "FROM MODIFYPERSONINFORMATIONEXPENSES ne " +
                            "WHERE (ne.REFERENCE_NUMBER = :ref AND ne.status = 'Pending') " +
                    ") as t ")
    List<Object[]> findByExpenseReferenceNumber(@Param("ref") String referenceNumber);

    @Query(nativeQuery = true,
            value = "Select * from ( " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM NEWREQUESTEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM RENEWREQUESTEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM CANCELREQUESTEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM REPEATEIDREQUESTEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM CONTRACTMODIFICATIONEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM MODIFYVISAREQUESTEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM UNPAIDLEAVEEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers " +
                    "UNION ALL " +
                    "SELECT ne.REFERENCE_NUMBER, ne.ID visaRequestExpenseID, " +
                    "ne.ENTITY_TYPE visaExpenseType, " +
                    "ne.AMOUNT amount, " +
                    "ne.CHARGE CHARGE, " +
                    "ne.VAT_CHARGE VAT_CHARGE " +
                    "FROM MODIFYPERSONINFORMATIONEXPENSES ne " +
                    "WHERE ne.status = 'Pending' " +
                    "AND ne.REFERENCE_NUMBER IN :referenceNumbers" +
                    ") as t")
    List<Object[]> findByExpenseReferenceNumbersAndCreationDateBatch(
            @Param("referenceNumbers") List<String> referenceNumbers);

    @Query("select n.overstayFee " +
            "from NewRequestExpense ne " +
            "join ne.request n " +
            "where n.housemaid = ?1 and ne.status = 'Pending' " +
            "order by ne.creationDate desc")
    List<Double> findOverstayFeeByHousemaidAndStatusPending(Housemaid h);

    @Query(nativeQuery = true,
            value = "Select sum(q.amount) as amount from (  " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM NEWREQUESTEXPENSES ne INNER JOIN NEWREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION  " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM RENEWREQUESTEXPENSES ne INNER JOIN RENEWREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION  " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM CANCELREQUESTEXPENSES ne INNER JOIN CANCELREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION  " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM REPEATEIDREQUESTEXPENSES ne INNER JOIN REPEATEIDREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION  " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM CONTRACTMODIFICATIONEXPENSES ne INNER JOIN CONTRACTMODIFICATIONS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION  " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM MODIFYVISAREQUESTEXPENSES ne INNER JOIN MODIFYVISAREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM MODIFYPERSONINFORMATIONEXPENSES ne INNER JOIN MODIFYPERSONINFORMATIONREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    "UNION " +
                        "SELECT SUM(t.amount) as amount " +
                        "FROM UNPAIDLEAVEEXPENSES ne INNER JOIN UNPAIDLEAVEREQUESTS n ON n.id = ne.REQUEST_ID " +
                        "INNER JOIN TRANSACTIONS t ON t.id = ne.TRANSACTION_ID " +
                        "WHERE n.HOUSEMAID_ID = :houseMaidId AND ne.status = 'Added' " +
                    ") as q")
    Double findSumTransactionAmountByHouseMaidId(@Param("houseMaidId") Long houseMaidId);
}
