package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.NoqodiStatementRecord;
import com.magnamedia.entity.VisaStatement;
import com.magnamedia.entity.VisaStatementTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface NoqodiStatementRecordRepository extends BaseRepository<NoqodiStatementRecord> {

    @Query("select r from NoqodiStatementRecord r where r.id > ?1 and r.statement = ?2 and r.isFinal = ?3")
    Page<NoqodiStatementRecord> findByStatementAndIsFinal(Long lastId, VisaStatement statement, Boolean isFinal, Pageable pageable);

    NoqodiStatementRecord findTopByTransaction(VisaStatementTransaction transaction);

    List<NoqodiStatementRecord> findByStatement(VisaStatement statement);

}
