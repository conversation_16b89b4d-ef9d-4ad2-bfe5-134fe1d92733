package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FlowProcessorEntityRepository extends BaseRepository<FlowProcessorEntity> {

    FlowProcessorEntity findFirstByFlowEventConfig_NameAndContractPaymentTerm_ContractOrderByCreationDateDesc(
            FlowEventConfig.FlowEventName name, Contract contract);

    FlowProcessorEntity findFirstByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalseOrderByCreationDate(
            FlowEventConfig.FlowEventName flowEventConfig, Contract c);

    FlowProcessorEntity findFirstByFlowEventConfigAndContractPaymentTermAndStoppedFalseAndCompletedFalseOrderByCreationDateDesc(
            FlowEventConfig flowEventConfig, ContractPaymentTerm contractPaymentTerm);

    FlowProcessorEntity findFirstByFlowEventConfigAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalseOrderByCreationDateDesc(
            FlowEventConfig flowEventConfig, Contract contract);

    List<FlowProcessorEntity> findByFlowEventConfigAndStoppedFalseAndCompletedFalse(
            FlowEventConfig flowEventConfig);

    @Query("select f " +
            "from FlowProcessorEntity f " +
            "where f.id > ?1 and f.flowEventConfig.name = ?2 and f.stopped = false and f.completed = false and " +
                "not exists( select 1 from GraphicDesignerToDo g where g.contractId = f.contractPaymentTerm.contract.id and " +
                    "g.toDoType = 'ACCOUNTING_DIGITALIZE_CLIENT_SIGNATURE' and g.completed = false and g.stopped = false)")
    Page<FlowProcessorEntity> findRunningIncompleteFlowMissingBankInfo(
            Long id, FlowEventConfig.FlowEventName flowEventName, Pageable pageable);

    List<FlowProcessorEntity> findByFlowEventConfig_NameAndStoppedFalseAndCompletedFalse(FlowEventConfig.FlowEventName name);

    Boolean existsByFlowEventConfig_NameAndContractPaymentTerm_Contract(
            FlowEventConfig.FlowEventName name, Contract contract);

    Boolean existsByFlowEventConfig_NameAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalse(
            FlowEventConfig.FlowEventName name, Contract contract);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "where f.id = ?1 and f.stopped = false and f.completed = false")
    boolean existsRunningFlow(Long id);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and f.flowEventConfig.name = ?2 and f.currentSubEvent.name = ?3 and " +
            "f.creationDate between ?4 and ?5")
    boolean existsFlowCreatedBetween(
            Contract contract, FlowEventConfig.FlowEventName flowEventName,
            FlowSubEventConfig.FlowSubEventName subEventName,
            Date startDate,
            Date endDate);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and f.flowEventConfig.name = ?2 " +
                "and f.currentSubEvent.name in (?3) and f.stopped = false and f.completed = false")
    boolean existsRunningFlow(
            Contract contract,
            FlowEventConfig.FlowEventName event,
            List<FlowSubEventConfig.FlowSubEventName> subEvents);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "join f.contractPaymentConfirmationToDo todo " +
            "inner join ContractPaymentWrapper wrapper on wrapper.contractPaymentConfirmationToDo = todo " +
            "where f.contractPaymentTerm.contract = ?1 and " +
            "f.flowEventConfig.name = ?2 and f.currentSubEvent.name in (?3)  and wrapper.generatedPaymentId = ?4 ")
    boolean existsFlowByRelatedTodo(
            Contract contract, FlowEventConfig.FlowEventName event, List<FlowSubEventConfig.FlowSubEventName> subEvents, Long paymentId);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and f.flowEventConfig.name in ?2 " +
                "and f.stopped = false and f.completed = false")
    boolean existsRunningFlow(
            Contract contract,
            List<FlowEventConfig.FlowEventName> events);

    @Query("select f from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and " +
                "f.flowEventConfig.name in ('CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED', 'CLIENTS_PAYING_VIA_Credit_Card') and " +
                "f.stoppedDueFreezeContract = true")
    List<FlowProcessorEntity> findPayingViaCcStoppedDueFreezeContract(Contract contract);

    @Query("select f from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = :contract and f.flowEventConfig.name = :event " +
            "and f.currentSubEvent.name in (:subEvents) and f.stopped = false and f.completed = false " +
            "and (:todo is null or f.contractPaymentConfirmationToDo = :todo)" +
            "order by f.creationDate desc")
    List<FlowProcessorEntity> getRunningFlow(
            @Param("contract") Contract contract,
            @Param("event") FlowEventConfig.FlowEventName event,
            @Param("subEvents") List<FlowSubEventConfig.FlowSubEventName> subEvents,
            @Param("todo") ContractPaymentConfirmationToDo todo);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and f.flowEventConfig.name = ?2 and " +
                "f.stopped = true and f.trials >= f.currentSubEvent.maxTrials")
    boolean isContractScheduledForTerminationByFlowEventName(
            Contract contract, FlowEventConfig.FlowEventName flowEventName);

    Boolean existsByFlowEventConfig_NameAndContractPaymentTermAndStoppedFalseAndCompletedFalse(
            FlowEventConfig.FlowEventName name, ContractPaymentTerm contractPaymentTerm);

    // ACC-5161
    Boolean existsByFlowEventConfigAndContractPaymentTerm_ContractAndStoppedFalseAndCompletedFalse(
            FlowEventConfig flowEventConfig, Contract contract);

    List<FlowProcessorEntity> findByFlowEventConfig_NameAndContractPaymentConfirmationToDo(
            FlowEventConfig.FlowEventName name, ContractPaymentConfirmationToDo t);

    List<FlowProcessorEntity> findByContractPaymentTermAndStoppedFalseAndCompletedFalse(ContractPaymentTerm cpt);

    FlowProcessorEntity findFirstByContractPaymentConfirmationToDoAndStoppedFalseAndCompletedFalse(ContractPaymentConfirmationToDo t);

    List<FlowProcessorEntity> findByContractPaymentConfirmationToDoAndStoppedFalseAndCompletedFalse(
            ContractPaymentConfirmationToDo todo);

    List<FlowProcessorEntity> findByContractPaymentConfirmationToDo(ContractPaymentConfirmationToDo todo);

    @Query("select f from FlowProcessorEntity f " +
            "where f.contractPaymentConfirmationToDo = ?1 " +
            "order by f.stopped, f.completed")
    List<FlowProcessorEntity> findByContractPaymentConfirmationToDoOrderByRunning(ContractPaymentConfirmationToDo todo);

    @Query("select f from ContractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = todo.id " +
            "inner join FlowSubEventConfig s on s.id = f.currentSubEvent " +
            "where f.contractPaymentTerm.contract = ?1 and (todo = ?2 or " +
                    "(todo.disabled = false and todo.showOnERP = false and todo.confirmed = false)) and " +
                "s.name in ('INITIAL_FLOW_FOR_DDB', 'INITIAL_FLOW_FOR_DDA') and f.stopped = true and " +
                "not exists (select 1 from FlowProcessorEntity f1 where f1.contractPaymentTerm.contract = ?1 and " +
                    "f1.currentSubEvent.name in ?3 and f1.stopped = false and f.completed = false) " +
            "order by f.creationDate desc")
    List<FlowProcessorEntity> findOnlineCardTodosRelatedToInitialFlowStopped(
            Contract c, ContractPaymentConfirmationToDo todo, List<FlowSubEventConfig.FlowSubEventName> subEvents, Pageable pageable);

    @Query("select count(f.id) > 0 from FlowProcessorEntity f " +
            "where f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and f.currentSubEvent.name = 'MONTHLY_REMINDER' and " +
            "f.contractPaymentTerm.contract = ?1 and f.stopped = true and f.creationDate >= ?2  ")
    boolean existsStoppedMonthlyReminder(Contract c, Date startDate);

    @Query("select f from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and f.stopped = true and f.completed = false and " +
            "(f.stoppedDueContractTerminated = true or f.causedTermination = true) ")
    List<FlowProcessorEntity> findAllStoppedFlowsToReactivateCollectionFlowByContract(Contract c);

    @Query("select f from FlowProcessorEntity f " +
            "where f.contractPaymentTerm.contract = ?1 and f.causedTermination = true")
    List<FlowProcessorEntity> findByContractAndCausedTermination(Contract c);

    @Query("select f from FlowProcessorEntity f " +
            "where f.flowEventConfig.name = 'CLIENTS_PAYING_VIA_Credit_Card' and f.currentSubEvent.name = 'MONTHLY_REMINDER' and " +
            "f.contractPaymentTerm.contract = ?1 and f.stopped = true and f.creationDate >= ?2 " +
            "order by f.creationDate desc")
    List<FlowProcessorEntity> findStoppedMonthlyReminder(Contract c, Date startDate);

    @Query("select f from FlowProcessorEntity f " +
            "where f.contractPaymentConfirmationToDo.id = ?1 and f.stopped = false and f.completed = false")
    List<FlowProcessorEntity> findRunningFlowByToDo(Long id);

    @Query("select count(w.id) > 0 from ContractPaymentWrapper w " +
            "join w.contractPaymentConfirmationToDo todo " +
            "inner join FlowProcessorEntity f on todo.id = f.contractPaymentConfirmationToDo.id " +
            "where f.contractPaymentTerm.contract.id = ?1 and f.flowEventConfig.name = ?2 and " +
                "w.paymentDate = ?3 and w.amount = ?4 and w.paymentType.code = ?5 and " +
                "f.stopped = false and f.completed = false and todo.disabled = false and todo.showOnERP = false")
    boolean existsRunningFlowCoverPaymentByContractAndPaymentInfo(
            Long contractId, FlowEventConfig.FlowEventName eventName, Date date, Double amount, String type);

    @Query("select count(w.id) > 0 from ContractPaymentWrapper w " +
            "join w.contractPaymentConfirmationToDo t " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = t " +
            "where t.contractPaymentTerm.contract = ?1 and t.disabled = false and t.showOnERP = false and " +
                "f.flowEventConfig.name = 'ONLINE_CREDIT_CARD_PAYMENT_REMINDERS' and " +
                "f.stopped = false and f.completed = false and " +
                "w.paymentDate between ?2 and ?3 and w.paymentType.code = 'monthly_payment'")
    boolean existsOnlineReminderCoverNextMonthlyPayment(Contract c, Date startDate, Date endDate);

    @Query("select distinct f from ContractPaymentWrapper w " +
            "join w.contractPaymentConfirmationToDo t " +
            "inner join FlowProcessorEntity f on f.contractPaymentConfirmationToDo = t " +
            "where t.contractPaymentTerm.contract = ?1 and t.disabled = false and t.showOnERP = false and " +
                "f.flowEventConfig.name = 'ONLINE_CREDIT_CARD_PAYMENT_REMINDERS' and " +
                "f.stopped = false and f.completed = false and " +
                "w.paymentDate between ?2 and ?3 and w.paymentType.code = 'monthly_payment'")
    List<FlowProcessorEntity> findOnlineReminderFlowsCoverNextMonthlyPayment(Contract c, Date startDate, Date endDate);

    @Query("select f " +
            "from FlowProcessorEntity f " +
            "where f.id > ?1 and f.flowEventConfig.name = ?2 and " +
                "f.stopped = false and f.completed = false")
    Page<FlowProcessorEntity> findByEventNameAndStoppedFalseAndCompletedFalse(
            Long lastId, FlowEventConfig.FlowEventName flowEventName, Pageable pageable);
}