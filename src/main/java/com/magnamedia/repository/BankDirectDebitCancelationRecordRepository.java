package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.BankDirectDebitCancelationFile;
import com.magnamedia.entity.BankDirectDebitCancelationRecord;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
@Repository
public interface BankDirectDebitCancelationRecordRepository
        extends BaseRepository<BankDirectDebitCancelationRecord> {

    List<BankDirectDebitCancelationRecord> findByBankDirectDebitCancelationFile(BankDirectDebitCancelationFile bankDirectDebitCancelationFile);

    @Query("select b.id from BankDirectDebitCancelationRecord b " +
            "left join b.directDebitFile ddf " +
            "where b.bankDirectDebitCancelationFile.id = ?1 and ddf is not null and " +
                "ddf.ddStatus = 'PENDING_FOR_CANCELLATION' and b.confirmed = false and b.status = 'REJECTED'")
    List<Long> findIdsByBankDirectDebitCancellationFileId(Long fileId);

    @Query("select count(record) > 0 from BankDirectDebitCancelationRecord record where record.bankDirectDebitCancelationFile = :file " +
            "and (record.cbStatus is null or record.cbStatus <> :cbStatus) and record.confirmed = :confirmed")
    boolean existsByCbStatusNotAndBankDirectDebitCancelationFileAndConfirmed(@Param("cbStatus") String cbStatus, @Param("file") BankDirectDebitCancelationFile file, @Param("confirmed") boolean confirmed);

    List<BankDirectDebitCancelationRecord> findByCbStatusAndBankDirectDebitCancelationFile(String cbStatus, BankDirectDebitCancelationFile file);

    @Query("select b.id as id, b.status as status, b.bankDirectDebitCancelationFile.id as fileId " +
            "from BankDirectDebitCancelationRecord b " +
            "join b.directDebitFile ddf " +
            "where b.bankDirectDebitCancelationFile = ?1 and " +
                "ddf.ddStatus = 'PENDING_FOR_CANCELLATION' and b.confirmed = false and " +
                "not exists (select 1 from DDFBatchForRPA rpa " +
                    "where rpa.status = 'UNDER_PROCESS' and (concat(', ', rpa.ids, ',') like concat('%, ', ddf.id, ',%')))")
    List<Map<String, Object>> findRecordsToBeConfirmed(BankDirectDebitCancelationFile file);

    @Query("select count(b.id) from BankDirectDebitCancelationRecord b " +
            "left join b.directDebitFile ddf " +
            "where b.bankDirectDebitCancelationFile.id = ?1 and b.confirmed = false and " +
                "ddf is not null and ddf.ddStatus = 'PENDING_FOR_CANCELLATION' and b.status = ?2")
    long countMatchedRecordsByStatus(Long fileId, BankDirectDebitCancelationRecord.status status);

    @Query("select count(b.id) from BankDirectDebitCancelationRecord b " +
            "left join b.directDebitFile ddf " +
            "where b.bankDirectDebitCancelationFile.id = ?1 and " +
                "(ddf is null or ddf.ddStatus not in ('PENDING_FOR_CANCELLATION', 'CANCELED'))")
    long countUnMatchedRecords(Long fileId);

    @Query("select b from BankDirectDebitCancelationRecord b " +
            "where b.bankDirectDebitCancelationFile.id = ?1")
    List<BankDirectDebitCancelationRecord> findRecordsByFileId(Long fileId);

    @Query("select count(b.id) " +
            "from BankDirectDebitCancelationRecord b " +
            "where b.confirmed = true and b.status = 'CONFIRMED' and " +
            "(:fileId is null or b.bankDirectDebitCancelationFile.id <> :fileId) and b.ddaRefNo in (:ddaRefNos) " +
            "group by b.ddaRefNo")
    Long countRecordsByDdaRefNo(@Param("fileId") Long fileId, @Param("ddaRefNos") List<String> ddaRefNos);

    @Query("select count(b.id) > 0 from BankDirectDebitCancelationRecord b " +
            "left join b.directDebitFile ddf " +
            "where b.bankDirectDebitCancelationFile = ?1 and " +
                "(ddf is null or ddf.ddStatus not in ('PENDING_FOR_CANCELLATION', 'CANCELED'))")
    boolean existsUnMatchedRecords(BankDirectDebitCancelationFile file);

}