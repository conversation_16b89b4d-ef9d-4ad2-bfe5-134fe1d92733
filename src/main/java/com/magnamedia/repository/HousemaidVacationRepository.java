package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.HousemaidVacation;
import com.magnamedia.module.type.VacationStatus;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 */
@Repository
public interface HousemaidVacationRepository extends BaseRepository<HousemaidVacation> {

	public List<HousemaidVacation> findByHousemaidAndStatus(Housemaid housemaid, VacationStatus status);       
}
