package com.magnamedia.repository;


import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.repository.BaseRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @created 28/05/2025 - 2:12 PM
 * ACC-9428
 */
@Repository
public interface BaseAdditionalInfoAccRepository extends BaseRepository<BaseAdditionalInfo> {

    boolean existsByOwnerIdAndOwnerTypeAndInfoKeyAndInfoValue(Long id, String ownerType, String key, String value);
}