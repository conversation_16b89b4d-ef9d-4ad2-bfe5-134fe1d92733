package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.HousemaidTransaction;
import com.magnamedia.entity.Transaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1338
 */

@Repository
public interface HousemaidTransactionRepository extends BaseRepository<HousemaidTransaction> {

    List<HousemaidTransaction> findByTransaction(Transaction t);

    @Query("select new map(ht as housemaidTransaction, todo.creationDate as creationDate) " +
            "from HousemaidTransaction ht " +
            "join ht.transaction t " +
            "join ht.housemaid h " +
            "inner join ExpensePayment e on e.id = t.expensePaymentId " +
            "inner join ExpenseRequestTodo todo on todo.expensePayment = e " +
            "where ht.id > ?1 and ht.contractId is null and todo.relatedToId = h.id and todo.relatedToType = 'MAID'")
    Page<Map<String, Object>> findTransactionsNeedingContractIdUpdate(Long lastId, Pageable pageable);
}
