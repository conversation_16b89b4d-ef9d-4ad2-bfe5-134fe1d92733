package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.repository.SalariesAccessRepository;

import java.io.IOException;

/**
 * Created on 3/4/2021.
 * ACC-3200
 */

public class SalaryAmountSerializer extends JsonSerializer<Object> {
    @Override
    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        SalariesAccessRepository repository = Setup.getRepository(SalariesAccessRepository.class);
        User user = CurrentRequest.getUser();

        if (user == null || "guest".equals(user.getLoginName()) || repository.countByUser(user) > 0) {
            jsonGenerator.writeObject(o);
        } else {
            jsonGenerator.writeString("****");
        }
    }
}

