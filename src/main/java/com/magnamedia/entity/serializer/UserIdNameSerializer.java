package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.User;

import java.io.IOException;

/**
 * <PERSON> (Jan 27, 2021)
 */
public class UserIdNameSerializer extends JsonSerializer<User> {

    @Override
    public void serialize(User value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
        if(value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeString<PERSON>ield("firstName", value.getFirstName());
        gen.writeString<PERSON>ield("lastName", value.getLastName());
        gen.writeEndObject();
    }
}
