package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 8, 2018
 */
public class PaymentRequestPurposeSerializer extends JsonSerializer<PaymentRequestPurpose> {

	@Override
	public void serialize(PaymentRequestPurpose value, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		
            if (value == null) {
                gen.writeNull();
                return;
            }
            gen.writeStartObject();
            gen.writeNumberField("id", value.getId());
            gen.writeStringField("name", value.getName());
            gen.writeStringField("label", value.getName());
            gen.writeStringField("categoryStr", value.getCategoryStr());
            gen.writeBooleanField("categorySamePurpose", value.getCategorySamePurpose());
            if(value.getRefundCategory()!=null){
                
                gen.writeFieldName("refundCategory");
                gen.writeStartObject();
                gen.writeNumberField("id", value.getRefundCategory().getId());
                gen.writeStringField("name", value.getRefundCategory().getName());
                gen.writeStringField("label", value.getRefundCategory().getName());
                gen.writeEndObject();
            } else {
                gen.writeObjectField("refundCategory", null);
            }
            gen.writeEndObject();
	}

}
