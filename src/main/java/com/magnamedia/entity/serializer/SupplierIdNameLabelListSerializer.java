package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Supplier;
import com.magnamedia.module.type.ExpensePaymentMethod;

import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Aug 8, 2018
 */
public class SupplierIdNameLabelListSerializer extends JsonSerializer<List<Supplier>> {

	@Override
	public void serialize(List<Supplier> values, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		if (values == null || values.isEmpty()) {
			gen.writeNull();
			return;
		}
        gen.writeStartArray();
        for (Supplier value : values){
            gen.writeStartObject();
            gen.writeNumberField("id", value.getId());
            gen.writeStringField("label", value.getLabel());
            gen.writeStringField("name", value.getName());
            gen.writeBooleanField("isTicketNumberRequired",
                    value.getIsTicketNumberRequired()); // Jira ACC-4505
            gen.writeArrayFieldStart("paymentMethods");
            for(ExpensePaymentMethod paymentMethod : value.getPaymentMethods()) {
                gen.writeStartObject();
                gen.writeStringField("label", paymentMethod.getLabel());
                gen.writeStringField("value", paymentMethod.name());
                gen.writeEndObject();
            }
            gen.writeEndArray();
            gen.writeEndObject();
        }
        gen.writeEndArray();
	}
}