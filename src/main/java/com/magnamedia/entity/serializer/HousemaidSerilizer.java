package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Housemaid;

import java.io.IOException;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jan 15, 2019
 */

public class HousemaidSerilizer extends JsonSerializer<Housemaid> {

    @Override
    public void serialize(Housemaid housemaid, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        if (housemaid == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                housemaid.getId());
        gen.writeStringField("label",
                housemaid.getLabel());

        gen.writeStringField("name",
                housemaid.getName());

        if (housemaid.getNationality() != null) {
            gen.writeFieldName("nationality");
            gen.writeStartObject();
            gen.writeNumberField("id", housemaid.getNationality().getId());
            gen.writeStringField("name", housemaid.getNationality().getName());
            gen.writeEndObject();
        } else {
            gen.writeStringField("nationality",
                    null);
        }
        gen.writeStringField("passportNumber", housemaid.getPassportNumber());

        //Jirra ACC-1435
        if (housemaid.getStatus() != null) {
            gen.writeStringField("status",
                    housemaid.getStatus().name());
        } else {
            gen.writeStringField("status",
                    null);
        }

        if (housemaid.getPendingStatus() != null) {
            gen.writeStringField("pendingStatus",
                    housemaid.getPendingStatus().name());
        } else {
            gen.writeStringField("pendingStatus",
                    null);
        }
        gen.writeEndObject();
    }

}

