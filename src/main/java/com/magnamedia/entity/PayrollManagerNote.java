package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */

@Entity
@Table(
        indexes = {
            @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class PayrollManagerNote extends BaseEntity{
    
    public enum ManagerNoteType {
        ADDITION,
        DEDUCTION,
        PENALTY_DEDUCTION,
        EXTRA_SHIFT
    }
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private Housemaid housemaid;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private OfficeStaff officeStaff;
    
    @Column
    private Double amount;
    
//    @Column
//    private Double postponedAmount;
//    
//    //Jirra ACC-645
//    @Column
//    private Date postponedDate;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollManagerNote oldNote;
    
    @Column
    private String noteReasone;
    
    @Column
    private Date noteDate;
    
    @Column
    private Boolean isRefund = false;
    
    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private PayrollManagerNote refundedNote;
    
    @Column
    @Enumerated(EnumType.STRING)
    private ManagerNoteType noteType;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem additionReason;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem deductionReason;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem fromManager;
    
    //Jirra ACC-1085
    @Column(columnDefinition = "boolean default false")
    private boolean notFinal;
        
    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

//    public Double getPostponedAmount() {
//        return postponedAmount;
//    }
//
//    public void setPostponedAmount(Double postponedAmount) {
//        this.postponedAmount = postponedAmount;
//    }
//
//    public Date getPostponedDate() {
//        return postponedDate;
//    }
//
//    public void setPostponedDate(Date postponedDate) {
//        this.postponedDate = postponedDate;
//    }

    public PayrollManagerNote getOldNote() {
        return oldNote;
    }

    public void setOldNote(PayrollManagerNote oldNote) {
        this.oldNote = oldNote;
    }

    public Boolean getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Boolean isRefund) {
        this.isRefund = isRefund;
    }

    public PayrollManagerNote getRefundedNote() {
        return refundedNote;
    }

    public void setRefundedNote(PayrollManagerNote refundedNote) {
        this.refundedNote = refundedNote;
    }

    public String getNoteReasone() {
        return noteReasone;
    }

    public void setNoteReasone(String noteReasone) {
        this.noteReasone = noteReasone;
    }


    public ManagerNoteType getNoteType() {
        return noteType;
    }

    public void setNoteType(ManagerNoteType noteType) {
        this.noteType = noteType;
    }



    public PicklistItem getAdditionReason() {
        return additionReason;
    }

    public void setAdditionReason(PicklistItem additionReason) {
        this.additionReason = additionReason;
    }

    public PicklistItem getDeductionReason() {
        return deductionReason;
    }

    public void setDeductionReason(PicklistItem deductionReason) {
        this.deductionReason = deductionReason;
    }

    public PicklistItem getFromManager() {
        return fromManager;
    }

    public void setFromManager(PicklistItem fromManager) {
        this.fromManager = fromManager;
    }

    public Date getNoteDate() {
        return noteDate;
    }

    public void setNoteDate(Date noteDate) {
        this.noteDate = noteDate;
    }

    public boolean isNotFinal() {
        return notFinal;
    }

    public void setNotFinal(boolean notFinal) {
        this.notFinal = notFinal;
    }
    
}
