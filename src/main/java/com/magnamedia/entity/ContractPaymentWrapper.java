package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.controller.ContractPaymentController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.*;
import com.magnamedia.service.ContractPaymentService;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import org.joda.time.LocalDate;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.util.Arrays;
import java.util.Date;
import java.util.logging.Logger;

/**
 * Created by Ma<PERSON>.Masod on 7/4/2021.
 */

@Entity
public class ContractPaymentWrapper extends BaseEntity {
    protected static final Logger logger = Logger.getLogger(ContractPaymentWrapper.class.getName());

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentConfirmationToDo contractPaymentConfirmationToDo;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPayment contractPayment;

    @Column
    private Long replacedBouncedPaymentId;

    @Column
    private Long replacedFuturePaymentId;

    @Column
    private Long transactionId;

    @Column
    private Long generatedPaymentId;

    @Column
    @Temporal(TemporalType.DATE)
    private Date paymentDate;

    @Column(columnDefinition = "boolean default false")
    private boolean prorated;

    @Column(columnDefinition = "boolean default false")
    private boolean initial;

    @Column
    private Double amount;

    @Column
    private Double actualReceivedAmount;

    @Column
    private Boolean vatPaidByClient;
    
    @Column(columnDefinition = "boolean default false")
    private boolean includeWorkerSalary = false;

    @Column
    @Enumerated(EnumType.STRING)
    private VatType vatType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private Revenue revenue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private Bucket toBucket;

    @Lob
    private String description;

    @Lob
    private String notes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem paymentType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem subType;

    @Column(columnDefinition = "boolean default false")
    private Boolean affectedByAdditionalDiscount = false;

    @Column(nullable = false)
    // @Min(0) ACC-7151 ACC-7390 #8
    private Double discountAmount = 0.0;

    @Column(columnDefinition = "double default 0")
    @Min(0)
    private Double moreAdditionalDiscount = 0.0;

    @Column(columnDefinition = "double default 0")
    @Min(0)
    private Double additionalDiscountAmount = 0.0;

    @Column(columnDefinition = "double default 0")
    @Min(0)
    private Double additionAmount = 0.0;

    @Column(columnDefinition = "boolean default false")
    private boolean includeUpgradingFee = false;

    @Column(columnDefinition = "double default 0")
    @Min(0)
    private Double workerSalary = 0.0;

    @Transient
    private boolean addedFromPaymentForApproval = false;

    public Boolean getAffectedByAdditionalDiscount() {
        return affectedByAdditionalDiscount;
    }

    public void setAffectedByAdditionalDiscount(Boolean affectedByAdditionalDiscount) {
        this.affectedByAdditionalDiscount = affectedByAdditionalDiscount;
    }

    //ACC-3827
    public Revenue getExpectedRevenue(){
        PicklistItem paymentType = getPaymentType();
        boolean mvContract = contractPaymentConfirmationToDo.getContractPaymentTerm().getContract().getContractProspectType()
                .getCode().equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));
        String code = null;
        if (paymentType != null) {
            if (mvContract) {
                if (isInitial() && paymentType.hasTag("mv_wire_transfer_revenue_initial")) {
                    code = paymentType.getTagValue("mv_wire_transfer_revenue_initial").getValue();
                } else if (paymentType.hasTag("mv_wire_transfer_revenue")) {
                    code = paymentType.getTagValue("mv_wire_transfer_revenue").getValue();
                }
            } else if (paymentType.hasTag("cc_wire_transfer_revenue")) {
                code = paymentType.getTagValue("cc_wire_transfer_revenue").getValue();
            }
        }
        return code == null ? null : Setup.getRepository(RevenueRepository.class).findByCode(code);
    }

    //ACC-3827
    public Bucket getExpectedToBucket(){
        Boolean isMustaqeemBucket = false;
        PicklistItem paymentType = getPaymentType();
        boolean mvContract = contractPaymentConfirmationToDo.getContractPaymentTerm().getContract().getContractProspectType().getCode().
                equalsIgnoreCase(PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));

        if(paymentType != null) {
            switch (paymentType.getCode().toLowerCase()) {
                case "cc_to_mv_contract":
                case "same_day_recruitment_fee":
                case "monthly_payment":
                case "service_charge":
                    isMustaqeemBucket = true;
                    break;
                case "accommodation_fee":
                case "employing_ex_dh":
                case "pcr_without_Vat":
                    if (!mvContract) isMustaqeemBucket = true;
                    break;
                case "matching_fee":
                case "urgent_visa_charges":
                case "overstay_fee":
                case "tadbeer_fees":
                    if (mvContract) isMustaqeemBucket = true;
                    break;
            }
        }

        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);

        return isMustaqeemBucket ? Setup.getRepository(BucketRepository.class).findByCode(bucketCode) : null;
    }

    public ContractPaymentConfirmationToDo getContractPaymentConfirmationToDo() {
        return contractPaymentConfirmationToDo;
    }

    public void setContractPaymentConfirmationToDo(ContractPaymentConfirmationToDo contractPaymentConfirmationToDo) {
        this.contractPaymentConfirmationToDo = contractPaymentConfirmationToDo;
    }

    public ContractPayment getContractPayment() {
        return contractPayment;
    }

    public void setContractPayment(ContractPayment contractPayment) {
        this.contractPayment = contractPayment;
    }

    public Long getReplacedBouncedPaymentId() {
        return replacedBouncedPaymentId;
    }

    public void setReplacedBouncedPaymentId(Long replacedBouncedPaymentId) {
        this.replacedBouncedPaymentId = replacedBouncedPaymentId;
    }

    public Long getReplacedFuturePaymentId() {
        return replacedFuturePaymentId;
    }

    public void setReplacedFuturePaymentId(Long replacedFuturePaymentId) {
        this.replacedFuturePaymentId = replacedFuturePaymentId;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public Long getGeneratedPaymentId() {
        return generatedPaymentId;
    }

    public void setGeneratedPaymentId(Long generatedPaymentId) {
        this.generatedPaymentId = generatedPaymentId;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public boolean isProrated() {
        return prorated;
    }

    public void setProrated(boolean prorated) {
        this.prorated = prorated;
    }

    public boolean isInitial() {
        return initial;
    }

    public void setInitial(boolean initial) {
        this.initial = initial;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = isAddedFromPaymentForApproval() ? amount - getDiscountAmount() : amount;
    }

    public Double getActualReceivedAmount() {
        if (actualReceivedAmount == null || actualReceivedAmount.equals(0D)) return amount;

        return actualReceivedAmount;
    }

    public void setActualReceivedAmount(Double actualReceivedAmount) {
        this.actualReceivedAmount = actualReceivedAmount;
    }

    public PicklistItem getPaymentType() {
        if (paymentType != null) return paymentType;
        else return contractPaymentConfirmationToDo.getPaymentType();
    }
    
    public void setPaymentType(PicklistItem paymentType) {
        this.paymentType = paymentType;
    }

    public PicklistItem getSubType() { return subType; }

    public void setSubType(PicklistItem subType) { this.subType = subType; }

    public PaymentMethod getPaymentMethod() {
        return contractPaymentConfirmationToDo.getPaymentMethod();
    }

    public boolean isVatPaidByClient() {
        return vatPaidByClient == null || vatPaidByClient;
    }
    
    public Boolean getVatPaidByClient() {
        return vatPaidByClient;
    }

    public void setVatPaidByClient(Boolean vatPaidByClient) {
        this.vatPaidByClient = vatPaidByClient;
    }

    public boolean isIncludeWorkerSalary() {
        return includeWorkerSalary;
    }

    public void setIncludeWorkerSalary(boolean includeWorkerSalary) {
        this.includeWorkerSalary = includeWorkerSalary;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public boolean isReplacementOfBouncedPayment() {
        return replacedBouncedPaymentId != null;
    }

    public boolean isReplacementOfFuturePayment() {
        return replacedFuturePaymentId != null;
    }

    public boolean isCreationOfNewPayment() {
        return !isReplacementOfBouncedPayment() && !isReplacementOfFuturePayment();
    }

    public boolean isAddedFromPaymentForApproval() { return addedFromPaymentForApproval; }

    public void setAddedFromPaymentForApproval(boolean addedFromPaymentForApproval) { this.addedFromPaymentForApproval = addedFromPaymentForApproval; }

    public void generateNewContractPayment() {
        contractPayment = initContractPaymentProps();

        Setup.getApplicationContext().getBean(ContractPaymentController.class)
                .addPaymentsByAccountant(
                        getContractPaymentConfirmationToDo().getContractPaymentTerm(),
                        Arrays.asList(contractPayment));
    }

    public ContractPayment initContractPaymentProps() {
        ContractPayment p = new ContractPayment();
        p.setContractPaymentTerm(getContractPaymentConfirmationToDo().getContractPaymentTerm());
        p.setConfirmationTodoAdded(true);
        p.setPaymentMethod(getPaymentMethod());
        p.setPaymentType(getPaymentType());
        p.setSubType(getSubType());
        p.setDate(getPaymentDate());
        p.setAmount(getAmount());
        p.setAttachments(getAttachments());
        p.setIsProRated(isProrated());
        p.setIsInitial(isInitial());
        p.setConfirmed(Boolean.FALSE);
        p.setOnline(getContractPaymentConfirmationToDo().isPayingOnline());
        p.setReplaceOf(getReplacedBouncedPaymentId() != null?
                Setup.getRepository(PaymentRepository.class).findOne(getReplacedBouncedPaymentId()) : null);
        p.setDescription(StringUtils.isEmpty(getDescription()) ?
                contractPaymentConfirmationToDo.getDescription() :
                getDescription());
        p.setIncludeWorkerSalary(isIncludeWorkerSalary());
        if (isIncludeWorkerSalary()) {
            // related ACC-8422
            if (getWorkerSalary() != 0.0) {
                p.setWorkerSalary(getWorkerSalary());
            }

            if (getDiscountAmount() == 0D) {
                setDiscountAmount(getContractPaymentConfirmationToDo().getContractPaymentTerm().getDiscount());
            }
            p.setDiscountAmount(getDiscountAmount());
        }
        p.setVatPaidByClient(getVatPaidByClient());
        p.setMoreAdditionalDiscount(getMoreAdditionalDiscount());

        ContractPaymentTerm cpt = getContractPaymentConfirmationToDo().getContractPaymentTerm();
        // if the incoming payment is marked as affected by additional discount and there is discount on the CPT
        if(getAffectedByAdditionalDiscount() && cpt.getAdditionalDiscount() != null &&
                cpt.getAdditionalDiscountMonths() != null && (getAdditionAmount() == null || getAdditionAmount() == 0.0) &&
                (getAdditionalDiscountAmount() == null || getAdditionalDiscountAmount() == 0.0) &&
                (getMoreAdditionalDiscount() == null || getMoreAdditionalDiscount() == 0.0)) {

            PicklistItem t = Setup.getRepository(PicklistItemRepository.class).findOne(getPaymentType().getId());
            AbstractPaymentTypeConfig config = cpt.getPaymentTypeConfig(t.getCode());

            Logger.getLogger(ContractPaymentWrapper.class.getName()).info("config: " +
                    t.getCode() + (config == null ? "; null" :
                            ";" + config.getId() + "; " + config.getEntityType() +
                            "; " + config.getAffectedByAdditionalDiscount()));

            // extra match -> additional discount is applied on one type only per CPT ->
            // verify the incoming payment is of the type in question
            // ACC-7390 #A.10
            CalculateDiscountsWithVatService calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);

            Double additionalDiscountAmountPerPayment = calculateDiscountsWithVatService.getAdditionalDiscountPerPayment(cpt, new LocalDate(getPaymentDate()));
            if (additionalDiscountAmountPerPayment != null && additionalDiscountAmountPerPayment != 0.0) {
                p.setAdditionalDiscountAmount(additionalDiscountAmountPerPayment);
            }

            // Apply more additional discount
            calculateDiscountsWithVatService.updateAmountContractPaymentTermDetailsViaContractPayment(cpt, p);
        } else if (getAdditionAmount() != null && getAdditionAmount() != 0.0) {
            p.setAdditionAmount(getAdditionAmount());
        } else {
            if (getAdditionalDiscountAmount() != null && getAdditionalDiscountAmount() != 0.0) {
                p.setAdditionalDiscountAmount(getAdditionalDiscountAmount());
            }
            if (getMoreAdditionalDiscount() != null && getMoreAdditionalDiscount() != 0.0) {
                p.setMoreAdditionalDiscount(getMoreAdditionalDiscount());
            }
        }

        return p;
    }

    public void updateFromPayment(Payment payment) {
        setVatPaidByClient(payment.getVatPaidByClient());
        setIncludeWorkerSalary(payment.getIncludeWorkerSalary());
        setWorkerSalary(payment.getWorkerSalary());
        setInitial(payment.getIsInitial());
        setProrated(payment.getIsProRated());
        if (getReplacedBouncedPaymentId() == null &&
                getDiscountAmount().equals(0.0)) {
            setAmount(payment.getAmountOfPayment());
        }

        setPaymentDate(payment.getDateOfPayment());
        setPaymentType(payment.getTypeOfPayment());
        setSubType(payment.getSubType());

        ContractPayment cp = payment.getContractPayment();
        if (cp == null) return;

        // ACC-7151 + ACC-7864
        setMoreAdditionalDiscount(cp.getMoreAdditionalDiscount());
        setAdditionalDiscountAmount(cp.getAdditionalDiscountAmount());
        if (cp.getAdditionAmount() != null && cp.getAdditionAmount() > 0) {
            setAdditionAmount(cp.getAdditionAmount());
        }
        if (!getAffectedByAdditionalDiscount()) {
            setAffectedByAdditionalDiscount(cp.getAdditionalDiscountAmount() != null &&
                    cp.getAdditionalDiscountAmount() > 0.0);
        }
        setDiscountAmount(cp.getDiscountAmount());
    }
    
    public void updateFromContractPayment() {
        setVatPaidByClient(contractPayment.getVatPaidByClient());
        setIncludeWorkerSalary(contractPayment.getIncludeWorkerSalary());
        setPaymentType(contractPayment.getPaymentType());
        setSubType(contractPayment.getSubType());
    }

    public Double getDiscountAmount() { return discountAmount; }

    public void setDiscountAmount(Double discountAmount) { this.discountAmount = discountAmount; }

    // ACC-7093
    public Double getAdditionalDiscountAmount() { return additionalDiscountAmount; }

    public void setAdditionalDiscountAmount(Double additionalDiscountAmount) { this.additionalDiscountAmount = additionalDiscountAmount; }

    public Double getMoreAdditionalDiscount() { return moreAdditionalDiscount == null ? 0.0 : moreAdditionalDiscount; }

    public void setMoreAdditionalDiscount(Double moreAdditionalDiscount) { this.moreAdditionalDiscount = moreAdditionalDiscount; }

    public Double getAdditionAmount() { return additionAmount; }

    public void setAdditionAmount(Double additionAmount) { this.additionAmount = additionAmount; }

    public boolean isIncludeUpgradingFee() { return includeUpgradingFee; }

    public void setIncludeUpgradingFee(boolean includeUpgradingFee) { this.includeUpgradingFee = includeUpgradingFee; }

    public Double getWorkerSalary() { return workerSalary != null ? workerSalary : 0D; }

    public void setWorkerSalary(Double workerSalary) { this.workerSalary = workerSalary; }

}