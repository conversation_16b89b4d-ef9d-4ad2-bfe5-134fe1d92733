package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdNameMobileSerializer;
import com.magnamedia.entity.serializer.IdNameSerializer;
import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 28, 2017
 */
@Entity
public class NonClientPDC extends BaseEntity {
    
    @Label
    @Column(nullable = false)
    private String name;
    
    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private TenancyContract tenancyContract;
    
    //Jirra ACC-1100 ACC-1333
    @JsonSerialize(using = IdNameSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Bucket bankIssuerBucket;
    
    @Column(nullable = false)
    private Integer amount;
    
    @Column
    private String leaseCompanyName;
    
    @Column(nullable = false)
    private String chequeIssuerName;
    
    @Column
    private String propertyDiscription;
    
    @Column(nullable = false)
    private Date chequeDueDate;
    
    @Column(nullable = false)
    private Date contractEndDate;
    
    @Column(nullable = false)
    private Boolean withdrawn = false;
    
    @Column(nullable = false)
    private Boolean bounced = false;
    
    //Jirra ACC-1101
    @Column
    private Integer vatAmount;
    
    @Column
    private String license;
    
    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    private boolean doneByCoo = false;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TenancyContract getTenancyContract() {
        return tenancyContract;
    }

    public void setTenancyContract(TenancyContract tenancyContract) {
        this.tenancyContract = tenancyContract;
    }

    public Bucket getBankIssuerBucket() {
        return bankIssuerBucket;
    }

    public void setBankIssuerBucket(Bucket bankIssuerBucket) {
        this.bankIssuerBucket = bankIssuerBucket;
    }
    

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getLeaseCompanyName() {
        return leaseCompanyName;
    }

    public void setLeaseCompanyName(String leaseCompanyName) {
        this.leaseCompanyName = leaseCompanyName;
    }

    public String getChequeIssuerName() {
        return chequeIssuerName;
    }

    public void setChequeIssuerName(String chequeIssuerName) {
        this.chequeIssuerName = chequeIssuerName;
    }

    public String getPropertyDiscription() {
        return propertyDiscription;
    }

    public void setPropertyDiscription(String propertyDiscription) {
        this.propertyDiscription = propertyDiscription;
    }

    public Date getChequeDueDate() {
        return chequeDueDate;
    }

    public void setChequeDueDate(Date chequeDueDate) {
        this.chequeDueDate = chequeDueDate;
    }

    public Date getContractEndDate() {
        return contractEndDate;
    }

    public void setContractEndDate(Date contractEndDate) {
        this.contractEndDate = contractEndDate;
    }

    public Boolean getWithdrawn() {
        return withdrawn != null? withdrawn : false;
    }

    public void setWithdrawn(Boolean withdrawn) {
        this.withdrawn = withdrawn;
    }

    public Boolean getBounced() {
        return bounced != null? bounced : false;
    }

    public void setBounced(Boolean bounced) {
        this.bounced = bounced;
    }
    
    //Jirra ACC-1101
    public Integer getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Integer vatAmount) {
        this.vatAmount = vatAmount;
    }

    public String getLicense() {
        return license;
    }

    public void setLicense(String license) {
        this.license = license;
    }

    public Expense getExpense() {
        return expense;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public boolean isDoneByCoo() { return doneByCoo; }

    public void setDoneByCoo(boolean doneByCoo) { this.doneByCoo = doneByCoo; }
}
