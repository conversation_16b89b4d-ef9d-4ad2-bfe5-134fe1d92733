package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;


/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 13, 2019
 */
@Entity
public class BlockLog extends BaseEntity {

    public BlockLog() {
        this.setUnblock(false);
    }

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Client client;

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    @Column(nullable = false)
    private Boolean unblock;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem type;

    private String notes;

    public Boolean isUnblock() {
        return unblock;
    }

    public void setUnblock(Boolean unblock) {
        this.unblock = unblock;
    }

    public PicklistItem getType() {
        return type;
    }

    public void setType(PicklistItem type) {
        this.type = type;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

}

