package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.core.Setup;
import com.magnamedia.repository.AdhocCompanyRepository;
import com.magnamedia.repository.BaseCompanyReportRepository;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@Entity
public class AdhocCompany extends BaseReportCompany {

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "PLCompany")
    protected List<AdhocNode> children = new ArrayList();

    @Transient
    protected Double bucketsValue;

    @Override
    public List<BasePLNode> getSortedChildren() {
        if (this.children != null && !this.children.isEmpty())
            return this.children.stream()
                    .filter(x -> x.getParent() == null)
                    .sorted(Comparator.comparingInt(AdhocNode::getNodeOrder))
                    .collect(Collectors.toList());
        else
            return this.children.stream().map(adhocNode -> ((BasePLNode) adhocNode)).collect(Collectors.toList());
    }

    @Override
    public List<BasePLNode> getChildren() {
        return this.children.stream().map(adhocNode -> ((BasePLNode) adhocNode)).collect(Collectors.toList());
    }

    @Override
    public void setChildren(List<BasePLNode> children) {
        this.children = children.stream().map(adhocNode -> ((AdhocNode) adhocNode)).collect(Collectors.toList());
    }

    @Override
    public void calculateValue(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        bucketsValue = 0.0D;
        profitAdjustmentsValue = 0.0D;

        for (BasePLNode plNode : getChildren()) {
            bucketsValue += plNode.calculateAndSetValue(fromDate, toDate, searchCriteria);
        }

        //Jirra ACC-583
        calculateRatio(getSortedChildren(), bucketsValue, 1000L);

        this.revenuesValue = 0.0;
        this.expensesValue = 0.0;
    }

    @Override
    public void validate() {
        super.validate();
    }

    @Override
    @JsonIgnore
    public String getReportTitle() {
        return "Dynamic Report: " + this.getName();
    }

    @Override
    @JsonIgnore
    public BaseCompanyReportRepository getRepository() {
        return Setup.getRepository(AdhocCompanyRepository.class);
    }
}

