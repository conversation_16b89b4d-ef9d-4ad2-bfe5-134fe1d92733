package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.extra.VisaStatementStatus;
import com.magnamedia.extra.VisaStatementType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.sql.Date;

@Entity
public class VisaStatement extends BaseEntity {

    @Column
    @Enumerated(EnumType.STRING)
    private VisaStatementType type;

    @Column
    @Enumerated(EnumType.STRING)
    private VisaStatementStatus status;

    @Column
    private Date start;

    @Column
    private Date end;

    @Column(columnDefinition = "boolean default true")
    private Boolean canBeDeleted = Boolean.TRUE;

    public VisaStatementType getType() {
        return type;
    }

    public void setType(VisaStatementType type) {
        this.type = type;
    }

    public VisaStatementStatus getStatus() {
        if (this.status == null){
            return VisaStatementStatus.PENDING;
        }
        return status;
    }

    public void setStatus(VisaStatementStatus status) {
        this.status = status;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }

    public Boolean getCanBeDeleted() {
        return canBeDeleted;
    }

    public void setCanBeDeleted(Boolean canBeDeleted) {
        this.canBeDeleted = canBeDeleted;
    }
}
