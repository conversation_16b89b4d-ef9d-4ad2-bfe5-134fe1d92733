package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.OneToOne;
import javax.persistence.criteria.CriteriaBuilder;
import java.util.Date;

//ACC-3854
@Entity
public class SignatureCollectionFlowToDo extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private Contract contract;

    private Integer trials;

    private Integer reminder;

    @Column
    private Date lastMessageSentDate;

    @Column
    private boolean isActive;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Integer getTrials() {
        return trials == null ? 0 : trials;
    }

    public void setTrials(Integer trials) {
        this.trials = trials;
    }

    public Integer getReminder() {
        return reminder == null ? 0 : reminder;
    }

    public void setReminder(Integer reminder) {
        this.reminder = reminder;
    }

    public Date getLastMessageSentDate() {
        return lastMessageSentDate;
    }

    public void setLastMessageSentDate(Date lastMessageSentDate) {
        this.lastMessageSentDate = lastMessageSentDate;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }
}
