package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.repository.ContractPaymentTypeRepository;
import com.magnamedia.service.CalculateDiscountsWithVatService;
import com.magnamedia.service.ContractService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Min;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 *         ACC-984 SAL-1061
 */
@Entity
@Where(clause = "IS_DELETED = false And (DIRECT_DEBIT_ID is null OR exists( select dd.id from DIRECTDEBITS dd where dd.id = DIRECT_DEBIT_ID and dd.IS_DELETED = false ))")
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ContractPayment extends BaseEntity {
    protected static final Logger logger = Logger.getLogger(ContractPayment.class.getName());
    protected static final String prefix = "MMM ";

    //Jirra ACC-1435
    public final static String NO_VAT_TAG = "NO_VAT";
    public final static String EXCLUDED_FROM_DDC_CHECK_TAG = "excluded_from_ddc_check";
    @Column(nullable = false)
    private boolean oneTime = false;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
//    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem paymentType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem subType;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private PaymentMethod paymentMethod;

    @Column(nullable = false)
    @Min(0)
    private Double amount;

    @Column(nullable = false)
    //@Min(0)
    private Double discountAmount = 0.0;

    //Jirra ACC-952
    @Column(nullable = false)
    @Min(0)
    private Double additionalDiscountAmount = 0.0;

    @Column(columnDefinition = "double default 0")
    @Min(0)
    private Double additionAmount = 0.0;

    @Column(nullable = false)
    @Temporal(TemporalType.DATE)
    private Date date;

    @Column(length = 1000)
    private String description;

    // ACC-3225
    @Column(nullable = false, length = 1000)
    private String descriptionForSigningScreen;

    @Column(nullable = false)
    private Boolean confirmed = Boolean.FALSE;

    @Column(columnDefinition = "boolean default false")
    private Boolean paid = Boolean.FALSE;

    @Column(nullable = false)
    private Boolean isCalculated = Boolean.FALSE;

    @Column(nullable = false)
    private Boolean isProRated = Boolean.FALSE;

    // ACC-2184
    @Column(nullable = false)
    private Boolean isProRatedPlusMonth = Boolean.FALSE;

    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private DirectDebit directDebit;

    @Column(columnDefinition = "boolean default false")
    private boolean requiredForUnfitToWork = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Payment replaceOf;

    @Column(nullable = true)
    private Double vatPercent;

    @Column(nullable = true)
    private Double vat;

    @Column(nullable = true)
    private Double paymentWithoutVAT;

    @Column(nullable = true)
    private Double visaFees;

    @Column(nullable = true)
    private Double visaFeesWithoutVAT = 0.0;

    @Column
    private Double visaFeesVAT = 0.0;

    @Column
    private Boolean includeWorkerSalary;

    // ACC-1435
    @Column(nullable = false)
    private Boolean addedByAccountant = false;

    // ACC-1435
    @Column(nullable = false)
    private Boolean isInitial;

    // SAL-884
    @Transient
    private boolean isTrasient = false;

    // SAL-884
    @Column(columnDefinition = "boolean default false")
    private boolean confirmationTodoAdded = false;

    // ACC-984
    @JsonIgnore
    @Column(nullable = false)
    private Boolean isDeleted = false;

    // ACC-3388
    @Column
    @ColumnDefault("false")
    private boolean online = false;

    // ACC-3225
    @Column
    private Boolean affectsPaidEndDate;

    // ACC-3296
    @Transient
    private boolean generateManualDDFs = true;

    @Column
    private Boolean vatPaidByClient;

    @Column(columnDefinition = "double default 0")
    @Min(0)
    private Double moreAdditionalDiscount = 0.0;

    @Column(columnDefinition = "boolean default false")
    private boolean waived = false;

    @Column(columnDefinition = "boolean default false")
    private boolean addedManuallyFromClientProfile = false;

    @Column
    private Long generatedPaymentId;

    @Column
    private Double workerSalaryVAT = 0.0;

    @Column
    private Double workerSalaryWithoutVAT = 0.0;

    @Column
    private Double workerSalary;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitGenerationPlan directDebitGenerationPlan;

    @Transient
    private Long contractPaymentTypeId;

    public Boolean getPaid() {
        return paid != null && paid;
    }

    public void setPaid(Boolean paid) {
        this.paid = paid;
    }

    public ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public PicklistItem getPaymentType() {
        return paymentType;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public Double getAmount() {
        return amount;
    }

    public Date getDate() {
        return date;
    }

    public String getDescription() {
        return description;
    }

    public String getDescriptionForSigningScreen() {
        return descriptionForSigningScreen;
    }

    public void setDescriptionForSigningScreen(String descriptionForSigningScreen) {
        this.descriptionForSigningScreen = descriptionForSigningScreen;
    }
    
    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public void setPaymentType(PicklistItem paymentType) {
        this.paymentType = paymentType;
    }

    public PicklistItem getSubType() { return subType; }

    public void setSubType(PicklistItem subType) { this.subType = subType; }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Double getAdditionalDiscountAmount() {
        return additionalDiscountAmount == null ? 0.0 : additionalDiscountAmount;
    }

    public void setAdditionalDiscountAmount(Double additionalDiscountAmount) {
        this.additionalDiscountAmount = additionalDiscountAmount;
    }

    public Double getAdditionAmount() { return additionAmount; }

    public void setAdditionAmount(Double additionAmount) { this.additionAmount = additionAmount; }

    public Boolean getIsCalculated() {
        return isCalculated;
    }

    public void setIsCalculated(Boolean isCalculatedPayment) {
        this.isCalculated = isCalculatedPayment;
    }

    public Boolean getIsProRated() {
        return isProRated != null && isProRated;
    }

    public void setIsProRated(Boolean isProRated) {
        this.isProRated = isProRated;
    }

    public Boolean getProRatedPlusMonth() {
        return isProRatedPlusMonth == null ? false : isProRatedPlusMonth;
    }

    public void setProRatedPlusMonth(Boolean proRatedPlusMonth) {
        isProRatedPlusMonth = proRatedPlusMonth;
    }

    public DirectDebit getDirectDebit() {
        return directDebit;
    }

    public void setDirectDebit(DirectDebit directDebit) {
        this.directDebit = directDebit;
    }

    public Payment getReplaceOf() {
        return replaceOf;
    }

    public void setReplaceOf(Payment replaceOf) {
        this.replaceOf = replaceOf;
    }

    public Double getVatPercent() {
        return this.vatPercent == null ? 0.0 : vatPercent;
    }

    public void setVatPercent(Double vatPercent) {
        this.vatPercent = vatPercent;
    }

    public Double getVat() {
        return vat;
    }

    // ACC-1435
    public void setVat(Double vat) {
        this.vat = Utils.roundDownMode(vat, 1);
    }

    public Double getPaymentWithoutVAT() {
        return paymentWithoutVAT;
    }

    public void setPaymentWithoutVAT(Double paymentWithoutVAT) {
        this.paymentWithoutVAT = paymentWithoutVAT;
    }

    public Double getVisaFees() {
        return visaFees;
    }

    public void setVisaFees(Double visaFees) {
        this.visaFees = visaFees;
    }

    public Double getVisaFeesWithoutVAT() {
        return visaFeesWithoutVAT;
    }

    public void setVisaFeesWithoutVAT(Double visaFeesWithoutVAT) {
        this.visaFeesWithoutVAT = visaFeesWithoutVAT;
    }

    //    public Boolean getIncludeWorkerSalary() {
//        return includeWorkerSalary;
//    }

    // ACC-1435
    public Boolean getIncludeWorkerSalary() {
        if (includeWorkerSalary != null) return includeWorkerSalary;

        if (getContractPaymentTerm() == null || getContractPaymentTerm().getContract() == null) {
            return false;
        }

        if (getContractPaymentTerm().getContract().getContractProspectType() == null ||
                getContractPaymentTerm().getContract().getContractProspectType().getId() == null) {
            return false;
        }

        PicklistItem prospectType = Setup.getRepository(PicklistItemRepository.class)
                .findOne(getContractPaymentTerm().getContract().getContractProspectType().getId());
        if (!prospectType.getCode().equals(AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE)) return false;

        // ACC-3306 #1
        if (PaymentHelper.isMonthlyPayment(getPaymentType()) &&
                (getContractPaymentTerm().getDiscount() == null || getContractPaymentTerm().getDiscount().equals(0D))) {
            return true;
        }

        return !discountAmount.equals(0D);
    }

    public Boolean getAddedByAccountant() {
        return addedByAccountant;
    }

    public void setAddedByAccountant(Boolean addedByAccountant) {
        this.addedByAccountant = addedByAccountant;
    }

    public void setIsInitial(Boolean isInitial) {
        this.isInitial = isInitial;
    }

    public Boolean getIsInitial() {
        return isInitial != null && isInitial;
    }

    public boolean isOneTime() {
        return oneTime;
    }

    public void setOneTime(boolean oneTime) {
        this.oneTime = oneTime;
    }

    public void setIncludeWorkerSalary(Boolean includeWorkerSalary) {
        this.includeWorkerSalary = includeWorkerSalary;
    }

    public boolean isConfirmationTodoAdded() {
        return confirmationTodoAdded;
    }
    
    public void setConfirmationTodoAdded(boolean confirmationTodoAdded) {
        this.confirmationTodoAdded = confirmationTodoAdded;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public Boolean getAffectsPaidEndDate() {
        return affectsPaidEndDate;
    }

    public void setAffectsPaidEndDate(Boolean affectsPaidEndDate) {
        this.affectsPaidEndDate = affectsPaidEndDate;
    }

    public boolean isGenerateManualDDFs() {
        return generateManualDDFs;
    }

    public void setGenerateManualDDFs(boolean generateManualDDFs) {
        this.generateManualDDFs = generateManualDDFs;
    }

    public Boolean getVatPaidByClient() { return vatPaidByClient; }

    public void setVatPaidByClient(Boolean vatPaidByClient) {
        this.vatPaidByClient = vatPaidByClient;
    }

    //Jirra ACC-1435
    @BeforeInsert
    public void checkEntityBeforeInsert() {

        // ACC-8633
        Contract contract = this.getContractPaymentTerm().getContract();
        if (getIncludeWorkerSalary() && (getWorkerSalary() == null || getWorkerSalary() == 0D) && contract.getWorkerSalaryNew() != null) {
            setWorkerSalary(contract.getWorkerSalaryNew());
        }

        checkEntity();

        //ACC-4176

        if (paymentType.getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE)) {
            if (!contract.getClientPaidVat()) {
                Map map = new HashMap();
                map.put("id", contract.getId());
                map.put("clientPaidVat", true);

                Setup.getApplicationContext()
                        .getBean(ContractService.class)
                        .updateContractFromClientMgtAsync(map);

            }

            setVatPaidByClient(true);
        } else { 
            setVatPaidByClient(!paymentType.hasTag(NO_VAT_TAG));
        }
    }

    @BeforeUpdate
    private void checkEntity() {
        // put all functions that related with check entity here
        if (this.descriptionForSigningScreen == null || this.descriptionForSigningScreen.trim().isEmpty()) {
            this.descriptionForSigningScreen = this.description;
        }

        Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .updateVatFields(this);
       
        if(this.isInitial == null) {
            this.isInitial = this.getContractPaymentTerm().getDiscount() != null &&
                this.getContractPaymentTerm().getDiscount() != 0 && 
                this.getDiscountAmount() == 0 &&
                this.getPaymentType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);
        }
    }

    @AfterInsert
    private void checkEntityAfterInsert() {

        if (getContractPaymentTypeId() == null) return;

        ContractPaymentTypeRepository contractPaymentTypeRepository = Setup.getRepository(ContractPaymentTypeRepository.class);
        ContractPaymentType contractPaymentType = contractPaymentTypeRepository.findByIdAndPostponedDdGeneratedFalse(getContractPaymentTypeId());
        if (contractPaymentType != null) {
            contractPaymentType.setPostponedDdGenerated(true);
            contractPaymentTypeRepository.save(contractPaymentType);
        }
    }

    public Double getMoreAdditionalDiscount() { return moreAdditionalDiscount == null ? 0.0 : moreAdditionalDiscount; }

    public void setMoreAdditionalDiscount(Double moreAdditionalDiscount) { this.moreAdditionalDiscount = moreAdditionalDiscount; }

    public boolean isWaived() { return waived; }

    public void setWaived(boolean waived) { this.waived = waived; }

    public boolean isAddedManuallyFromClientProfile() { return addedManuallyFromClientProfile; }

    public void setAddedManuallyFromClientProfile(boolean addedManuallyFromClientProfile) {
        this.addedManuallyFromClientProfile = addedManuallyFromClientProfile;
    }

    public Long getGeneratedPaymentId() { return generatedPaymentId; }

    public void setGeneratedPaymentId(Long generatedPaymentId) { this.generatedPaymentId = generatedPaymentId; }

    public Double getWorkerSalaryVAT() { return workerSalaryVAT == null ? 0.0 : workerSalaryVAT; }

    public void setWorkerSalaryVAT(Double workerSalaryVAT) { this.workerSalaryVAT = Utils.roundDownMode(workerSalaryVAT, 1);; }

    public Double getVisaFeesVAT() { return visaFeesVAT == null ? 0.0 :visaFeesVAT; }

    public void setVisaFeesVAT(Double visaFeesVAT) { this.visaFeesVAT = Utils.roundDownMode(visaFeesVAT, 1); }

    public Double getWorkerSalaryWithoutVAT() { return workerSalaryWithoutVAT == null ? 0.0 : workerSalaryWithoutVAT; }

    public void setWorkerSalaryWithoutVAT(Double workerSalaryWithoutVAT) {
        this.workerSalaryWithoutVAT = workerSalaryWithoutVAT;
    }

    public Double getWorkerSalary() { return workerSalary != null ? workerSalary : 0D; }

    public void setWorkerSalary(Double workerSalary) { this.workerSalary = workerSalary; }

    public boolean isRequiredForUnfitToWork() { return requiredForUnfitToWork; }

    public void setRequiredForUnfitToWork(boolean requiredForUnfitToWork) { this.requiredForUnfitToWork = requiredForUnfitToWork; }

    public Long getContractPaymentTypeId() { return contractPaymentTypeId; }

    public void setContractPaymentTypeId(Long contractPaymentTypeId) { this.contractPaymentTypeId = contractPaymentTypeId; }

    @JsonIgnore
    public DirectDebitGenerationPlan getDirectDebitGenerationPlan() { return directDebitGenerationPlan; }

    public void setDirectDebitGenerationPlan(DirectDebitGenerationPlan directDebitGenerationPlan) { this.directDebitGenerationPlan = directDebitGenerationPlan; }
}