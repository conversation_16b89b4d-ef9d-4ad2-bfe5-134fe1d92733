package com.magnamedia.entity;

import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.workflow.newversion.services.graphicdesigner.GraphicToDoType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 11, 2021
 *         Jirra ACC-2860
 */

@Entity
public class GraphicDesignerToDo extends WorkflowEntity implements Serializable {

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private GraphicToDoType toDoType;

    private Long contractId;

    private Long clientId;

    public GraphicDesignerToDo() {
        super("");
    }

    public GraphicDesignerToDo(String startTaskName) {
        super(startTaskName);
    }

    @Override
    public String getFinishedTaskName() {
        return "Completed";
    }

    @Override
    public List<FormField> getForm(String string) {
        return Collections.emptyList();
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public GraphicToDoType getToDoType() {
        return toDoType;
    }

    public GraphicDesignerToDo setToDoType(GraphicToDoType toDoType) {
        this.toDoType = toDoType;
        return this;
    }
}