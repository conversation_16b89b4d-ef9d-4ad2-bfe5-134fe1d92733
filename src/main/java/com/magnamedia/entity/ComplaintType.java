package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.Classification;
import com.magnamedia.module.type.SeriousnessLevel;
import com.magnamedia.extra.SeriousnessLevelHolder;
import com.magnamedia.repository.ComplaintRepository;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import org.hibernate.envers.NotAudited;

/**
 *
 * <AUTHOR> Esrawi <<EMAIL>>
 * Created at Nov 5, 2017
 * <AUTHOR> kanaan <<EMAIL>>
 * Updated on Jul 28, 2019
 * Jirra ACC-737
 */
@Entity
public class ComplaintType extends BaseEntity implements Serializable, SeriousnessLevelHolder {

    @Column
    @Label
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ComplaintCategory category;

    @Column(columnDefinition = "boolean default true")
    private Boolean causeFaultReplacement;

    @Enumerated(EnumType.STRING)
    @Column
    private Classification classification;

    @Column
    private boolean disabled;

    @Enumerated(EnumType.STRING)
    @Column
    private SeriousnessLevel seriousnessLevel;

    @Column
    private String code;

    @Column(columnDefinition = "boolean default false")
    private boolean isReplacementReason;

    @Column
    private String deductionCode;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "COMPLAINT_TYPES_TAGS",
            joinColumns = @JoinColumn(
                    name = "COMPLAINT_TYPE",
                    referencedColumnName = "ID"),
            inverseJoinColumns = @JoinColumn(
                    name = "TAG",
                    referencedColumnName = "ID")
    )
    private List<Tag> tags;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    @NotAudited
    private PicklistItem type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ComplaintCategory getCategory() {
        return category;
    }

    public void setCategory(ComplaintCategory category) {
        this.category = category;
        if (category != null) {
            this.classification = category.getClassification();
        }
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public SeriousnessLevel getSeriousnessLevel() {
        return seriousnessLevel;
    }

    public void setSeriousnessLevel(SeriousnessLevel seriousnessLevel) {
        this.seriousnessLevel = seriousnessLevel;
    }

    public Classification getClassification() {
        return classification;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean getIsReplacementReason() {
        return isReplacementReason;
    }

    public void setIsReplacementReason(boolean replacementReason) {
        isReplacementReason = replacementReason;
    }

    public long getComplaintsCount() {
        if (isNewInstance()) {
            return 0;
        } else {
            return ((ComplaintRepository) Setup.getRepository(
                    ComplaintRepository.class))
                    .countByType(this);
        }
    }

    public Boolean getCauseFaultReplacement() {
        return causeFaultReplacement;
    }

    public void setCauseFaultReplacement(Boolean causeFaultReplacement) {
        this.causeFaultReplacement = causeFaultReplacement;
    }

    public String getDeductionCode() {
        return deductionCode;
    }

    public void setDeductionCode(String deductionCode) {
        this.deductionCode = deductionCode;
    }

    @JsonSerialize(contentUsing = ToStringSerializer.class)
    public List<Tag> getTags() {
        if (tags == null) {
            return new ArrayList<>();
        }
        return tags;
    }

    @JsonIgnore
    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public Tag getTagValue(String key) {
        for (Tag tag : this.getTags()) {
            if (tag.isComplex()
                    && tag.getKey()
                            .equalsIgnoreCase(key)) {
                return tag;
            }
        }
        return null;
    }

    public boolean hasTag(String key) {
        if (!getTags()
                .stream()
                .anyMatch((tag)
                        -> (tag.getKey()
                        .equalsIgnoreCase(key)))) {
        } else {
            return true;
        }
        return false;
    }

    public PicklistItem getType() {
        return type;
    }

    public void setType(PicklistItem type) {
        this.type = type;
    }
    
    
    
}
