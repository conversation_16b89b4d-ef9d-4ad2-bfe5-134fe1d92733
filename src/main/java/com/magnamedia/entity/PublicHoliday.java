package com.magnamedia.entity;

import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jan 28, 2020
 *         Jirra ACC-1227
 */

@Entity
public class PublicHoliday extends BaseEntity {


    @Column
    @Label
    private String holidayTitle;

    @Column
    private Long year;

    @Column
    private Date startDate;

    @Column
    private Date endDate;

    public String getHolidayTitle() {
        return holidayTitle;
    }

    public void setHolidayTitle(String holidayTitle) {
        this.holidayTitle = holidayTitle;
    }

    public Long getYear() {
        return year;
    }

    public void setYear(Long year) {
        this.year = year;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
