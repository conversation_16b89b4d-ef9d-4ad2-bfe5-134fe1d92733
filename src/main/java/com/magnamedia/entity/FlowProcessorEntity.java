package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.repository.BaseAdditionalInfoRepository;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.Utils;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.ExtensionFlowService;

import java.util.*;
import javax.persistence.*;

/**
 *
 * <AUTHOR>
 */

@Entity
public class FlowProcessorEntity extends WorkflowEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FlowEventConfig flowEventConfig;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FlowSubEventConfig currentSubEvent;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentConfirmationToDo contractPaymentConfirmationToDo;

    private int trials;
    private int reminders;
    private int currentFlowRun;
    private Date lastExecutionDate;
    private boolean pauseEventTermination = false;
    private Date lastResetDate;

    @OneToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebit directDebit;

    @Transient
    private Integer incrementedTrials;
    @Transient
    private Integer incrementedReminders;
    @Transient
    private String contractCancellationReason;

    @Lob
    private String additionalInfo;

    @Column(columnDefinition = "boolean default false")
    private boolean stoppedDueContractTerminated = false; // Another flow canceled the contract, causing the flow to stop

    @Column(columnDefinition = "boolean default false")
    private boolean causedTermination = false; // Contract termination was caused by this flow

    @Column(columnDefinition = "boolean default false")
    private boolean stoppedDueFreezeContract = false;

    @Column(columnDefinition = "boolean default false")
    private boolean ignorePaidEndDate = false;

    @Column
    private Date lastMessageDate;

    @Transient
    private ContractPaymentTerm activeCpt;

    public FlowEventConfig getFlowEventConfig() {
        return flowEventConfig;
    }

    public void setFlowEventConfig(FlowEventConfig flowEventConfig) {
        this.flowEventConfig = flowEventConfig;
    }

    private ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public ContractPaymentTerm getActiveCpt() {

        if (activeCpt == null) {
            activeCpt = getContractPaymentTerm().isActive() ?
                    getContractPaymentTerm() :
                    getContract().getActiveContractPaymentTerm();
        }

        return activeCpt;
    }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public int getTrials() {
        return trials;
    }

    public void setTrials(int trials) {
        this.trials = trials;
    }

    public int getReminders() {
        return reminders;
    }

    public void setReminders(int reminders) {
        this.reminders = reminders;
    }

    public FlowProcessorEntity(String startTaskName) {
        super(startTaskName);
    }

    public FlowProcessorEntity() {
        this("");
    }

    public int getCurrentFlowRun() {
        return currentFlowRun;
    }

    public void setCurrentFlowRun(int currentFlowRun) {
        this.currentFlowRun = currentFlowRun;
    }

    public FlowSubEventConfig getCurrentSubEvent() {
        return currentSubEvent;
    }

    public void setCurrentSubEvent(FlowSubEventConfig currentSubEvent) {
        this.currentSubEvent = currentSubEvent;
    }

    public Date getLastExecutionDate() {
        return lastExecutionDate;
    }

    public void setLastExecutionDate(Date lastExecutionDate) {
        this.lastExecutionDate = lastExecutionDate;
    }

    public ContractPaymentConfirmationToDo getContractPaymentConfirmationToDo() {
        return contractPaymentConfirmationToDo;
    }

    public void setContractPaymentConfirmationToDo(ContractPaymentConfirmationToDo contractPaymentConfirmationToDo) {
        this.contractPaymentConfirmationToDo = contractPaymentConfirmationToDo;
    }

    public boolean isPauseEventTermination() {
        return pauseEventTermination;
    }

    public void setPauseEventTermination(boolean pauseEventTermination) {
        this.pauseEventTermination = pauseEventTermination;
    }

    public String getContractCancellationReason() { return contractCancellationReason; }

    public void setContractCancellationReason(String contractCancellationReason) {
        this.contractCancellationReason = contractCancellationReason;
    }

    @Override
    public String getFinishedTaskName() {
        return "";
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return new ArrayList<>();
    }

    @JsonIgnore
    public Contract getContract() {
        return getContractPaymentTerm().getContract();
    }

    @JsonIgnore
    public DirectDebit getDirectDebit() {
        return directDebit;
    }

    public void setDirectDebit(DirectDebit directDebit) {
        this.directDebit = directDebit;
    }

    public Date getLastResetDate() {
        return lastResetDate;
    }

    public void setLastResetDate(Date lastResetDate) {
        this.lastResetDate = lastResetDate;
    }

    public Integer getIncrementedTrials() {
        return incrementedTrials != null ? incrementedTrials : getTrials();
    }

    public boolean isStoppedDueContractTerminated() { return stoppedDueContractTerminated; }

    public void setStoppedDueContractTerminated(boolean stoppedDueContractTerminated) {
        this.stoppedDueContractTerminated = stoppedDueContractTerminated;
    }

    public boolean isCausedTermination() { return causedTermination; }

    public void setCausedTermination(boolean causedTermination) { this.causedTermination = causedTermination; }

    public void setIncrementedTrials(Integer incrementedTrials) { this.incrementedTrials = incrementedTrials; }

    public Integer getIncrementedReminders() {
        return incrementedReminders != null ? incrementedReminders : getReminders();
    }

    public boolean isStoppedDueFreezeContract() {return stoppedDueFreezeContract;}

    public void setStoppedDueFreezeContract(boolean stoppedDueFreezeContract) {this.stoppedDueFreezeContract = stoppedDueFreezeContract;}

    public void setIncrementedReminders(Integer incrementedReminders) {
        this.incrementedReminders = incrementedReminders;
    }

    public Date getLastMessageDate() { return lastMessageDate; }

    public void setLastMessageDate(Date lastMessageDate) { this.lastMessageDate = lastMessageDate; }

    @JsonIgnore
    public Map<String, Object> getAdditionalInfo() {

        if (additionalInfo != null) {
            try {
                return Setup.getApplicationContext().getBean(ObjectMapper.class)
                        .readValue(additionalInfo, new TypeReference<Map<String, Object>>() {
                        });
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return new HashMap<>();
    }

    public void setAdditionalInfo(Map<String, Object> additionalInfo) {
        try {
            this.additionalInfo =
                    Setup.getApplicationContext().getBean(ObjectMapper.class)
                            .writeValueAsString(additionalInfo);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @JsonIgnore
    public Object getAdditionalValue(String key) {
        return getAdditionalInfo().get(key);
    }

    public boolean isIgnorePaidEndDate() { return ignorePaidEndDate; }

    public void setIgnorePaidEndDate(boolean ignorePaidEndDate) { this.ignorePaidEndDate = ignorePaidEndDate; }

    public void setAdditionalValue(String key, Object value) {

        Map<String, Object> additionalInfo = getAdditionalInfo();
        additionalInfo.put(key, value);
        setAdditionalInfo(additionalInfo);
    }

    @JsonIgnore
    public List<DirectDebit> getRelatedDirectDebits() {

        List<Long> relayedDDsIds = new ArrayList<>();
        // ACC-9612
        int ddNum = 0;
        while(getAdditionalInfo().containsKey("dda_" + ddNum)) {
            Long ddId = Utils.parseValue(this.getAdditionalValue("dda_" + ddNum), Long.class);
            if (ddId == null) {
                break;
            }
            relayedDDsIds.add(ddId);
            ddNum++;
        }

        Long ddbId = Utils.parseValue(this.getAdditionalValue("ddbId"), Long.class);
        if (ddbId != null) relayedDDsIds.add(ddbId);

        if (relayedDDsIds.isEmpty()) return new ArrayList<>();

        return Setup.getRepository(DirectDebitRepository.class).findAll(relayedDDsIds);
    }

    @Override
    public void setCompleted(Boolean completed) {

        switch (this.getFlowEventConfig().getName()) {
            case CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED:
                BaseAdditionalInfo b = getContract().getBaseAdditionalInfo(Contract.ADDITIONAL_INFO_IS_IPAM_RUNNING);
                if (b != null) {
                    b.setInfoValue("false");
                    Setup.getRepository(BaseAdditionalInfoRepository.class).saveAndFlush(b);
                }
                break;
            case EXTENSION_FLOW:
                Setup.getApplicationContext().getBean(ExtensionFlowService.class).handleStopExtensionFlow(this);
                break;
        }

        super.setCompleted(completed);
    }
}