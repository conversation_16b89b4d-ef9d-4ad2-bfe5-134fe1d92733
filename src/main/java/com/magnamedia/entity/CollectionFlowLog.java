package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.CollectionFlowStatus;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created At 3/5/2022
 **/

@Entity
public class CollectionFlowLog extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem flowType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Column
    private String triggerUser = "ERP";

    @Column
    private Long relatedToId;

    @Column
    private String relatedToEntity;

    @Column
    private Date triggerDate;

    @Column
    private Date idleSinceDate;

    @Column
    @Enumerated(EnumType.STRING)
    private CollectionFlowStatus status;

    @Column
    private String notes;

    @Column(columnDefinition = "boolean default false")
    private Boolean ended = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean rejectionTodoIsRescheduledWhenEnded = false;
    @Column
    private Long currentRunningFlowId;

    @Column
    private String currentRunningFlowEntity;

    public CollectionFlowLog() {
        super();
    }

    public CollectionFlowLog(
            PicklistItem flowType, Contract contract, Long relatedToId, String relatedToEntity,
            Date triggerDate, Date idleSinceDate, CollectionFlowStatus status, String notes,
            Long currentRunningFlowId, String currentRunningFlowEntity) {
        super();
        this.flowType = flowType;
        this.contract = contract;
        this.relatedToId = relatedToId;
        this.relatedToEntity = relatedToEntity;
        this.triggerDate = triggerDate;
        this.idleSinceDate = idleSinceDate;
        this.status = status;
        this.notes = notes;
        this.currentRunningFlowId = currentRunningFlowId;
        this.currentRunningFlowEntity = currentRunningFlowEntity;
    }

    public PicklistItem getFlowType() {
        return flowType;
    }

    public void setFlowType(PicklistItem flowType) {
        this.flowType = flowType;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Long getRelatedToId() {
        return relatedToId;
    }

    public void setRelatedToId(Long relatedToId) {
        this.relatedToId = relatedToId;
    }

    public String getRelatedToEntity() {
        return relatedToEntity;
    }

    public void setRelatedToEntity(String relatedToEntity) {
        this.relatedToEntity = relatedToEntity;
    }

    public Date getTriggerDate() {
        return triggerDate;
    }

    public void setTriggerDate(Date triggerDate) {
        this.triggerDate = triggerDate;
    }

    public Date getIdleSinceDate() {
        return idleSinceDate;
    }

    public void setIdleSinceDate(Date idleSinceDate) {
        this.idleSinceDate = idleSinceDate;
    }

    public CollectionFlowStatus getStatus() {
        return status;
    }

    public void setStatus(CollectionFlowStatus status) {
        this.status = status;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Boolean getEnded() {
        return ended;
    }

    public void setEnded(Boolean ended) {
        this.ended = ended;
    }

    public Boolean getRejectionTodoIsRescheduledWhenEnded() {
        return rejectionTodoIsRescheduledWhenEnded != null && rejectionTodoIsRescheduledWhenEnded;
    }

    public void setRejectionTodoIsRescheduledWhenEnded(Boolean rejectionTodoIsRescheduledWhenEnded) {
        this.rejectionTodoIsRescheduledWhenEnded = rejectionTodoIsRescheduledWhenEnded;
    }

    public String getTriggerUser() { return triggerUser; }

    public void setTriggerUser(String triggerUser) { this.triggerUser = triggerUser; }

    public Long getCurrentRunningFlowId() { return currentRunningFlowId; }

    public void setCurrentRunningFlowId(Long currentRunningFlowId) { this.currentRunningFlowId = currentRunningFlowId; }

    public String getCurrentRunningFlowEntity() { return currentRunningFlowEntity; }

    public void setCurrentRunningFlowEntity(String currentRunningFlowEntity) {this.currentRunningFlowEntity = currentRunningFlowEntity;}
}
