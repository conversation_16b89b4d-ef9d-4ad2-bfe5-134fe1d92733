package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.interfaces.BasePLVariableNode;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@MappedSuperclass
public class BasePLVariableBucket extends BaseEntity {

    @Column
    protected Double wieght;

    public Double getWieght() {
        return wieght;
    }

    public void setWieght(Double wieght) {
        this.wieght = wieght;
    }

    public Revenue getRevenue() {
        return null;
    }

    public void setRevenue(Revenue revenue) {

    }

    public Expense getExpense() {
        return null;
    }

    public void setExpense(Expense expense) {

    }

    @JsonIgnore
    public BasePLVariableNode getpLVariable() {
        return null;
    }

    public void setpLVariable(BasePLVariableNode pLVariable) {
    }

    @BeforeInsert
    @BeforeUpdate
    public void validate() {
//            //Jirra ACC-280
//                PLVariableNodeRepository pLVariableRepository =
//                        Setup.getApplicationContext().getBean(PLVariableNodeRepository.class);

        if (this.getpLVariable() == null)
            throw new RuntimeException("Variable should not be empty.");

        if (this.getWieght() == null)
            throw new RuntimeException("Bucket weight should not be empty.");

        if (this.getWieght() < -1 || this.getWieght() > +1)
            throw new RuntimeException("Bucket weight should be between -1 and +1.");

        //Jirra ACC-280
//		PLVariableNode pLVariable1 = pLVariableRepository.findOne(this.getpLVariable().getId());
//		if (pLVariable1.getParent().getpLNodeType().equals(PLNodeType.EXPENSES)) {
//			if (this.getExpense() == null)
//				throw new RuntimeException(
//						"you should assign an expense bucket to this variable, The Main level Type is EXPENSES.");
//			if (this.getRevenue() != null)
//				throw new RuntimeException(
//						"you can not assign a revenue bucket to this variable, The Main level Type is EXPENSES.");
//		}
//
//		if (pLVariable1.getParent().getpLNodeType().equals(PLNodeType.REVENUES)) {
//			if (this.getRevenue() == null)
//				throw new RuntimeException(
//						"you should assign a revenue bucket to this variable, The Main level Type is REVENUES.");
//			if (this.getExpense() != null)
//				throw new RuntimeException(
//						"you can not assign an expense bucket to this variable, The Main level Type is REVENUES.");
//		}
    }
}
