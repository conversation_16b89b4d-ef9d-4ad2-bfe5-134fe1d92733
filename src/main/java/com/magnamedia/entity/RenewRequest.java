package com.magnamedia.entity;

import com.magnamedia.core.workflow.FormField;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.workflow.visa.EidMedicalExpensesService;
import com.magnamedia.workflow.visa.LaborCardRenewExpensesService;
import com.magnamedia.workflow.visa.RenewExpensesService;
import com.magnamedia.workflow.visa.ResidenceRenewExpensesService;
import java.io.Serializable;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Oct 9, 2017
 */
@Entity
@Table(
        indexes = {
            @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class RenewRequest extends VisaRequest<RenewRequest, RenewRequestNote, RenewRequestExpense> implements Serializable {

    public RenewRequest() {
        super("Collect Documents");
    }
    
    @Override
    public String getFinishedTaskName() {
        return "Visa processing complete";
    }
    
        @Column
	private java.sql.Date renewalDate;

	@Enumerated
	@Column
	private PaymentType renewalPaymentType;

	@Column
	private Double renewalExpenses;

	@Enumerated
	@Column
	private PaymentType eidPaymentType;

	@Column
	private Double eidExpenses;

	@Enumerated
	@Column
	private PaymentType medicalPaymentType;

	@Column
	private Double medicalExpenses;

	@Column
	private java.sql.Date renewedSignedContractDate;

	@Column
	private java.sql.Date laborCardSubmissionDate;

	@Column
	private java.sql.Date laborCardExpiryDate;

	@Enumerated
	@Column
	private PaymentType laborCardRenewPaymentType;

	@Column
	private Double laborCardRenewExpenses;

	@Column
	private java.sql.Date residenceRenewDate;

	@Enumerated
	@Column
	private PaymentType residenceRenewPaymentType;

	@Column
	private Double residenceRenewExpenses;
        
        @Column
        private Boolean fitToWorkInUAE;
        
	@Override
	public boolean equals(Object object) {
		if (!(object instanceof RenewRequest)) {
			return false;
		}
		return super.equals(object);
	}

	public java.sql.Date getRenewalDate() {
		return renewalDate;
	}

	public void setRenewalDate(java.sql.Date renewalDate) {
		this.renewalDate = renewalDate;
	}
        
	public PaymentType getRenewalPaymentType() {
		return renewalPaymentType;
	}

	public void setRenewalPaymentType(PaymentType renewalPaymentType) {
		this.renewalPaymentType = renewalPaymentType;
	}

	public Double getRenewalExpenses() {
		if (renewalExpenses == null) {
			renewalExpenses = RenewExpensesService.DEFAULT_EXPENSES;
		}
		return renewalExpenses;
	}

	public void setRenewalExpenses(Double renewalExpenses) {
		this.renewalExpenses = renewalExpenses;
	}
        
	public PaymentType getEidPaymentType() {
		return eidPaymentType;
	}

	public void setEidPaymentType(PaymentType eidPaymentType) {
		this.eidPaymentType = eidPaymentType;
	}

	public Double getEidExpenses() {
		if (eidExpenses == null) {
			eidExpenses = EidMedicalExpensesService.EID_DEFAULT_EXPENSES;
		}
		return eidExpenses;
	}

	public void setEidExpenses(Double eidExpenses) {
		this.eidExpenses = eidExpenses;
	}
        
	public PaymentType getMedicalPaymentType() {
		return medicalPaymentType;
	}

	public void setMedicalPaymentType(PaymentType medicalPaymentType) {
		this.medicalPaymentType = medicalPaymentType;
	}

	public Double getMedicalExpenses() {
		if (medicalExpenses == null) {
			medicalExpenses = EidMedicalExpensesService.MEDICAL_DEFAULT_EXPENSES;
		}
		return medicalExpenses;
	}

	public void setMedicalExpenses(Double medicalExpenses) {
		this.medicalExpenses = medicalExpenses;
	}

	public java.sql.Date getRenewedSignedContractDate() {
		return renewedSignedContractDate;
	}

	public void setRenewedSignedContractDate(java.sql.Date renewedSignedContractDate) {
		this.renewedSignedContractDate = renewedSignedContractDate;
	}
	public java.sql.Date getLaborCardSubmissionDate() {
		return laborCardSubmissionDate;
	}

	public void setLaborCardSubmissionDate(java.sql.Date laborCardSubmissionDate) {
		this.laborCardSubmissionDate = laborCardSubmissionDate;
	}

	public java.sql.Date getLaborCardExpiryDate() {
		return laborCardExpiryDate;
	}

	public void setLaborCardExpiryDate(java.sql.Date laborCardExpiryDate) {
		this.laborCardExpiryDate = laborCardExpiryDate;
	}
	public PaymentType getLaborCardRenewPaymentType() {
		return laborCardRenewPaymentType;
	}

	public void setLaborCardRenewPaymentType(PaymentType laborCardRenewPaymentType) {
		this.laborCardRenewPaymentType = laborCardRenewPaymentType;
	}

	public Double getLaborCardRenewExpenses() {
		if (laborCardRenewExpenses == null) {
			laborCardRenewExpenses = LaborCardRenewExpensesService.DEFAULT_EXPENSES;
		}
		return laborCardRenewExpenses;
	}

	public void setLaborCardRenewExpenses(Double laborCardRenewExpenses) {
		this.laborCardRenewExpenses = laborCardRenewExpenses;
	}
        
	public java.sql.Date getResidenceRenewDate() {
		return residenceRenewDate;
	}

	public void setResidenceRenewDate(java.sql.Date residenceRenewDate) {
		this.residenceRenewDate = residenceRenewDate;
	}
        
	public PaymentType getResidenceRenewPaymentType() {
		return residenceRenewPaymentType;
	}

	public void setResidenceRenewPaymentType(PaymentType residenceRenewPaymentType) {
		this.residenceRenewPaymentType = residenceRenewPaymentType;
	}

	public Double getResidenceRenewExpenses() {
		if (residenceRenewExpenses == null) {
			residenceRenewExpenses = ResidenceRenewExpensesService.DEFAULT_EXPENSES;
		}
		return residenceRenewExpenses;
	}

	public void setResidenceRenewExpenses(Double residenceRenewExpenses) {
		this.residenceRenewExpenses = residenceRenewExpenses;
	}

    public Boolean getFitToWorkInUAE() {
        return fitToWorkInUAE;
    }

    public void setFitToWorkInUAE(Boolean fitToWorkInUAE) {
        this.fitToWorkInUAE = fitToWorkInUAE;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }

}
