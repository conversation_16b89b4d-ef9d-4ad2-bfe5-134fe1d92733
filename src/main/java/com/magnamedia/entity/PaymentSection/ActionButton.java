package com.magnamedia.entity.PaymentSection;

import java.util.Map;

public class ActionButton {
    public enum ButtonType {
        PRIMARY,
        SECONDARY
    }

    public ActionButton(String text, String route, Map<String, Object> arguments, boolean enabled, ButtonType buttonType) {
        this.text = text;
        this.route = route;
        Arguments = arguments;
        this.enabled = enabled;
        this.buttonType = buttonType;
    }

    public ActionButton(String text, String hyperlink, boolean enabled) {
        this.text = text;
        this.hyperlink = hyperlink;
        this.enabled = enabled;
    }

    private String text;

    private String route;

    private String hyperlink;

    private Map<String, Object> Arguments;

    private boolean enabled;

    private ButtonType buttonType = ButtonType.PRIMARY;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getRoute() {
        return route;
    }

    public void setRoute(String route) {
        this.route = route;
    }

    public String getHyperlink() { return hyperlink; }

    public void setHyperlink(String hyperlink) { this.hyperlink = hyperlink; }

    public Map<String, Object> getArguments() {
        return Arguments;
    }

    public void setArguments(Map<String, Object> arguments) {
        Arguments = arguments;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public ButtonType getButtonType() { return buttonType; }

    public void setButtonType(ButtonType buttonType) { this.buttonType = buttonType; }
}
