package com.magnamedia.entity.workflow;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Bucket;
import com.magnamedia.module.type.BucketReplenishmentTodoStatus;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Entity
public class BucketReplenishmentTodo extends WorkflowEntity {

    public BucketReplenishmentTodo() {
        super("");
    }

    public BucketReplenishmentTodo(String string) {
        super(string);
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Override
    public List<FormField> getForm(String s) {
        return null;
    }

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Bucket bucket;

    @ManyToOne(fetch = FetchType.EAGER)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Bucket fillFrom;

    @Column
    private Double amount;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private BucketReplenishmentTodoStatus status = BucketReplenishmentTodoStatus.PENDING;

    @Column
    private Date requestDate;

    @Column
    private Boolean autoRequested;

    @Column
    private Boolean confirmed = false;

    @Column
    private Boolean auditManagerApproved = false;

    @OneToMany(mappedBy = "replenishmentTodo")
//    @JsonSerialize(using = ExpenseRequestForPaymentSerializer.class)
    @JsonIgnore
    private List<ExpensePayment> expensePayments = new ArrayList();

    @Column
    private Boolean transGuardDone = false;

    @Column(columnDefinition = "boolean default false")
    private boolean transactionAdded = false;

    @Column(columnDefinition = "boolean default false")
    public boolean transGuardService = false;

    @Transient
    private String todoName;
    @Transient
    private Boolean showDone;

    //JIRA ACC-4505
    @Column
    private Double bucketBalance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User cashier;

    public Double getBucketBalance() {
        return bucketBalance;
    }

    public void setBucketBalance(Double bucketBalance) {
        this.bucketBalance = bucketBalance;
    }

    @BeforeInsert
    public void beforeInsertTrigger(){
        setBucketBalance(getBucket().getBalance());
    }

    public String getTodoName() {
        return "Replenish " + (bucket != null ? bucket.getName() : " ") + " from " + (fillFrom != null ? fillFrom.getName() : " ") + " with amount " + (amount != null ? amount.toString() : "0.0") + (bucket != null && isTransGuardService() ? " using TransGuardService" : " ");
    }

    public Boolean getShowDone() {
        return getAuditManagerApproved() && !getCompleted() && isTransGuardService() && !getTransGuardDone();
    }

    public void setTodoName(String todoName) {
        this.todoName = todoName;
    }

    public void setShowDone(Boolean showDone) {
        this.showDone = showDone;
    }

    public Boolean getTransGuardDone() {
        if (transGuardDone == null)
            return false;
        return transGuardDone;
    }

    public void setTransGuardDone(Boolean transGuardDone) {
        this.transGuardDone = transGuardDone;
    }

    public boolean isTransactionAdded() {
        return transactionAdded;
    }

    public void setTransactionAdded(boolean transactionAdded) {
        this.transactionAdded = transactionAdded;
    }

    public boolean isTransGuardService() {
        return transGuardService;
    }

    public void setTransGuardService(boolean transGuardService) {
        this.transGuardService = transGuardService;
    }

    public List<ExpensePayment> getExpensePayments() {
        return expensePayments;
    }

    public void setExpensePayments(List<ExpensePayment> expensePayments) {
        this.expensePayments = expensePayments;
    }

    @JsonIgnore
    public Boolean isAuditManagerApproved() {
        return auditManagerApproved;
    }

    public Boolean getAuditManagerApproved() {
        if (auditManagerApproved == null)
            return false;
        return auditManagerApproved;
    }

    public void setAuditManagerApproved(Boolean auditManagerApproved) {
        this.auditManagerApproved = auditManagerApproved;
    }

    public Bucket getBucket() {
        return bucket;
    }

    public void setBucket(Bucket bucket) {
        this.bucket = bucket;
    }

    public Bucket getFillFrom() {
        return fillFrom;
    }

    public void setFillFrom(Bucket fillFrom) {
        this.fillFrom = fillFrom;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public BucketReplenishmentTodoStatus getStatus() {
        return status;
    }

    public void setStatus(BucketReplenishmentTodoStatus status) {
        this.status = status;
    }

    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    public Boolean getAutoRequested() {
        return autoRequested;
    }

    public void setAutoRequested(Boolean autoRequested) {
        this.autoRequested = autoRequested;
    }

    @JsonIgnore
    public Boolean isConfirmed() {
        return confirmed;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public User getCashier() {
        return cashier;
    }

    public void setCashier(User cashier) {
        this.cashier = cashier;
    }
}
