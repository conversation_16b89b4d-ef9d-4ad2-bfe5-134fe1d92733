package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Complaint;
import com.magnamedia.entity.Contract;
import com.magnamedia.extra.ClientOnVoiceResolverToDoSerializer;
import com.magnamedia.module.type.VoiceResolverToDoReason;
import com.magnamedia.module.type.VoiceResolverToDoType;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
public class VoiceResolverToDo extends WorkflowEntity {

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;

    @Enumerated(EnumType.STRING)
    private VoiceResolverToDoReason reason;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne
    private Complaint complaint;

    @JsonSerialize(using = ClientOnVoiceResolverToDoSerializer.class)
    @ManyToOne
    private Client client;

    @ManyToOne(fetch = FetchType.LAZY)
    private User assignee;

    public Complaint getComplaint() { return complaint; }

    public void setComplaint(Complaint complaint) { this.complaint = complaint; }

    public VoiceResolverToDo() {
        super(VoiceResolverToDoType.TALK_TO_CLIENT.getLabel());
    }

    public VoiceResolverToDo(String startTaskName) {
        super(startTaskName);
    }

    public VoiceResolverToDoReason getReason() {
        return reason;
    }

    public void setReason(VoiceResolverToDoReason reason) {
        this.reason = reason;
    }

    @Override
    public String getFinishedTaskName() {
        return "Voice Resolver To Do Completed";
    }

    @Override
    public List<FormField> getForm(String string) {
        return new ArrayList<>();
    }

    public Contract getContract() {return contract;}

    public void setContract(Contract contract) {this.contract = contract;}

    public Client getClient() {return client;}

    public void setClient(Client client) {this.client = client;}

    public User getAssignee() {return assignee;}

    public void setAssignee(User assignee) {this.assignee = assignee;}
}
