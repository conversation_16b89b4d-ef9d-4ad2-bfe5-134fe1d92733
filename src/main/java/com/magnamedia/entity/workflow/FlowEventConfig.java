package com.magnamedia.entity.workflow;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.MagnaIndex;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Tag;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.repository.FlowSubEventConfigRepository;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> Hachem
 */

@Entity
public class FlowEventConfig extends BaseEntity {
    
    @Enumerated(EnumType.STRING)
    private FlowEventName name;
    
    private int maxFlowRuns = 1;
    private boolean stopMessagesOnContractCancellation = true;
    private boolean stopTodosOnContractCancellation = true;
    private boolean closeToDosUponCompletion = true;

    @Column(columnDefinition = "boolean default true")
    private boolean stopFlowsCreatedAfterTermination = true;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem cancellationReason;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "FLOWEVENTCONFIGTAGS",
            joinColumns = {@JoinColumn(
                    name = "ITEM",
                    referencedColumnName = "ID",
                    foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT)
            )},
            inverseJoinColumns = {@JoinColumn(
                    name = "TAG",
                    referencedColumnName = "ID",
                    foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT)
            )}
    )
    @MagnaIndex
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    private List<Tag> tags;

    public FlowEventName getName() {
        return name;
    }

    public void setName(FlowEventName name) {
        this.name = name;
    }

    public int getMaxFlowRuns() {
        return maxFlowRuns;
    }

    public void setMaxFlowRuns(int maxFlowRuns) {
        this.maxFlowRuns = maxFlowRuns;
    }

    public boolean isStopMessagesOnContractCancellation() {
        return stopMessagesOnContractCancellation;
    }

    public void setStopMessagesOnContractCancellation(boolean stopMessagesOnContractCancellation) {
        this.stopMessagesOnContractCancellation = stopMessagesOnContractCancellation;
    }

    public boolean isStopTodosOnContractCancellation() {
        return stopTodosOnContractCancellation;
    }

    public void setStopTodosOnContractCancellation(boolean stopTodosOnContractCancellation) {
        this.stopTodosOnContractCancellation = stopTodosOnContractCancellation;
    }

    public boolean isCloseToDosUponCompletion() { return closeToDosUponCompletion; }

    public void setCloseToDosUponCompletion(boolean closeToDosUponCompletion) {
        this.closeToDosUponCompletion = closeToDosUponCompletion;
    }

    public PicklistItem getCancellationReason() {
        return cancellationReason;
    }

    public void setCancellationReason(PicklistItem cancellationReason) {
        this.cancellationReason = cancellationReason;
    }
    
    @Basic(fetch = FetchType.LAZY)
    public List<FlowSubEventConfig> getSubEventConfigs() {
        return Setup.getRepository(FlowSubEventConfigRepository.class).findByFlowEventConfig(this);
    }

    public List<Tag> getTags() {
        return (List)(this.tags == null ? new ArrayList() : this.tags);
    }

    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }

    public Tag getTagValue(String key) {
        return this.getTags().stream().filter(t -> t.getName()
                .contains(key)).findFirst().orElse(null);
    }

    public boolean hasTag(String key) {
        return this.getTags().stream().anyMatch((tag) ->
            tag.getKey().contains(key)
        );
    }

    public boolean isStopFlowsCreatedAfterTermination() { return stopFlowsCreatedAfterTermination; }

    public void setStopFlowsCreatedAfterTermination(boolean stopFlowsCreatedAfterTermination) {
        this.stopFlowsCreatedAfterTermination = stopFlowsCreatedAfterTermination;
    }

    public enum FlowEventName {
        CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED(DDMessagingType.ClientPaidCashAndNoSignatureProvided),
        ONLINE_CREDIT_CARD_PAYMENT_REMINDERS(DDMessagingType.OnlineCreditCardPaymentReminders),
        CLIENTS_PAYING_VIA_Credit_Card(DDMessagingType.ClientsPayingViaCreditCard),
        ONE_MONTH_AGREEMENT(DDMessagingType.OneMonthAgreement), // deprecated ACC-6647
        INCOMPLETE_FLOW_MISSING_BANK_INFO(DDMessagingType.IncompleteDDClientHasNoApprovedSignature),
        PAYMENT_EXPIRY_FLOW(DDMessagingType.ExpiryPayment),
        EXTENSION_FLOW(DDMessagingType.ExtensionFlow);

        private final DDMessagingType messagingType;

        FlowEventName(DDMessagingType messagingType) {
            this.messagingType = messagingType;
        }

        public DDMessagingType getMessagingType() {
            return messagingType;
        }
    }
}
