package com.magnamedia.entity.maidArrival;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.module.type.MaidArrivalManager;
import com.magnamedia.module.type.MaidArrivalToDoType;
import org.joda.time.DateTime;
import org.joda.time.Days;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on May 14, 2019
 * ACC-688
 */

@Entity
@Table(
        indexes = {
                @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class MaidArrivalToDo extends WorkflowEntity {

    public MaidArrivalToDo() {
        super(MaidArrivalToDoType.CHANGE_STATUS_TO_LANDED_IN_DUBAI.toString());
    }

    public MaidArrivalToDo(String startTaskName) {
        super(startTaskName);
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(foreignKey = @ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
    @JsonSerialize(using = IdLabelSerializer.class)
    private Housemaid housemaid;

    @Column
    private Double cashAdvanceAmount;

    @Column
    private Double smartphoneLoanAmount;

    //Jirra ACC-977
    @Column
    private Boolean cashAdvanceAdded;


    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }


    public Double getCashAdvanceAmount() {
        return cashAdvanceAmount;
    }

    public void setCashAdvanceAmount(Double cashAdvanceAmount) {
        this.cashAdvanceAmount = cashAdvanceAmount;
    }

    public Double getSmartphoneLoanAmount() {
        return smartphoneLoanAmount;
    }

    public void setSmartphoneLoanAmount(Double smartphoneLoanAmount) {
        this.smartphoneLoanAmount = smartphoneLoanAmount;
    }

    public Boolean getCashAdvanceAdded() {
        return cashAdvanceAdded;
    }

    public void setCashAdvanceAdded(Boolean cashAdvanceAdded) {
        this.cashAdvanceAdded = cashAdvanceAdded;
    }

    @Override
    public String getFinishedTaskName() {
        return MaidArrivalToDoType.PRINT_MOHRE_CERTIFICATE.toString();
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }
}

