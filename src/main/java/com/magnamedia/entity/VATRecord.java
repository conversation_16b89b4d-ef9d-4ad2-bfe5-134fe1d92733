package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.validation.constraints.Min;

/**
 *
 * <AUTHOR>
 */
@Entity
public class VATRecord extends BaseEntity {

    @Column(nullable = false)
    @Min(0)
    private Long totalSmsCount = (long) 0;

    @Column(nullable = false)
    private Boolean isApprovedByClient = false;


    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;


    public Long getTotalSmsCount() {
        if (totalSmsCount == null) {
            totalSmsCount = 0l;
        }
        return totalSmsCount;
    }

    public void setTotalSmsCount(Long totalSmsCount) {
        this.totalSmsCount = totalSmsCount;
    }

    public Boolean getIsApprovedByClient() {
        return isApprovedByClient;
    }

    public void setIsApprovedByClient(Boolean isApprovedByClient) {
        this.isApprovedByClient = isApprovedByClient;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }
}