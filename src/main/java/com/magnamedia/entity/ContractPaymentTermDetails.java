package com.magnamedia.entity;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.sql.Date;

/**
 * <AUTHOR>
 *         ACC-6150
 */

@Entity
@Where(clause = "DELETED <> true")
public class ContractPaymentTermDetails extends BaseEntity {

    public enum DetailType {
        DISCOUNT
    }

    public enum Source {
        CREDIT_NOTE
    }

    public enum Level {
        CONTRACT_PAYMENT_TERM,
        CONTRACT
    }

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ContractPaymentTerm contractPaymentTerm;

    @Column
    private double amount = 0.0;

    @Column
    private Date startDate;

    @Column
    private Date endDate;

    @Column
    private int discountMonths = 0;

    @Column
    @Enumerated(EnumType.STRING)
    private DetailType detailType;

    @Column
    @Enumerated(EnumType.STRING)
    private Source source;

    @Column
    @Enumerated(EnumType.STRING)
    private Level detailLevel = Level.CONTRACT_PAYMENT_TERM;

    @Column(columnDefinition = "boolean default false")
    private Boolean deleted = false;

    @Transient
    private boolean finished = false;

    @Transient
    private boolean proRatedPassed = false;

    public ContractPaymentTerm getContractPaymentTerm() { return contractPaymentTerm; }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }

    public double getAmount() { return amount; }

    public void setAmount(double amount) { this.amount = amount; }

    public Date getStartDate() { return startDate; }

    public void setStartDate(Date startDate) { this.startDate = startDate; }

    public Date getEndDate() { return endDate; }

    public void setEndDate(Date endDate) { this.endDate = endDate; }

    public int getDiscountMonths() { return discountMonths; }

    public void setDiscountMonths(int discountMonths) { this.discountMonths = discountMonths; }

    public DetailType getDetailType() { return detailType; }

    public void setDetailType(DetailType detailType) { this.detailType = detailType; }

    public Source getSource() { return source; }

    public void setSource(Source source) { this.source = source; }

    public Level getDetailLevel() { return detailLevel; }

    public void setDetailLevel(Level detailLevel) { this.detailLevel = detailLevel; }

    public boolean isDeleted() { return deleted; }

    public void setDeleted(boolean deleted) { this.deleted = deleted; }

    public boolean isFinished() { return finished; }

    public void setFinished(boolean finished) { this.finished = finished; }

    public boolean isProRatedPassed() { return proRatedPassed; }

    public void setProRatedPassed(boolean proRatedPassed) { this.proRatedPassed = proRatedPassed; }
}