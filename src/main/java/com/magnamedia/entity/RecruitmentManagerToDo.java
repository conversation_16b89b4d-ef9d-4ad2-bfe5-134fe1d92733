package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import org.hibernate.envers.NotAudited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 28, 2020
 * Jirra ACC-1651
 */

@Entity
public class RecruitmentManagerToDo extends BaseEntity {

    @ManyToOne
    private Housemaid housemaid;
    
    @Column
    @NotAudited
    private Boolean giveLoan;

    
    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public Boolean getGiveLoan() {
        return giveLoan;
    }

    public void setGiveLoan(Boolean giveLoan) {
        this.giveLoan = giveLoan;
    }


    
}