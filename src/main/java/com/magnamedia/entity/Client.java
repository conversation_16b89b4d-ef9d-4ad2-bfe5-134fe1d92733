package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.mastersearch.SearchableField;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.ClientDocumentRepository;
import com.magnamedia.repository.ContractPaymentTermRepository;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;
import org.hibernate.validator.constraints.Email;

import javax.persistence.*;
import javax.validation.constraints.Pattern;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static com.magnamedia.helper.PicklistHelper.getItem;

/**
 * <AUTHOR>
 * 
 */
@Entity
@Searchable(showunName = "Clients", order = 0, permissionCode = "ClientList")
public class Client extends BaseEntity {

    @Column
    @Label
    @SearchableField(headerName = "Name")
    private String name;

    @Column
    private String fullAddress;

    @Column
    private String dewaNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem status;

    @Column
    private Integer kidsUnder12Years;

    @Column
    private String mobileNumber;

    @NotAudited
    @Formula(
            "(SELECT ( NOT  U.UNBLOCK ) FROM   BLOCKLOGS U WHERE  U.CLIENT_ID = ID AND U.ID = (SELECT MAX(I.ID) FROM   BLOCKLOGS I WHERE  I.CLIENT_ID = ID ORDER BY I.CREATION_DATE)) ")
    private Boolean blocked;

    //Jirra ACC-820
    @Email
    private String email;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    @Deprecated
    private PicklistItem preferredContactMethod;

    //Jirra ACC-837
    @Column
    @Pattern(regexp = "[7][8][4][-][0-9]{4}[-][0-9]{7}[-][0-9]")
    private String eid;

    @Column
    private String clientIBAN;

    @Column
    private String accountName;

    @Transient
    private String transientEID;
    @Transient
    private String transientIBAN;
    @Transient
    private String transientAccountName;

    //Jirra ACC-1092
    @Column
    private String normalizedMobileNumber;

    @Column
    private String normalizedSpouseMobileNumber;

    @Transient
    private Attachment forntEid;

    @Transient
    private boolean showWorker;

    //Jirra ACC-1241
    @JsonIgnore
    @Column(unique = true)
    private String discountCode;

    @Column(columnDefinition = "boolean default false")
    private Boolean fired = false;

    @Column
    private Boolean gcc = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem area;

    public PicklistItem getArea() {
        return area;
    }

    public void setArea(PicklistItem area) {
        this.area = area;
    }

    @Column(columnDefinition = "boolean default false")
    private Boolean areaFilledByAreaListFile;

    @Column
    private String birthDate;

    @Column(columnDefinition = "boolean default false")
    private Boolean vVip = false;

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public void setGcc(Boolean gcc) {
        this.gcc = gcc;
    }

    public Boolean getGcc() {
        return gcc;
    }
    
    public String getDewaNumber() {
        return dewaNumber;
    }

    public void setDewaNumber(String dewaNumber) {
        this.dewaNumber = dewaNumber;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullAddress() {
        return fullAddress;
    }

    public void setFullAddress(String fullAddress) {
        this.fullAddress = fullAddress;
    }

    public String getName() {
        return name;
    }

    public PicklistItem getStatus() {
        return status;
    }

    public void setStatus(PicklistItem status) {
        this.status = status;
    }

    public Integer getKidsUnder12Years() {
        return kidsUnder12Years;
    }

    public void setKidsUnder12Years(Integer kidsUnder12Years) {
        this.kidsUnder12Years = kidsUnder12Years;
    }

    @JsonIgnore
    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public Boolean isBlocked() {
        return blocked != null && blocked;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getBlocked() {
        return blocked != null && blocked;
    }

    public void setBlocked(Boolean blocked) {
        this.blocked = blocked;
    }

    public PicklistItem getPreferredContactMethod() {
        return preferredContactMethod;
    }

    public void setPreferredContactMethod(PicklistItem preferredContactMethod) {
        this.preferredContactMethod = preferredContactMethod;
    }

    public String getEid() {
        initBankInfo();

        if (transientEID != null && !transientEID.isEmpty()) return transientEID;

        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getClientIBAN() {
        initBankInfo();

        if (transientIBAN != null && !transientIBAN.isEmpty()) return transientIBAN;

        return clientIBAN;
    }

    public void setClientIBAN(String clientIBAN) {
        this.clientIBAN = clientIBAN;
    }

    //Jirra ACC-1092
    @JsonIgnore
    public String getNormalizedMobileNumber() {
        return normalizedMobileNumber;
    }

    public void setNormalizedMobileNumber(String normalizedMobileNumber) {
        this.normalizedMobileNumber = normalizedMobileNumber;
    }

    public boolean isShowWorker() {
        return showWorker;
    }

    public void setShowWorker(boolean showWorker) {
        this.showWorker = showWorker;
    }

    @JsonIgnore
    public String getNormalizedSpouseMobileNumber() {
        return normalizedSpouseMobileNumber;
    }

    public void setNormalizedSpouseMobileNumber(String normalizedSpouseMobileNumber) {
        this.normalizedSpouseMobileNumber = normalizedSpouseMobileNumber;
    }

    public String getAccountName() {
        initBankInfo();

        if (transientAccountName != null && !transientAccountName.isEmpty()) return transientAccountName;

        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Attachment getForntEid() {
        ClientDocumentRepository documentRepository =
                Setup.getRepository(ClientDocumentRepository.class);
        List<ClientDocument> docs =
                documentRepository.findByClientAndType(this, Setup.getItem("ClientDocumentType", "EMIRATES_ID_FRONT_SIDE"));
        if (docs != null && !docs.isEmpty())
            return docs.get(0).getAttachment("EMIRATES_ID_FRONT_SIDE");
        return null;
    }

    public boolean getShowWorker() {

        ContractRepository contractRepository =
                Setup.getRepository(ContractRepository.class);
        Contract c =
                contractRepository
                        .findFirstOneByClientAndStatusInOrderByCreationDateDesc(
                                this, new ContractStatus[]{ContractStatus.ACTIVE});
        return c != null
                && c.getContractProspectType() != null
                && c.getContractProspectType().getCode().equalsIgnoreCase(
                PicklistItem.getCode(AccountingModule.MAID_VISA_PEOSPECT_TYPE));
    }

    public String getDiscountCode() {
        return discountCode;
    }

    public void setDiscountCode(String discountCode) {
        this.discountCode = discountCode;
    }

    private void initBankInfo() {
        if (!StringUtils.isEmpty(transientAccountName) && !StringUtils.isEmpty(transientEID) && !StringUtils.isEmpty(transientIBAN))
            return;

        ContractRepository contractRepository =
                Setup.getRepository(ContractRepository.class);
        Contract contract =
                contractRepository
                        .findFirstOneByClientAndStatusInOrderByCreationDateDesc(
                                this, new ContractStatus[]{ContractStatus.ACTIVE});
        setContractPaymentTermInfo(contract);
    }

    public void setContractPaymentTermInfo(Contract c) {

        ContractPaymentTermRepository contractPaymentTermRepository =
                Setup.getRepository(ContractPaymentTermRepository.class);
        if (c != null) {
            List<ContractPaymentTerm> cpts =
                    contractPaymentTermRepository.findByContractAndIsActive(c, true);
            if (cpts != null && !cpts.isEmpty()) {
                this.transientAccountName = cpts.get(0).getAccountName();
                this.transientIBAN = cpts.get(0).getIbanNumber();
                this.transientEID = cpts.get(0).getEid();
            }
        }
    }

    @JsonIgnore
    public Boolean isPayingThroughCheques(Contract contract) {
        PicklistItem monthlyPayment = getItem(AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AccountingModule.PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT);

        return 0 < Setup.getRepository(PaymentRepository.class)
                .countByContractAndStatusAndTypeOfPaymentAndMethodOfPayment(
                        contract, PaymentStatus.PDC, monthlyPayment, PaymentMethod.CHEQUE);
    }

    //Jirra ACC-1435 from here
    @Column
    private Boolean smsConfirmed;

    @Formula("(EXISTS (SELECT 1 FROM BLOCKLOGS U WHERE U.CLIENT_ID = ID) and (SELECT NOT U.UNBLOCK FROM BLOCKLOGS U WHERE  U.CLIENT_ID = ID ORDER BY U.ID DESC LIMIT 1))")
    @JsonIgnore
    @Transient
    private Boolean salesBlocked;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @NotAudited
    @Formula("((select count(*)from SMSLOGS smslog0_ where smslog0_.CLIENT_ID=ID and smslog0_.RESPONSE='OK' and (smslog0_.TYPE in ('NEW_PROSPECT' , 'PROSPECT_LIST')))<>0 )")
    private Boolean smsMsgSent;

    @Transient
    @Enumerated(EnumType.STRING)
    private PortType port;

    @Transient
    private Boolean withBotProspects = false;

    @Column
    private Boolean hasNoWhatsAppNumber = false;

    @Column
    private Timestamp quartetShareDateTime;

    @Column
    private Boolean engagedSameDay;

    @Transient
    private List<InterviewVisit> activeInterviews;

    @Transient
    private PicklistItem searchNotEqualInitialContact;

    @OneToMany(mappedBy = "client")
    @OrderBy("id DESC")
    private List<BlockLog> blockLogs;

    @Column
    private String whatsappNumber;

    @Column
    private String spouseMobileNumber;

    @Column
    private String otherMatchingTypes;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem initialContact;

    @Column
    private String spouseName;

    @Column
    private Boolean sent = false;

    @Column(unique = true)
    private String token;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem prospectType;

    @Column
    private Double eagernessforOurService;

    @Column
    private Double agencyFeeConcern;

    @Column
    private Double monthlyConcern;

    @Column
    private String instructionstoOutboundEnchanters;

    @Column
    private String normalizedWhatsappNumber;

    @Formula("(EXISTS (SELECT 1 FROM CONTRACTS c WHERE c.CLIENT_ID = ID AND (c.STATUS = 'ACTIVE' or c.STATUS = 'PLANNED_RENEWAL')))")
    @NotAudited
    private Boolean hasActiveContract;

    @Column
    private Timestamp tokenGenerationTime;

    @Column
    private Timestamp firstPageVisit;

    @Column
    private Long preSalesBotId;

    @Column
    private String maxPanelId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem city;

    //// For add new contract
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem title;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem nationality;

    @Column
    private String instructionsToTheMaid;

    @Column
    private String spouseWhatsappNumber;

    @Column
    private String normalizedSpouseWhatsappNumber;

    @Column
    private Timestamp lastPageVisit;

    @Column
    private Integer vidVisitsCount;

    @Column
    @Lob
    private String notes;

    @Column
    @Enumerated(EnumType.STRING)
    private ClientSource source;

    @Column
    @Enumerated(EnumType.STRING)
    private WhenToContact whenToContact;

    @Column
    @Enumerated(EnumType.STRING)
    private NeedOfMaidDuration needOfMaidDuration;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "client", fetch = FetchType.LAZY)
    private List<ClientAgePreference> agePreferences;

    @OneToMany(mappedBy = "client", fetch = FetchType.LAZY)
    private List<ClientRemark> Remarks;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PromotionalEvent promotionalEvent;

    @ManyToOne(fetch = FetchType.LAZY)
    private CorporateCompany corporateCompany;

    @Column(columnDefinition = "boolean default false")
    private Boolean vip = false;

    @Column
    private String nickName;

    public Boolean getSmsConfirmed() {
        return smsConfirmed != null && smsConfirmed;
    }

    public Boolean getSmsMsgSent() {
        return smsMsgSent != null && smsMsgSent;
    }

    public PortType getPort() {
        return port;
    }

    public Boolean getWithBotProspects() {
        return withBotProspects;
    }

    @Transient
    String addedDate;

    public String getAddedDate() {
        if (this.getCreationDate() == null) return "";
        return DateUtil.formatDateDashed(this.getCreationDate());
    }

    public Boolean getHasNoWhatsAppNumber() {
        return hasNoWhatsAppNumber;
    }

    public Timestamp getQuartetShareDateTime() {
        return quartetShareDateTime;
    }

    public Boolean getEngagedSameDay() {
        return engagedSameDay;
    }

    public List<InterviewVisit> getActiveInterviews() {
        SelectQuery<InterviewVisit> query = new SelectQuery<>(InterviewVisit.class);
        query.filterBy("status.code", "=", PicklistItem.getCode(AccountingModule.INTERVIEW_STATUS_IN_INTERVIEW));
        query.filterBy("client.id", "=", this.getId());
        List<InterviewVisit> actInterviews = query.execute();
        activeInterviews = actInterviews;
        return activeInterviews;
    }

    public PicklistItem getSearchNotEqualInitialContact() {
        return searchNotEqualInitialContact;
    }

    public List<BlockLog> getBlockLogs() {
        return blockLogs;
    }

    @JsonIgnore
    public String getWhatsappNumber() {
        return whatsappNumber;
    }

    @JsonIgnore
    public String getSpouseMobileNumber() {
        return spouseMobileNumber;
    }

    public String getOtherMatchingTypes() {
        return otherMatchingTypes;
    }

    public PicklistItem getInitialContact() {
        return initialContact;
    }

    public String getSpouseName() {
        return spouseName;
    }

    public Boolean getSent() {
        return sent;
    }

    public String getToken() {
        return token;
    }

    public PicklistItem getProspectType() {
        return prospectType;
    }

    public Double getEagernessforOurService() {
        return eagernessforOurService;
    }

    public Double getAgencyFeeConcern() {
        return agencyFeeConcern;
    }

    public Double getMonthlyConcern() {
        return monthlyConcern;
    }

    public String getInstructionstoOutboundEnchanters() {
        return instructionstoOutboundEnchanters;
    }

    @JsonIgnore
    public String getNormalizedWhatsappNumber() {
        return normalizedWhatsappNumber;
    }

    public Boolean getHasActiveContract() {
        return hasActiveContract;
    }

    public Timestamp getTokenGenerationTime() {
        return tokenGenerationTime;
    }

    public Timestamp getFirstPageVisit() {
        return firstPageVisit;
    }

    public Long getPreSalesBotId() {
        return preSalesBotId;
    }

    public String getMaxPanelId() {
        return maxPanelId;
    }

    public PicklistItem getCity() {
        return city;
    }

    public PicklistItem getTitle() {
        return title;
    }

    public void setTitle(PicklistItem title) {
        this.title = title;
    }

    public void setNationality(PicklistItem nationality) {
        this.nationality = nationality;
    }

    public PicklistItem getNationality() {
        return nationality;
    }

    public String getInstructionsToTheMaid() {
        return instructionsToTheMaid;
    }

    @JsonIgnore
    public String getSpouseWhatsappNumber() {
        return spouseWhatsappNumber;
    }

    @JsonIgnore
    public String getNormalizedSpouseWhatsappNumber() {
        return normalizedSpouseWhatsappNumber;
    }

    public Timestamp getLastPageVisit() {
        return lastPageVisit;
    }

    public Integer getVidVisitsCount() {
        return vidVisitsCount;
    }

    public String getNotes() {
        return notes;
    }

    public ClientSource getSource() {
        return source;
    }

    public WhenToContact getWhenToContact() {
        return whenToContact;
    }

    public Boolean isVip() { return vip; }

    @Transient
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<Contract> activeContracts;

    public List<Contract> getActiveContracts() {
        if (hasActiveContract != null && hasActiveContract && activeContracts == null) {
            return this.getStatus() != null && this.getStatus().getCode().equals(PicklistItem.getCode(AccountingModule.CLIENT_STATUS_PROSPECT)) ?
                    new ArrayList() : Setup.getRepository(ContractRepository.class).findActiveContractByClient(this);
        }

        return activeContracts;
    }

    @Basic(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelListSerializer.class)
    public List<Contract> getAllContracts() {
        return Setup.getRepository(ContractRepository.class).findAllContractByClient(this);
    }

    @Transient
    private String addedTime;

    public String getAddedTime() {
        if (this.getCreationDate() == null) return "";
        return DateUtil.formatTime(this.getCreationDate());
    }

    public Boolean getSalesBlocked() {
        return salesBlocked;
    }

    public void setSalesBlocked(Boolean salesBlocked) {
        this.salesBlocked = salesBlocked;
    }

    public NeedOfMaidDuration getNeedOfMaidDuration() {
        return needOfMaidDuration;
    }

    public void setNeedOfMaidDuration(NeedOfMaidDuration needOfMaidDuration) {
        this.needOfMaidDuration = needOfMaidDuration;
    }

    public List<ClientAgePreference> getAgePreferences() {
        return agePreferences == null ? new ArrayList<>() :  agePreferences;
    }

    public void setAgePreferences(List<ClientAgePreference> agePreferences) {
        this.agePreferences = agePreferences;
    }

    public List<ClientRemark> getRemarks() {
        return Remarks;
    }

    public void setRemarks(List<ClientRemark> Remarks) {
        this.Remarks = Remarks;
    }

    public PromotionalEvent getPromotionalEvent() {
        return promotionalEvent;
    }

    public void setPromotionalEvent(PromotionalEvent promotionalEvent) {
        this.promotionalEvent = promotionalEvent;
    }

    public CorporateCompany getCorporateCompany() {
        return corporateCompany;
    }

    public void setCorporateCompany(CorporateCompany corporateCompany) {
        this.corporateCompany = corporateCompany;
    }
    //Jirra ACC-1435 to here

    @JsonIgnore
    public String getFirstName(boolean withTitle) {
        if (name == null || name.isEmpty()) return "";

        String firstName = name;
        if (name.contains(" ")) {
            String[] words = name.split(" ");
            for (int i = 0; i < words.length; i++) {
                if (words[i].length() > 2) {
                    firstName = words[i];
                    break;
                }
            }
        }
        return firstName == null ? "" : (this.getTitle() != null && withTitle ? this.getTitle().getName() + ". " : "") + firstName;
    }

    public Boolean getFired() {
        return fired;
    }

    public void setFired(Boolean fired) {
        this.fired = fired;
    }

    @JsonIgnore
    public Contract getActiveOrLastContract(){
        return getHasActiveContract() ?
                getActiveContracts().get(0) :
                Setup.getRepository(ContractRepository.class).findTopByClientOrderByCreationDateDesc(this);
    }


    public Boolean isAreaFilledByAreaListFile() {
        return areaFilledByAreaListFile;
    }

    public void setAreaFilledByAreaListFile(Boolean areaFilledByAreaListFile) {
        this.areaFilledByAreaListFile = areaFilledByAreaListFile;
    }

    public Boolean getvVip() { return vVip; }

    public void setvVip(Boolean vVip) { this.vVip = vVip; }

    public String getNickName() { return nickName; }

    public void setNickName(String nickName) { this.nickName = nickName; }

    @JsonIgnore
    public String getNickName(boolean withTitle) {
        return getNickName() != null ?
                (withTitle && this.getTitle() != null ? this.getTitle().getName() + ". " : "") + getNickName() : "";
    }
}