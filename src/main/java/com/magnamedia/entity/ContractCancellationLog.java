package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 27, 2019
 * Jirra ACC-1135
 */
@Entity
public class ContractCancellationLog extends BaseEntity implements Serializable {

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne
    private Contract contract;

    @Column
    private Date contractStartDate;

    @Column
    private Date lastWorkingDay;

    @Column
    private Double totalAEDReceived;

    @Column
    private Integer givenVacations;

    @Column
    private Integer numberOfDaysPaidFor;

    @Column
    private Integer numberOfDaysServed;

    @Column
    private Integer accruedVacations;

    @Column
    private Date paidUntil;

    @Column
    private Integer unpaidServedDays;

    @Column
    private Integer vacationDaysUnGiven;

    @Column
    private Integer totalDaysTheClientNeedsToPay;

    @Column
    private Double totalClientBalance;
    
    @Column
    private Double correctedBalance;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Date getContractStartDate() {
        return contractStartDate;
    }

    public void setContractStartDate(Date contractStartDate) {
        this.contractStartDate = contractStartDate;
    }

    public Date getLastWorkingDay() {
        return lastWorkingDay;
    }

    public void setLastWorkingDay(Date lastWorkingDay) {
        this.lastWorkingDay = lastWorkingDay;
    }

    public Double getTotalAEDReceived() {
        return totalAEDReceived;
    }

    public void setTotalAEDReceived(Double totalAEDReceived) {
        this.totalAEDReceived = totalAEDReceived;
    }

    public Integer getGivenVacations() {
        return givenVacations;
    }

    public void setGivenVacations(Integer givenVacations) {
        this.givenVacations = givenVacations;
    }

    public Integer getNumberOfDaysPaidFor() {
        return numberOfDaysPaidFor;
    }

    public void setNumberOfDaysPaidFor(Integer numberOfDaysPaidFor) {
        this.numberOfDaysPaidFor = numberOfDaysPaidFor;
    }

    public Integer getNumberOfDaysServed() {
        return numberOfDaysServed;
    }

    public void setNumberOfDaysServed(Integer numberOfDaysServed) {
        this.numberOfDaysServed = numberOfDaysServed;
    }

    public Integer getAccruedVacations() {
        return accruedVacations;
    }

    public void setAccruedVacations(Integer accruedVacations) {
        this.accruedVacations = accruedVacations;
    }

    public Date getPaidUntil() {
        return paidUntil;
    }

    public void setPaidUntil(Date paidUntil) {
        this.paidUntil = paidUntil;
    }

    public Integer getUnpaidServedDays() {
        return unpaidServedDays;
    }

    public void setUnpaidServedDays(Integer unpaidServedDays) {
        this.unpaidServedDays = unpaidServedDays;
    }

    public Integer getVacationDaysUnGiven() {
        return vacationDaysUnGiven;
    }

    public void setVacationDaysUnGiven(Integer vacationDaysUnGiven) {
        this.vacationDaysUnGiven = vacationDaysUnGiven;
    }

    public Integer getTotalDaysTheClientNeedsToPay() {
        return totalDaysTheClientNeedsToPay;
    }

    public void setTotalDaysTheClientNeedsToPay(Integer totalDaysTheClientNeedsToPay) {
        this.totalDaysTheClientNeedsToPay = totalDaysTheClientNeedsToPay;
    }

    public Double getTotalClientBalance() {
        return totalClientBalance;
    }

    public void setTotalClientBalance(Double totalClientBalance) {
        this.totalClientBalance = totalClientBalance;
    }
    
    public Double getCorrectedBalance() {
        return correctedBalance;
    }

    public void setCorrectedBalance(Double correctedBalance) {
        this.correctedBalance = correctedBalance;
    }
}
