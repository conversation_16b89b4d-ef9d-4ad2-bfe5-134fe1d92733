package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToOne;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */
@Entity
public class DDMsgLog extends BaseEntity {

    @Column
    private Boolean active;

    @Column
    private Date sendDate;

    @OneToOne
    private ContractPaymentTerm contractPaymentTerm;

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Date getSendDate() {
        return sendDate;
    }

    public void setSendDate(Date sendDate) {
        this.sendDate = sendDate;
    }

    public ContractPaymentTerm getContractPaymentTerm() {
        return contractPaymentTerm;
    }

    public void setContractPaymentTerm(ContractPaymentTerm contractPaymentTerm) {
        this.contractPaymentTerm = contractPaymentTerm;
    }
}
