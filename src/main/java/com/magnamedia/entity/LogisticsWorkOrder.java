package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.extra.Utils;

import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;

import java.io.Serializable;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.persistence.*;

@Entity
@Table(indexes = {@Index(columnList = "STATUS", unique = false),
        @Index(columnList = "PICKUPTYPE", unique = false),
        @Index(columnList = "TAXIWORKORDERSTATUS", unique = false)})
public class LogisticsWorkOrder extends BaseEntity implements Serializable {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM dd, yyyy");

    private static final SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Contract contract;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Client client;

    @ManyToOne
    private Housemaid housemaid;

    @Column
    private String otherPurpose;

    @Enumerated(EnumType.STRING)
    private LogisticsWorkOrderPurpose Purpose;

    @Enumerated(EnumType.STRING)
    private HousemaidArrival housemaidArrival;

    @Enumerated(EnumType.STRING)
    private LogisticsWorkOrderStatus status;

    @Lob
    private String initNote;

    @Column
    private Date leaveOn;

    @Column
    private Date arraiveOn;

    @Column
    private Boolean now;

    @Column
    @Lob
    private String fromAddress;

    @Lob
    @Column
    private String toAddress;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem taxiWorkOrderPurpose;

    @Enumerated(EnumType.STRING)
    private PickupType pickupType;

    @Enumerated(EnumType.STRING)
    private TaxiWorkOrderStatus taxiWorkOrderStatus;

    @Column
    private String googleKeyWords;

    @Column
    private Boolean talkToMaid;

    @Column
    private String rideUrl;

    @Transient
    private Transaction transaction;

    @Column
    private Double billAmount;

    @Column
    @JsonIgnore
    private Long transactionId;

    @Column
    private Date bookingDate;

    @Column(columnDefinition = "boolean default false")
    private boolean requestCovidLaserTest = false;
    
    //Jirra ACC-3373
    @Column(columnDefinition = "boolean default false")
    private Boolean addedFromCCApp = false;

    //Jirra ACC-3373
    @Transient
    private Boolean reScheduledFromCCAPP = false;

    public Date getBookingDate() {
        return bookingDate;
    }

    public void setBookingDate(Date bookingDate) {
        bookingDate = bookingDate;
    }


    @Column(columnDefinition = "boolean default false")
    private Boolean isTaxiBookedMsgSent = false;

    @Column(columnDefinition = "varchar(255) default 'WAITING'")
    @Enumerated(value = EnumType.STRING)
    private BillStatus billStatus = BillStatus.WAITING;

    @Column
    @Enumerated(value = EnumType.STRING)
    private BillSubStatus billSubStatus;

    @Column
    @Enumerated(value = EnumType.STRING)
    private BillType billType;

    @Column(columnDefinition = "boolean default false")
    private Boolean freeRide = false;

    @Column(columnDefinition = "double default 0")
    private double vat;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne
    private OfficeStaff officeStaff;

    @Column(columnDefinition = "boolean default false")
    private Boolean paidByRewardPoints;

    @Column
    private String bookingId;

    @Transient
    private Boolean ignoreVatUpdate;

    @Column
    private String contactNumber;

    @Column(columnDefinition = "boolean default false")
    private Boolean missingInvoice = false;

    public boolean getRequestCovidLaserTest() {
        return requestCovidLaserTest;
    }

    public void setRequestCovidLaserTest(boolean requestCovidLaserTest) {
        this.requestCovidLaserTest = requestCovidLaserTest;
    }

    public BillSubStatus getBillSubStatus() {
        return billSubStatus;
    }

    public void setBillSubStatus(BillSubStatus billSubStatus) {
        this.billSubStatus = billSubStatus;
    }

    public BillType getBillType() {
        return billType;
    }

    public void setBillType(BillType billType) {
        this.billType = billType;
    }

    public Boolean getFreeRide() {
        return freeRide;
    }

    public void setFreeRide(Boolean freeRide) {
        this.freeRide = freeRide;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public Boolean getIgnoreVatUpdate() {
        return ignoreVatUpdate;
    }

    public void setIgnoreVatUpdate(Boolean ignoreVatUpdate) {
        this.ignoreVatUpdate = ignoreVatUpdate;
    }

    public String getBookingId() {
        return bookingId;
    }

    public void setBookingId(String bookingId) {
        this.bookingId = bookingId;
    }

    public OfficeStaff getOfficeStaff() {
        return officeStaff;
    }

    public void setOfficeStaff(OfficeStaff officeStaff) {
        this.officeStaff = officeStaff;
    }

    public Boolean getPaidByRewardPoints() {
        return paidByRewardPoints;
    }

    public void setPaidByRewardPoints(Boolean paidByRewardPoints) {
        this.paidByRewardPoints = paidByRewardPoints;
    }

    public Boolean getIsTaxiBookedMsgSent() {
        return isTaxiBookedMsgSent;
    }

    public void setIsTaxiBookedMsgSent(Boolean isTaxiBookedMsgSent) {
        this.isTaxiBookedMsgSent = isTaxiBookedMsgSent;
    }

    public BillStatus getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(BillStatus billStatus) {
        this.billStatus = billStatus;
    }

    public double getVat() {
        return vat;
    }

    public void setVat(double vat) {
        this.vat = vat;
    }

    public Boolean getTaxiBookedMsgSent() {
        return isTaxiBookedMsgSent;
    }

    public void setTaxiBookedMsgSent(Boolean taxiBookedMsgSent) {
        isTaxiBookedMsgSent = taxiBookedMsgSent;
    }


    public Double getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(Double billAmount) {
        if (ignoreVatUpdate == null || !ignoreVatUpdate){
            vat = Utils.roundDownMode(billAmount - DiscountsWithVatHelper.getAmountWithoutVat(billAmount), 1);
        }

        this.billAmount = billAmount;
    }

    public String getDriverNote() {
        //String driverNumber = Setup.getParameter(Setup.getCurrentModule(), ClientManagementModule.PARAMETER_LOGISTICS_TEAM_PHONE);
        String clientNumber = this.client!=null ? this.client.getNormalizedMobileNumber() : "";
        return "Please contact " +
                ((!clientNumber.isEmpty() && !clientNumber.startsWith("+")) ? "+" : "")
                + clientNumber
                + " if you get lost, don't take the passenger to another destination in case she requests it";
    }

    public boolean getIsLate() {
        Calendar cal = Calendar.getInstance();
        Date now = cal.getTime();
        if (this.getLeaveOn() == null || this.getTaxiWorkOrderStatus() == null)
            return false;
        return this.getLeaveOn().getTime() <= now.getTime()
                && (this.getTaxiWorkOrderStatus().equals(TaxiWorkOrderStatus.PENDING)
                || this.getTaxiWorkOrderStatus().equals(TaxiWorkOrderStatus.ONGOING));
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public Transaction getTransaction() {
        if (transactionId != null) {
            TransactionRepository transactionRepository = Setup.getRepository(TransactionRepository.class);
            this.transaction = transactionRepository.findOne(transactionId);
        }
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public PickupType getPickupType() {
        return pickupType;
    }

    public void setPickupType(PickupType pickupType) {
        this.pickupType = pickupType;
    }

    public PicklistItem getTaxiWorkOrderPurpose() {
        return taxiWorkOrderPurpose;
    }

    public void setTaxiWorkOrderPurpose(PicklistItem taxiWorkOrderPurpose) {
        this.taxiWorkOrderPurpose = taxiWorkOrderPurpose;
    }

    public TaxiWorkOrderStatus getTaxiWorkOrderStatus() {
        return taxiWorkOrderStatus;
    }


    public void setTaxiWorkOrderStatus(TaxiWorkOrderStatus taxiWorkOrderStatus) {
        this.taxiWorkOrderStatus = taxiWorkOrderStatus;
        if(taxiWorkOrderStatus==TaxiWorkOrderStatus.PENDING)
            this.status=LogisticsWorkOrderStatus.PENDING;
        else if(taxiWorkOrderStatus==TaxiWorkOrderStatus.ONGOING)
            this.status=LogisticsWorkOrderStatus.ONGOING;
        else if(taxiWorkOrderStatus==TaxiWorkOrderStatus.CLOSED ||
                taxiWorkOrderStatus==TaxiWorkOrderStatus.CANCELLED )
            this.status=LogisticsWorkOrderStatus.DONE;
    }

    public String getGoogleKeyWords() {
        return googleKeyWords;
    }

    public void setGoogleKeyWords(String googleKeyWords) {
        this.googleKeyWords = googleKeyWords;
    }

    public Boolean getTalkToMaid() {
        return talkToMaid;
    }

    public void setTalkToMaid(Boolean talkToMaid) {
        this.talkToMaid = talkToMaid;
    }

    public String getRideUrl() {
        return rideUrl;
    }

    public void setRideUrl(String rideUrl) {
        this.rideUrl = rideUrl;
    }

    public String getInitNote() {
        return initNote;
    }

    public void setInitNote(String initNote) {
        this.initNote = initNote;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Housemaid getHousemaid() {
        return housemaid;
    }

    public void setHousemaid(Housemaid housemaid) {
        this.housemaid = housemaid;
    }

    public HousemaidArrival getHousemaidArrival() {
        return housemaidArrival;
    }

    public void setHousemaidArrival(HousemaidArrival housemaidArrival) {
        this.housemaidArrival = housemaidArrival;
    }

    public LogisticsWorkOrderStatus getStatus() {
        return status;
    }

    public void setStatus(LogisticsWorkOrderStatus status) {
        this.status = status;
    }

    public Date getLeaveOn() {
        return leaveOn;
    }

    public void setLeaveOn(Date leaveOn) {
        this.leaveOn = leaveOn;
    }

    public Date getArraiveOn() {
        return arraiveOn;
    }

    public void setArraiveOn(Date arraiveOn) {
        this.arraiveOn = arraiveOn;
    }

    public String getFromAddress() {
        return fromAddress;
    }

    public void setFromAddress(String fromAddress) {
        this.fromAddress = fromAddress;
    }

    public String getToAddress() {
        return toAddress;
    }

    public void setToAddress(String toAddress) {
        this.toAddress = toAddress;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public String getOtherPurpose() {
        return otherPurpose;
    }

    public void setOtherPurpose(String otherPurpose) {
        this.otherPurpose = otherPurpose;
    }

    public LogisticsWorkOrderPurpose getPurpose() {
        return Purpose;
    }

    public void setPurpose(LogisticsWorkOrderPurpose Purpose) {
        this.Purpose = Purpose;
    }


    public Boolean getNow() {
        if (now == null) {
            return false;
        }
        return now;
    }

    public void setNow(Boolean now) {
        this.now = now;
    }


    @JsonIgnore
    public String getPassengerName() {

        return getHousemaid()!=null ? getHousemaid().getName()!=null ? getHousemaid().getName() : "-" : getOfficeStaff()!=null ? getOfficeStaff().getName()!=null ? getOfficeStaff().getName() : "-" : "-";
    }


    public Boolean getAddedFromCCApp() {
        return addedFromCCApp;
    }

    public void setAddedFromCCApp(Boolean addedFromCCApp) {
        this.addedFromCCApp = addedFromCCApp;
    }

    public Boolean getReScheduledFromCCAPP() {
        return reScheduledFromCCAPP;
    }

    public void setReScheduledFromCCAPP(Boolean reScheduledFromCCAPP) {
        this.reScheduledFromCCAPP = reScheduledFromCCAPP;
    }

    public Boolean getMissingInvoice() { return missingInvoice; }

    public void setMissingInvoice(Boolean missingInvoice) {
        this.missingInvoice = missingInvoice;
    }
}
