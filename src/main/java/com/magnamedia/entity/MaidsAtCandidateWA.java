package com.magnamedia.entity;

import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.workflow.FormField;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@Entity
@Setter
@Getter
@Where(clause = "DELETED = false")
public class MaidsAtCandidateWA extends WorkflowEntity implements Serializable {

    public MaidsAtCandidateWA() {
        super(null);
    }

    @Label
    @Column
    private String name;

    @Column
    private String employerName;

    @Column
    private String employerPhoneNumber;

    @Column(nullable = false)
    private boolean deleted = false;

    @Column
    private String mobileNumber;

    @Column
    private String whatsappNumber;

    @Override
    public List<FormField> getForm(String s) {
        return null;
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }
}
