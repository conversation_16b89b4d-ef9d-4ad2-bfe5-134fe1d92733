package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on OCT 11, 2017
 */
@Entity
public class RenewRequestExpense extends VisaExpense<RenewRequest> {

    public RenewRequestExpense() {
        super(null,
                null);
    }

    public RenewRequestExpense(RenewRequest request,
            ExpensePurpose purpose) {
        super(request,
                purpose);
    }

    @Override
    public String getVisaExpenseType() {
        return "RenewRequestExpense";
    }
}
