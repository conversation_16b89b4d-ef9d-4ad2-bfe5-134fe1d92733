package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.module.type.DDMsgConfigType;

import javax.persistence.*;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */
@Entity
public class DDMsgConfig extends BaseEntity {

    @Column(nullable = false, unique = true)
    @Enumerated(EnumType.STRING)
    private DDMsgConfigType msgType;

    @Column(nullable = false)
    @Lob
    private String msgText;

    @Column
    @Lob
    private String msgPossibleVariables;

    @Column
    @Lob
    private String msgTrigger;

    @Column
    private String emailSubject;

    @Column(nullable = false)
    private Boolean active = true;

    public DDMsgConfigType getMsgType() {
        return msgType;
    }

    public void setMsgType(DDMsgConfigType msgType) {
        this.msgType = msgType;
    }

    public String getMsgText() {
        return msgText;
    }

    public void setMsgText(String msgText) {
        this.msgText = msgText;
    }

    public String getMsgPossibleVariables() {
        return msgPossibleVariables;
    }

    public void setMsgPossibleVariables(String msgPossibleVariables) {
        this.msgPossibleVariables = msgPossibleVariables;
    }

    public String getMsgTrigger() {
        return msgTrigger;
    }

    public void setMsgTrigger(String msgTrigger) {
        this.msgTrigger = msgTrigger;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }
}
