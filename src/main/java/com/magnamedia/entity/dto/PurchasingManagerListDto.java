package com.magnamedia.entity.dto;

import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.workflow.type.PurchasingToDoType;

import java.util.Date;

/**
 * <PERSON> (Feb 02, 2021)
 */
public class PurchasingManagerListDto {
    public PurchasingManagerListDto(PurchasingToDo purchasingToDo) {
        this.id = purchasingToDo.getId();
        this.creationDate = purchasingToDo.getTaskModifiedDate() != null ?
                purchasingToDo.getTaskModifiedDate() : purchasingToDo.getCreationDate();
        this.orderCycleName = purchasingToDo.getOrderCycleName();
        this.orderCycle = purchasingToDo.getCategory().getOrderCycle().getName();
        this.categoryName = purchasingToDo.getCategory().getName();
        this.todoName = purchasingToDo.getTaskName();
    }

    public PurchasingManagerListDto(MaintenanceRequest maintenanceRequest) {
        this.id = maintenanceRequest.getId();
        this.creationDate = maintenanceRequest.getTaskModifiedDate() != null ?
                maintenanceRequest.getTaskModifiedDate() : maintenanceRequest.getCreationDate();
//        this.orderCycleName = "";
//        this.orderCycle = maintenanceRequest.getCategory().getOrderCycle().getName();
//        this.categoryName = maintenanceRequest.getCategory().getName();
        this.todoName = maintenanceRequest.getTaskName();
    }

    private Long id;
    private Date creationDate;
    private String orderCycleName;
    private String categoryName;
    private String orderCycle;
    private String todoName;

    public String getOrderCycle() {
        return orderCycle;
    }

    public void setOrderCycle(String orderCycle) {
        this.orderCycle = orderCycle;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getOrderCycleName() {
        return orderCycleName;
    }

    public void setOrderCycleName(String orderCycleName) {
        this.orderCycleName = orderCycleName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getTodoName() {
        return todoName;
    }

    public void setTodoName(String todoName) {
        this.todoName = todoName;
    }

}
