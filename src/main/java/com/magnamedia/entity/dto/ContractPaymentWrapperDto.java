package com.magnamedia.entity.dto;

import com.magnamedia.entity.Bucket;
import com.magnamedia.entity.ContractPaymentWrapper;
import com.magnamedia.entity.Revenue;
import com.magnamedia.module.type.VatType;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 11, 2021
 */
public class ContractPaymentWrapperDto {
    
    private Long id;
    private boolean vatPaidByClient;
    private VatType vatType;
    private Revenue revenue;
    private Bucket toBucket;
    private String description;
    private String notes;
    private Long transactionId;
    private Double actualReceivedAmount;
    private boolean includeWorkerSalary;
    private boolean initial;
    

    public void updateContractPaymentWrapperFromDto(ContractPaymentWrapper contractPaymentWrapper){
        contractPaymentWrapper.setActualReceivedAmount(this.actualReceivedAmount);
        contractPaymentWrapper.setIncludeWorkerSalary(this.includeWorkerSalary);
        contractPaymentWrapper.setInitial(this.initial);
        contractPaymentWrapper.setVatPaidByClient(this.vatPaidByClient);
        contractPaymentWrapper.setVatType(this.vatType);
        contractPaymentWrapper.setRevenue(this.revenue);
        contractPaymentWrapper.setToBucket(this.toBucket);
        contractPaymentWrapper.setDescription(this.description);
        contractPaymentWrapper.setNotes(this.notes);
        contractPaymentWrapper.setTransactionId(this.transactionId);
    }
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isVatPaidByClient() {
        return vatPaidByClient;
    }

    public void setVatPaidByClient(boolean vatPaidByClient) {
        this.vatPaidByClient = vatPaidByClient;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public Double getActualReceivedAmount() {
        return actualReceivedAmount;
    }

    public void setActualReceivedAmount(Double actualReceivedAmount) {
        this.actualReceivedAmount = actualReceivedAmount;
    }

    public boolean isIncludeWorkerSalary() {
        return includeWorkerSalary;
    }

    public void setIncludeWorkerSalary(boolean includeWorkerSalary) {
        this.includeWorkerSalary = includeWorkerSalary;
    }

    public boolean isInitial() {
        return initial;
    }

    public void setInitial(boolean initial) {
        this.initial = initial;
    }
}
