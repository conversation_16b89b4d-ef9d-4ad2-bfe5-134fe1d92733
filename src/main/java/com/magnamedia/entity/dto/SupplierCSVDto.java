package com.magnamedia.entity.dto;

import com.magnamedia.entity.Supplier;
import com.magnamedia.module.type.ExpensePaymentMethod;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
@AllArgsConstructor
public class SupplierCSVDto {
    private Long id;
    private String name;
    private String paymentMethod;
    private String financialName;
    private String internationalAsString;
    private String ibanForSupplier;
    private String accountNumberForSupplier;
    private String swiftForSupplier;
    private String vatRegisteredAsString;

    public SupplierCSVDto(Supplier supplier) {
        this.id = supplier.getId();
        this.name = supplier.getName();

        this.paymentMethod = supplier.getPaymentMethods().stream()
                .filter(Objects::nonNull)
                .map(ExpensePaymentMethod::getLabel)
                .collect(Collectors.joining(", "));


        this.financialName = supplier.getPaymentMethods() != null &&
                supplier.getPaymentMethods().contains(ExpensePaymentMethod.CREDIT_CARD) &&
                supplier.getNameInFinancialStatement() != null ?
                supplier.getNameInFinancialStatement() : "";

        if (supplier.getPaymentMethods() != null &&
                supplier.getPaymentMethods().contains(ExpensePaymentMethod.BANK_TRANSFER)) {
            if (supplier.getInternational() != null && supplier.getInternational()) {
                this.internationalAsString = "International";
            } else {
                this.internationalAsString = "Local";
            }
        } else {
            this.internationalAsString = "";
        }

        this.ibanForSupplier = supplier.getPaymentMethods() != null &&
                supplier.getPaymentMethods().contains(ExpensePaymentMethod.BANK_TRANSFER) &&
                supplier.getIban() != null ? supplier.getIban() : "";

        this.accountNumberForSupplier = supplier.getPaymentMethods() != null &&
                supplier.getPaymentMethods().contains(ExpensePaymentMethod.BANK_TRANSFER) &&
                supplier.getAccountNumber() != null ? supplier.getAccountNumber() : "";

        this.swiftForSupplier = supplier.getPaymentMethods() != null &&
                supplier.getPaymentMethods().contains(ExpensePaymentMethod.BANK_TRANSFER) &&
                supplier.getInternational() != null &&
                supplier.getInternational() &&
                supplier.getSwift() != null ? supplier.getSwift() : "";


        this.vatRegisteredAsString = supplier.getVatRegistered() != null && supplier.getVatRegistered()
                ? "Yes" : "No";

    }
}