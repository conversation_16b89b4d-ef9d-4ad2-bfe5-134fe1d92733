package com.magnamedia.entity.dto;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.module.type.HousemaidType;

import java.util.Date;
import java.util.List;

/**
 * <PERSON> (Jan 21, 2021)
 */
public class ExpenseRequestPayInvoiceDto {
    private Long id;
    private String expense;
    private String relatedTo;
    private Double amount;
    private Date expenseRequestDate;
    private String description;
    private List<Attachment> attachments;
    private String maidType;
    private String reason;

    public ExpenseRequestPayInvoiceDto() {
    }

    public ExpenseRequestPayInvoiceDto(ExpenseRequestTodo exp) {
        this.id = exp.getId();
        this.expense = exp.getExpense().getCaption();
        this.relatedTo = exp.getRelatedToInfo();
        this.amount = exp.getAmount();
        this.expenseRequestDate = exp.getCreationDate();
        this.description = exp.getDescription();
        this.attachments = exp.getAttachments();
    }

    // ACC-5346
    public ExpenseRequestPayInvoiceDto(
            Long id, String relatedTo, String expense, Double amount,
            Date creationDate, HousemaidType type, String reason) {

        this.id = id;
        this.relatedTo = relatedTo;
        this.expense = expense;
        this.amount = amount;
        this.expenseRequestDate = creationDate;
        this.maidType = type != null ? type.equals(HousemaidType.MAID_VISA) ? "MV" : "CC" : "";
        this.reason = reason;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getExpense() {
        return expense;
    }

    public void setExpense(String expense) {
        this.expense = expense;
    }

    public String getRelatedTo() {
        return relatedTo;
    }

    public void setRelatedTo(String relatedTo) {
        this.relatedTo = relatedTo;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getExpenseRequestDate() {
        return expenseRequestDate;
    }

    public void setExpenseRequestDate(Date expenseRequestDate) {
        this.expenseRequestDate = expenseRequestDate;
    }

    public String getDescription() { return description; }

    public void setDescription(String description) { this.description = description; }

    public List<Attachment> getAttachments() { return attachments; }

    public void setAttachments(List<Attachment> attachments) { this.attachments = attachments; }

    public String getMaidType() { return maidType; }

    public void setMaidType(String maidType) { this.maidType = maidType; }

    public String getReason() { return reason; }

    public void setReason(String reason) { this.reason = reason; }
}
