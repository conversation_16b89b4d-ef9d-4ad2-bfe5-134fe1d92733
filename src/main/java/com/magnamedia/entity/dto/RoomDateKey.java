package com.magnamedia.entity.dto;

import java.util.Date;
import java.util.Objects;

/**
 * <PERSON> (Apr 19, 2021)
 */
public class RoomDateKey {
    public RoomDateKey(String roomNumber, Date assignmentDate) {
        this.roomNumber = roomNumber;
        this.assignmentDate = assignmentDate;
    }

    public String roomNumber;
    public Date assignmentDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RoomDateKey that = (RoomDateKey) o;
        return Objects.equals(roomNumber, that.roomNumber) && Objects.equals(assignmentDate, that.assignmentDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roomNumber, assignmentDate);
    }
}
