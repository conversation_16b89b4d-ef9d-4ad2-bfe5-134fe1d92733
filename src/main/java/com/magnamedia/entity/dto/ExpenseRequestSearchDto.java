package com.magnamedia.entity.dto;

import com.magnamedia.entity.ExpenseRelatedTo;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;

import java.util.Date;
import java.util.List;

public class ExpenseRequestSearchDto {
    private Long id;
    private List<Long> expenseIds;
    private String expenseCaption;
    private List<ExpenseRequestStatus> Statuses;
    private String amountOperator;
    private Double amount;
    private List<ExpensePaymentMethod> paymentMethods;
    private ExpenseBeneficiaryType beneficiaryType;
    private List<Long> relatedToIds;
    private String relatedToName;
    private ExpenseRelatedTo.ExpenseRelatedToType relatedToType;
    private List<Long> beneficiaryIds;
    private String beneficiaryName;
    private Date date1;
    private Date date2;
    private String dateOperator;
    private List<Long> requestorIds;
    private String approvedBy;
    private Long approvedById;

    private Boolean confirmed;
    
    public Long getId() {
        return id;
    }
    private Boolean isRefunded;
    private Boolean refundConfirmed;

    private Boolean onlyUnconfirmed;

    private String bucketName;

    private List<Long> bucketIds;

    private String pendingForApproval;

    private String transactionOperator;

    private Long transactionId;

    public String getBucketName() {return bucketName;}

    public void setBucketName(String bucketName) {this.bucketName = bucketName;}



    public List<Long> getBucketIds() {
        return bucketIds;
    }

    public void setBucketIds(List<Long> bucketIds) {
        this.bucketIds = bucketIds;
    }

    public String getPendingForApproval() {return pendingForApproval;}

    public void setPendingForApproval(String pendingForApproval) {this.pendingForApproval = pendingForApproval;}

    public String getTransactionOperator() {
        return transactionOperator;
    }

    public void setTransactionOperator(String transactionOperator) {
        this.transactionOperator = transactionOperator;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public Boolean getRefunded() {
        return isRefunded;
    }

    public void setIsRefunded(Boolean refunded) {
        isRefunded = refunded;
    }

    public Boolean getRefundConfirmed() {
        return refundConfirmed;
    }

    public void setRefundConfirmed(Boolean refundConfirmed) {
        this.refundConfirmed = refundConfirmed;
    }

    public ExpenseRequestSearchDto() {
    }

    public List<Long> getExpenseIds() {
        return expenseIds;
    }

    public void setExpenseIds(List<Long> expenseIds) {
        this.expenseIds = expenseIds;
    }

    public String getExpenseCaption() {
        return expenseCaption;
    }

    public void setExpenseCaption(String expenseCaption) {
        this.expenseCaption = expenseCaption;
    }

    public List<ExpenseRequestStatus> getStatuses() {
        return Statuses;
    }

    public void setStatuses(List<ExpenseRequestStatus> statuses) {
        this.Statuses = statuses;
    }

    public String getAmountOperator() {
        return amountOperator;
    }

    public void setAmountOperator(String amountOperator) {
        this.amountOperator = amountOperator;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public List<ExpensePaymentMethod> getPaymentMethods() {
        return paymentMethods;
    }

    public void setPaymentMethods(List<ExpensePaymentMethod> paymentMethods) {
        this.paymentMethods = paymentMethods;
    }

    public ExpenseBeneficiaryType getBeneficiaryType() {
        return beneficiaryType;
    }

    public void setBeneficiaryType(ExpenseBeneficiaryType beneficiaryType) {
        this.beneficiaryType = beneficiaryType;
    }

    public List<Long> getRelatedToIds() {
        return relatedToIds;
    }

    public void setRelatedToIds(List<Long> relatedToIds) {
        this.relatedToIds = relatedToIds;
    }

    public String getRelatedToName() {
        return relatedToName;
    }

    public void setRelatedToName(String relatedToName) {
        this.relatedToName = relatedToName;
    }

    public ExpenseRelatedTo.ExpenseRelatedToType getRelatedToType() {
        return relatedToType;
    }

    public void setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType relatedToType) {
        this.relatedToType = relatedToType;
    }

    public List<Long> getBeneficiaryIds() {
        return beneficiaryIds;
    }

    public void setBeneficiaryIds(List<Long> beneficiaryIds) {
        this.beneficiaryIds = beneficiaryIds;
    }

    public String getBeneficiaryName() {
        return beneficiaryName;
    }

    public void setBeneficiaryName(String beneficiaryName) {
        this.beneficiaryName = beneficiaryName;
    }

    public Date getDate1() {
        return date1;
    }

    public void setDate1(Date date1) {
        this.date1 = date1;
    }

    public Date getDate2() {
        return date2;
    }

    public void setDate2(Date date2) {
        this.date2 = date2;
    }

    public String getDateOperator() {
        return dateOperator;
    }

    public void setDateOperator(String dateOperator) {
        this.dateOperator = dateOperator;
    }

    public List<Long> getRequestorIds() {
        return requestorIds;
    }

    public void setRequestorIds(List<Long> requestorIds) {
        this.requestorIds = requestorIds;
    }

    public Long getApprovedById() {
        return approvedById;
    }

    public void setApprovedById(Long approvedById) {
        this.approvedById = approvedById;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public Boolean getOnlyUnconfirmed() {
        return onlyUnconfirmed;
    }

    public void setOnlyUnconfirmed(Boolean onlyUnconfirmed) {
        this.onlyUnconfirmed = onlyUnconfirmed;
    }
}
