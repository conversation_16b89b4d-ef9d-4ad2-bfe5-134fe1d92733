package com.magnamedia.entity;

import javax.persistence.Entity;
import com.magnamedia.core.entity.BaseEntity;
import java.util.Date;
//import com.magnamedia.service.BucketService;

/**
 *
 * <AUTHOR> <<EMAIL>>
 *
 */
@Entity
public class AdsSpending extends BaseEntity {

//    @Label
    private Date adsSpendingDate;

    private Double maidsATAdsSpending;
    private Double maidsCCAdsSpending;

    public Date getAdsSpendingDate() {
        return adsSpendingDate;
    }

    public void setAdsSpendingDate(Date adsSpendingDate) {
        this.adsSpendingDate = adsSpendingDate;
    }

    public Double getMaidsATAdsSpending() {
        return maidsATAdsSpending;
    }

    public void setMaidsATAdsSpending(Double maidsATAdsSpending) {
        this.maidsATAdsSpending = maidsATAdsSpending;
    }

    public Double getMaidsCCAdsSpending() {
        return maidsCCAdsSpending;
    }

    public void setMaidsCCAdsSpending(Double maidsCCAdsSpending) {
        this.maidsCCAdsSpending = maidsCCAdsSpending;
    }
    
    

}
