package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.*;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.serializer.ContractSeriliserForPayment;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.SmsFormatter;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.apache.commons.lang3.BooleanUtils;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Date;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@Entity
//Jirra ACC-300
//Jirra ACC-1092
@Table(
        indexes = {
            @Index(columnList = "dateOfPayment", name = "DATE_OF_PAYMENT_IDX"),
            @Index(columnList = "methodOfPayment", name = "IDX_METHOD_OF_PAYMENT"),
            @Index(columnList = "dateOfBouncing", name = "IDX_DATE_OF_BOUNCING"),
            @Index(columnList = "bouncedFlowPausedForReplacement", name = "IDX_BOUNCED_FLOW_PAUSED_FOR_REPLACEMENT"),
            @Index(columnList = "directDebitId", unique = false),
            @Index(columnList = "directDebitFileId", unique = false),
            @Index(columnList = "oldApplicationId", unique = false)
        })
public class Payment extends BaseEntityWithAdditionalInfo implements Serializable {

    public final static String PAYMENT_MUST_COLLECTED_MANUALLY = "payment_must_collected_manually";

    @Transient
    private static final Logger logger = Logger.getLogger(Payment.class.getName());
    
    @Transient
    public static final String FILE_TAG_PAYMENTS_RECEIPT = "payment_receipt";
    
    @Transient
    public static final String HAS_NO_E_SIGNATURE = "has_no_e_signature";

    // ACC-789
    @Transient
    public static final String FILE_TAG_PAYMENTS_TAX_INVOICE = "payment_tax_invoice";

    // ACC-711 ACC-1092
    @Transient
    public static final String FILE_TAG_PAYMENTS_TAX_CREDIT_NOTE = "payment_tax_credit_note";

    // CM-717
    @Transient
    public static final String FILE_TAG_PAYMENTS_TAX_INVOICE_DUPLICATE = "payment_tax_invoice_duplicate";

    @Transient
    public static final String FILE_TAG_PAYMENTS_TAX_CREDIT_NOTE_DUPLICATE = "payment_tax_credit_note_duplicate";

    @Transient
    private Boolean vatFilledFromFrontEnd = false;

    @Transient
    private String contractCancellationReason;

    @Column
    private Long parentWrapperId;
    
    @Column(columnDefinition = "boolean default false")
    private Boolean includeWorkerSalary = false;

    @Column
    private Double vatPercent;

    @Column
    private Double vat;

    @Column
    private Double paymentWithoutVAT;

    @Column
    private Double visaFees;

    @Column
    private Double visaFeesVAT;

    @Column
    private Double visaFeesWithoutVAT;

    @Column(nullable = false)
    private Double amountOfPayment;

    @Column(columnDefinition = "boolean default false")
    Boolean automaticallyAdded = false;

    @JsonSerialize(using = ContractSeriliserForPayment.class)
    @ManyToOne
    private Contract contract;

    @Column
    private String chequeNumber;

    @Column
    private String chequeName;

    @Column
    private Date dateOfBouncing;

    @Column(nullable = false)
    private Date dateOfPayment;

    @Column
    private Boolean isReplacement = false;

    @Column(length = 1000)
    private String note;

    @Column
    private Boolean prepareToRefund = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean bouncedFlowPausedForReplacement = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean bouncedAgainDuringPausingPeriod = false;

    @Column
    private java.util.Date bouncedFlowPausedFromDate;

    //Jirra ACC-304
    @Column(columnDefinition = "double default 0")
    private Boolean replaced = false;

    @Column(columnDefinition = "boolean default false")
    private Boolean replacedMsgSent = false;
    @Column(columnDefinition = "boolean default false")
    private Boolean bouncedMsgSent = false;

    //Jirra ACC-1728
    @Column(columnDefinition = "boolean default false")
    private Boolean refundSentFromBF = false;

    @NotAudited
    @OneToOne(fetch = FetchType.LAZY)
    private Payment replacementFor;

    @NotAudited
    @Formula("(SELECT COUNT(*) FROM PAYMENTS P WHERE P.STATUS='RECEIVED' AND P.REPLACEMENT_FOR_ID =id)")
    private int replacmentsCount;

    @Column
    private Long bouncedPaymentId;

    @Enumerated(EnumType.STRING)
    private PaymentStatus status;

    //Jirra ACC-1135
    @Column(columnDefinition = "boolean default false")
    private Boolean bouncedInCancellationWaitingPeriod;

    @Enumerated(EnumType.STRING)
    private PaymentMethod methodOfPayment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem reasonOfBouncingCheque;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem typeOfPayment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem subType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem typeOfDiscount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem bankName;

    @Column
    private String reasonOfDiscount;

    //Jirra ACC-304
    @Column(columnDefinition = "double default 0")
    private Boolean chequeWithTheBank;

    @Column
    private String policeCaseNumber;

    //Jirra ACC-430
    @Column
    private String businessObjectId;

    @Column
    private String businessObjectType;

    @Column(columnDefinition = "boolean default false")
    private Boolean ignoreVAT = false;

    @Column(columnDefinition = "boolean default true")
    private Boolean vatPaidByClient = true;

    @Column(columnDefinition = "boolean default false")
    private Boolean includedInVatFix = false;

    @Transient
    private boolean matchedWithExpectedWireTransfer;

    @Column
    private Long directDebitId;

    @Transient
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebit directDebit;

    //Jirra ACC-1587
    @Column
    private Long directDebitFileId;

    @Transient
    @JsonSerialize(using = IdLabelSerializer.class)
    private DirectDebitFile directDebitFile;

    // Jirra ACC-1135
    @Column(columnDefinition = "boolean default false")
    private Boolean refundSentToExpensify;

    @Column(columnDefinition = "boolean default false")
    private Boolean fullyRefunded;
    //    @Column(columnDefinition = "boolean default false")
//    private Boolean toBeFullRefunded = false;
//
    @Column(columnDefinition = "boolean default false")
    private Boolean clientInformedAboutRefund = false;

    //Jirra ACC-1498
    @Temporal(value = TemporalType.TIMESTAMP)
    private java.util.Date dateChangedToPDP;

    //Jirra ACC-1544
    @Temporal(value = TemporalType.TIMESTAMP)
    private java.util.Date dateChangedToReceived;
    //Jirra ACC-1659
    @Column
    private Boolean isProRated = Boolean.FALSE;

    //Jirra ACC-1633	
    @Column(columnDefinition = "boolean default false")
    private Boolean requiredForUnfitToWork = false;

    //Jirra ACC-2503
    @Column(columnDefinition = "boolean default false")
    private Boolean requiredForBouncing = false;

    @Column
    private Long humanSmsId;

    @Column
    private Long bankStatmentTransactionId;

    //Jirra ACC-2991
    @ManyToOne(fetch = FetchType.LAZY)
    private ClientRefundToDo clientRefundToDo;

    @Column
    private java.util.Date lastMessageDate;

    public Long getDirectDebitId() {
        return directDebitId;
    }

    public void setDirectDebitId(Long directDebitId) {
        this.directDebitId = directDebitId;
    }

    public Long getDirectDebitFileId() {
        return directDebitFileId;
    }

    public void setDirectDebitFileId(Long directDebitFileId) {
        this.directDebitFileId = directDebitFileId;
    }

    //Jirra 1598
    @Column(columnDefinition = "boolean default false")
    private Boolean sentToBankByMDD = false;

    //Jirra 1511    
    @Column(columnDefinition = "int default 0")
    private Integer reminder = 0;

    @Column(columnDefinition = "int default 0")
    private Integer trials = 0;

    @Column(nullable = true)
    private Double workerSalary;

    //ACC-8173
    @Column(columnDefinition = "boolean default false")
    private boolean causedTermination = false;

    // ACC-1721
    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "payment",
            fetch = FetchType.LAZY)
    private List<BouncedPaymentLog> bouncedPaymentLogs;

    // acc-1777
    @Transient
    private boolean msgBrChecked = false;

    @Transient
    private boolean msgBr2Checked = false;

    @Transient
    private boolean msgBr3Checked = false;

    //Jirra ACC-1721
    @Transient
    private boolean pBF1BrChecked = false;

    //Jirra ACC-1721
    @Transient
    private boolean pBF2BrChecked = false;

    //Jirra ACC-2054
    @Transient
    private boolean pBF3BrChecked = false;

    //Jirra ACC-2071
    @Transient
    private boolean pBF4BrChecked = false;

    //Jirra ACC-1915
    @Transient
    private boolean rCTRBPBrChecked = false;

    //Jirra ACC-1721
    @Transient
    private boolean pCCBrChecked = false;

    //Jirra ACC-1721
    @Transient
    private boolean tPEBrChecked = false;

    //Jirra ACC-2180
    @Transient
    private boolean pRNRBrChecked = false;

    //Jirra ACC-2800
    @Transient
    private boolean sNBPBrChecked = false;

    //Jirra ACC-3233
    @Transient
    private boolean dPBrChecked = false;
    //Jirra ACC-3136
    @Transient
    private boolean sBABPBrChecked = false;

    //Jirra ACC-2653
    @Transient
    private Boolean bouncedLogAdded = false;

    @Transient
    private java.util.Date contractScheduleDateOfTermination;


    @Column(columnDefinition = "boolean default false")
    private Boolean updatedFromBankStatement = false;

    @Transient
    private boolean ignorePostingEngineBR = false;

    //Jirra ACC-2180
    @Column(columnDefinition = "boolean default false")
    private Boolean toBeRefundedPartially;

    @Column
    private Double amountToRefundPartially;

    //Jirra ACC-2475
    @Column
    private Double refundAmount;

    @Column
    private Long refundRequestId;

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private PaymentRequestPurpose refundPurpose;

    @Column
    private String oldApplicationId;

    @Transient
    private Boolean paymentIsInitial;

    @Column(columnDefinition = "boolean default false")
    private Boolean recurring = false;

    //CMA-622
    @Column(columnDefinition = "boolean default false")
    private boolean switchingAccountActionTaken = false;

    @Transient
    private boolean passStopConfirmationTodo = false;

    //Jirra ACC-3388
    @Column
    @ColumnDefault("false")
    private boolean online = false;

    @Column(nullable = false)
    private Double discount = 0.0;
    @Column
    private Double workerSalaryVAT = 0.0;

    @Column
    private Double workerSalaryWithoutVAT = 0.0;

    // ACC-8633
    @Column
    private Long contractPaymentId;

    @Transient
    private boolean deepSilentSave = false;

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public Integer getReminder() {
        return reminder;
    }

    public void setReminder(Integer reminder) {
        this.reminder = reminder;
    }

    public Integer getTrials() {
        return trials;
    }

    public void setTrials(Integer trials) {
        this.trials = trials;
    }

    public List<BouncedPaymentLog> getBouncedPaymentLogs() {
        return bouncedPaymentLogs;
    }

    public void setBouncedPaymentLogs(List<BouncedPaymentLog> bouncedPaymentLogs) {
        this.bouncedPaymentLogs = bouncedPaymentLogs;
    }

    //    public Long getDirectDebitFileId() {
//        return DirectDebitId;
//    }
//
//    public void setDirectDebitFileId(Long DirectDebitId) {
//        this.DirectDebitId = DirectDebitId;
//    }

    //Jirra ACC-594
    public DirectDebit getDirectDebit() {
        if (this.directDebit == null && directDebitId != null) {
            DirectDebitRepository directDebitRepository =
                    Setup.getRepository(DirectDebitRepository.class);
            this.directDebit = directDebitRepository.findOne(directDebitId);
        }
        return this.directDebit;
    }

    public void setDirectDebit(DirectDebit directDebit) {
        this.directDebit = directDebit;
    }

    public DirectDebitFile getDirectDebitFile() {
        if (this.directDebitFile == null && directDebitFileId != null) {
            DirectDebitFileRepository directDebitFileRepository =
                    Setup.getRepository(DirectDebitFileRepository.class);
            this.directDebitFile = directDebitFileRepository.findOne(directDebitFileId);
        }

        return this.directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        this.directDebitFile = directDebitFile;
    }

    //Jirra ACC-1036
    @Column(columnDefinition = "boolean default false")
    private Boolean isInitial = null;

    public Boolean getIsInitial() {
        return isInitial != null && isInitial;
    }

    public void setIsInitial(Boolean initial) {
        isInitial = initial;
    }

    public void setPaymentIsInitial(Boolean paymentIsInitial) {
        this.paymentIsInitial = paymentIsInitial;
    }

        //Jirra ACC-789
    @Transient
    @JsonIgnore
    private Attachment taxAttachment;

    public void setTaxAttachment(Attachment taxAttachment) {
        this.taxAttachment = taxAttachment;
    }

    public Attachment getTaxAttachment() {
        return this.taxAttachment;
    }

    public boolean isMatchedWithExpectedWireTransfer() {
        ExpectedWireTransferRepository expectedWireTransferRepository =
                Setup.getRepository(ExpectedWireTransferRepository.class);
        List<ExpectedWireTransfer> expectedWireTransfers =
                expectedWireTransferRepository.findByPaymentId(this.getId());
        return (expectedWireTransfers != null && expectedWireTransfers.size() > 0);
    }

    public Double getPaymentWithoutVATAED() {
        return (paymentWithoutVAT == null || paymentWithoutVAT == 0) ? amountOfPayment : paymentWithoutVAT;
    }

    public Boolean getIncludedInVatFix() {
        return includedInVatFix;
    }

    public void setIncludedInVatFix(Boolean includedInVatFix) {
        this.includedInVatFix = includedInVatFix;
    }

    public Boolean getReplacedMsgSent() {
        if (replacedMsgSent == null) {
            replacedMsgSent = false;
        }
        return replacedMsgSent;
    }

    public void setReplacedMsgSent(Boolean replacedMsgSent) {
        this.replacedMsgSent = replacedMsgSent;
    }

    public Boolean getBouncedMsgSent() {
        if (bouncedMsgSent == null) {
            bouncedMsgSent = false;
        }
        return bouncedMsgSent;
    }

    public void setBouncedMsgSent(Boolean bouncedMsgSent) {
        this.bouncedMsgSent = bouncedMsgSent;
    }

    public Boolean getRefundSentFromBF() {
        return refundSentFromBF;
    }

    public void setRefundSentFromBF(Boolean refundSentFromBF) {
        this.refundSentFromBF = refundSentFromBF;
    }

    public int getReplacmentsCount() {
        return replacmentsCount;
    }

    public void setReplacmentsCount(int replacmentsCount) {
        this.replacmentsCount = replacmentsCount;
    }

    public Boolean getVatFilledFromFrontEnd() {
        return vatFilledFromFrontEnd;
    }

    public void setVatFilledFromFrontEnd(Boolean vatFilledFromFrontEnd) {
        this.vatFilledFromFrontEnd = vatFilledFromFrontEnd;
    }

    public String getContractCancellationReason() { return contractCancellationReason; }

    public void setContractCancellationReason(String contractCancellationReason) {
        this.contractCancellationReason = contractCancellationReason;
    }

    public Boolean getAutomaticallyAdded() {
        return automaticallyAdded;
    }

    public void setAutomaticallyAdded(Boolean automaticallyAdded) {
        this.automaticallyAdded = automaticallyAdded;
    }

    public PaymentMethod getMethodOfPayment() {
        return methodOfPayment;
    }

    public void setMethodOfPayment(PaymentMethod methodOfPayment) {
        this.methodOfPayment = methodOfPayment;
    }

    public Double getVatPercent() {
        return vatPercent;
    }

    public void setVatPercent(Double vatPercent) {
        this.vatPercent = vatPercent;
    }

    public Double getVat() {
        return vat;
    }

    public void setVat(Double vat) {
        this.vat = Utils.roundDownMode(vat, 1);
    }

    public Double getPaymentWithoutVAT() {
        return paymentWithoutVAT;
    }

    public void setPaymentWithoutVAT(Double paymentWithoutVAT) {
        this.paymentWithoutVAT = paymentWithoutVAT;
    }

    public String getReasonOfDiscount() {
        return reasonOfDiscount;
    }

    public void setReasonOfDiscount(String reasonOfDiscount) {
        this.reasonOfDiscount = reasonOfDiscount;
    }

    public PicklistItem getBankName() {
        return bankName;
    }

    public void setBankName(PicklistItem bankName) {
        this.bankName = bankName;
    }

    public PicklistItem getTypeOfDiscount() {
        return typeOfDiscount;
    }

    public void setTypeOfDiscount(PicklistItem typeOfDiscount) {
        this.typeOfDiscount = typeOfDiscount;
    }

    public PicklistItem getTypeOfPayment() {
        return typeOfPayment;
    }

    public void setTypeOfPayment(PicklistItem typeOfPayment) {
        this.typeOfPayment = typeOfPayment;
    }

    public PicklistItem getSubType() { return subType; }

    public void setSubType(PicklistItem subType) { this.subType = subType; }

    public PicklistItem getReasonOfBouncingCheque() {
        return reasonOfBouncingCheque;
    }

    public Long getBouncedPaymentId() {
        return bouncedPaymentId;
    }

    public void setBouncedPaymentId(Long bouncedPaymentId) {
        if (Long.valueOf(bouncedPaymentId) == null) {
            this.bouncedPaymentId = 0l;
        }
        this.bouncedPaymentId = bouncedPaymentId;
    }

    public void setReasonOfBouncingCheque(PicklistItem reasonOfBouncingCheque) {
        this.reasonOfBouncingCheque = reasonOfBouncingCheque;
    }

    public PaymentStatus getStatus() {
        return status;
    }

    public void setStatus(PaymentStatus status) {
        this.status = status;
    }

    public Boolean getBouncedInCancellationWaitingPeriod() {
        return bouncedInCancellationWaitingPeriod != null && bouncedInCancellationWaitingPeriod;
    }

    public void setBouncedInCancellationWaitingPeriod(Boolean bouncedInCancellationWaitingPeriod) {
        this.bouncedInCancellationWaitingPeriod = bouncedInCancellationWaitingPeriod;
    }

    public Double getAmountOfPayment() {
        return amountOfPayment;
    }

    public void setAmountOfPayment(Double amountOfPayment) {
        this.amountOfPayment = amountOfPayment;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Boolean getRequiredForUnfitToWork() {
        return requiredForUnfitToWork != null && requiredForUnfitToWork;
    }

    public void setRequiredForUnfitToWork(Boolean requiredForUnfitToWork) {
        this.requiredForUnfitToWork = requiredForUnfitToWork;
    }

    public Boolean getRequiredForBouncing() {
        return requiredForBouncing != null && requiredForBouncing;
    }

    public void setRequiredForBouncing(Boolean requiredForBouncing) {
        this.requiredForBouncing = requiredForBouncing;
    }

    public String getChequeNumber() {
        return chequeNumber;
    }

    public void setChequeNumber(String chequeNumber) {
        this.chequeNumber = chequeNumber;
    }

    public String getChequeName() {
        return chequeName;
    }

    public void setChequeName(String chequeName) {
        this.chequeName = chequeName;
    }

    public Date getDateOfBouncing() {
        return dateOfBouncing;
    }

    public void setDateOfBouncing(Date dateOfBouncing) {
        this.dateOfBouncing = dateOfBouncing;
    }

    public Date getDateOfPayment() {
        return dateOfPayment;
    }

    public void setDateOfPayment(Date dateOfPayment) {
        this.dateOfPayment = dateOfPayment;
    }

    public Boolean getIsReplacement() {
        return isReplacement;
    }

    public void setIsReplacement(Boolean isReplacement) {
        this.isReplacement = isReplacement;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Boolean isPrepareToRefund() {
        return prepareToRefund;
    }

    public void setPrepareToRefund(Boolean prepareToRefund) {
        this.prepareToRefund = prepareToRefund;
    }

    public Boolean getBouncedFlowPausedForReplacement() {
        return bouncedFlowPausedForReplacement != null && bouncedFlowPausedForReplacement;
    }

    public void setBouncedFlowPausedForReplacement(Boolean bouncedFlowPausedForReplacement) {
        this.bouncedFlowPausedForReplacement = bouncedFlowPausedForReplacement;
    }

    public Boolean getBouncedAgainDuringPausingPeriod() {
        return bouncedAgainDuringPausingPeriod;
    }

    public void setBouncedAgainDuringPausingPeriod(Boolean bouncedAgainDuringPausingPeriod) {
        this.bouncedAgainDuringPausingPeriod = bouncedAgainDuringPausingPeriod;
    }

    public java.util.Date getBouncedFlowPausedFromDate() {
        return bouncedFlowPausedFromDate;
    }

    public void setBouncedFlowPausedFromDate(java.util.Date bouncedFlowPausedFromDate) {
        this.bouncedFlowPausedFromDate = bouncedFlowPausedFromDate;
    }

    public Boolean getReplaced() {
        return replaced;
    }

    public Boolean isReplaced() {
        return replaced != null && replaced;
    }

    public void setReplaced(Boolean replaced) {
        this.replaced = replaced;
    }

    public Payment getReplacementFor() {
        return replacementFor;
    }

    public void setReplacementFor(Payment replacementFor) {
        this.replacementFor = replacementFor;
    }

    public Boolean getChequeWithTheBank() {
        return chequeWithTheBank;
    }

    public void setChequeWithTheBank(Boolean chequeWithTheBank) {
        this.chequeWithTheBank = chequeWithTheBank;
    }

    public String getPoliceCaseNumber() {
        return policeCaseNumber;
    }

    public void setPoliceCaseNumber(String policeCaseNumber) {
        this.policeCaseNumber = policeCaseNumber;
    }

    public String getBusinessObjectId() {
        return businessObjectId;
    }

    public void setBusinessObjectId(String businessObjectId) {
        this.businessObjectId = businessObjectId;
    }

    public String getBusinessObjectType() {
        return businessObjectType;
    }

    public void setBusinessObjectType(String businessObjectType) {
        this.businessObjectType = businessObjectType;
    }

    public Boolean getIgnoreVAT() {
        return ignoreVAT;
    }

    public void setIgnoreVAT(Boolean ignoreVAT) {
        this.ignoreVAT = ignoreVAT;
    }

    public Boolean getVatPaidByClient() {
        return vatPaidByClient;
    }

    public void setVatPaidByClient(Boolean vatPaidByClient) {
        this.vatPaidByClient = vatPaidByClient;
    }

    public Boolean getIncludeWorkerSalary() {
        return includeWorkerSalary != null && includeWorkerSalary;
    }

    public void setIncludeWorkerSalary(Boolean includeWorkerSalary) {
        this.includeWorkerSalary = includeWorkerSalary;
    }

    public Double getVisaFees() {
        return visaFees;
    }

    public void setVisaFees(Double visaFees) {
        this.visaFees = visaFees;
    }

    public Double getVisaFeesWithoutVAT() {
        return visaFeesWithoutVAT;
    }

    public void setVisaFeesWithoutVAT(Double visaFeesWithoutVAT) {
        this.visaFeesWithoutVAT = visaFeesWithoutVAT;
    }

    public Boolean getRefundSentToExpensify() {
        return refundSentToExpensify != null && refundSentToExpensify;
    }

    public void setRefundSentToExpensify(Boolean refundSentToExpensify) {
        this.refundSentToExpensify = refundSentToExpensify;
    }

    public Boolean getFullyRefunded() {
        return fullyRefunded;
    }

    public void setFullyRefunded(Boolean fullyRefunded) {
        this.fullyRefunded = fullyRefunded;
    }

    //    public Boolean getToBeFullRefunded() {
//        return toBeFullRefunded;
//    }
//
//    public void setToBeFullRefunded(Boolean toBeFullRefunded) {
//        this.toBeFullRefunded = toBeFullRefunded;
//    }
//
    public Boolean getClientInformedAboutRefund() {
        return clientInformedAboutRefund;
    }

    public void setClientInformedAboutRefund(Boolean clientInformedAboutRefund) {
        this.clientInformedAboutRefund = clientInformedAboutRefund;
    }

    public Boolean getSentToBankByMDD() {
        return sentToBankByMDD != null && sentToBankByMDD;
    }

    public void setSentToBankByMDD(Boolean sentToBankByMDD) {
        this.sentToBankByMDD = sentToBankByMDD;
    }

    // ACC-1435
    @JsonIgnore
    public String getLabel() {
        return this.getDateOfPayment() + ", " + this.getAmountOfPayment();
    }

    //Jirra ACC-2282
    public static boolean hasPosition(Set<Position> positions, String position) {
        return positions != null
                && (positions.size() > 0)
                && (positions.stream().filter(x -> x.getCode().equalsIgnoreCase(position)).count() > 0);
    }

    @Transient
    private boolean locked;

    public void setLocked(boolean locked) {
        this.locked = locked;
    }

    //Jirra ACC-2282
    public boolean isLocked() {

        int paymentLockDate = Integer.valueOf(Setup.getParameter(Setup.getModule(AccountingModule.CLIENT_MGMT_MODULE_CODE),
                AccountingModule.PARAMETER_CLIENT_MGMT_RECEIVED_PAYMENT_LOCK_DAY));

        java.time.LocalDate currentDate = java.time.LocalDate.now();
        java.time.LocalDate firtDayOfMonth = java.time.LocalDate.now().withDayOfMonth(1);
        java.time.LocalDate endingDate = this.dateOfPayment.toLocalDate().plusMonths(1).withDayOfMonth(paymentLockDate);
        if (currentDate.isAfter(endingDate) && this.dateOfPayment.toLocalDate().isBefore(firtDayOfMonth)) {
            return true;
        }
        return false;
    }

    public java.util.Date getDateChangedToReceived() {
        return dateChangedToReceived;
    }

    public void setDateChangedToReceived(java.util.Date dateChangedToReceived) {
        this.dateChangedToReceived = dateChangedToReceived;
    }

    public Boolean getIsProRated() {
        return isProRated;
    }

    public void setIsProRated(Boolean isProRated) {
        this.isProRated = isProRated;
    }

    @JsonIgnore
    public ContractPayment getContractPayment() {
        List<ContractPayment> cps = Setup.getRepository(ContractPaymentRepository.class)
                .findMatchedContractPayment(
                        this.getContract(),
                        this.getMethodOfPayment(),
                        this.getTypeOfPayment(),
                        DateUtil.getDayStart(this.getDateOfPayment()),
                        DateUtil.getDayEnd(this.getDateOfPayment()),
                        this.getAmountOfPayment());
        
        if (cps == null || cps.isEmpty()) {
            return null;
        }
        return cps.get(0);
    }

    public java.util.Date getDateChangedToPDP() {
        return dateChangedToPDP;
    }

    public void setDateChangedToPDP(java.util.Date dateChangedToPDP) {
        this.dateChangedToPDP = dateChangedToPDP;
    }


    public void setMsgBrChecked(boolean msgBrChecked) {
        this.msgBrChecked = msgBrChecked;
    }

    public boolean getMsgBrChecked() {
        return msgBrChecked;
    }

    public void setMsgBr2Checked(boolean msgBr2Checked) {
        this.msgBr2Checked = msgBr2Checked;
    }

    public void setMsgBr3Checked(boolean msgBr3Checked) {
        this.msgBr3Checked = msgBr3Checked;
    }

    public boolean getMsgBr3Checked() {
        return msgBr3Checked;
    }

    public boolean getMsgBr2Checked() {
        return msgBr2Checked;
    }

    public boolean ispBF1BrChecked() {
        return pBF1BrChecked;
    }

    public void setpBF1BrChecked(boolean pBF1BrChecked) {
        this.pBF1BrChecked = pBF1BrChecked;
    }

    public boolean ispBF2BrChecked() {
        return pBF2BrChecked;
    }

    public void setpBF2BrChecked(boolean pBF2BrChecked) {
        this.pBF2BrChecked = pBF2BrChecked;
    }

    public boolean ispBF3BrChecked() {
        return pBF3BrChecked;
    }

    public void setpBF3BrChecked(boolean pBF3BrChecked) {
        this.pBF3BrChecked = pBF3BrChecked;
    }

    public boolean ispBF4BrChecked() {
        return pBF4BrChecked;
    }

    public void setpBF4BrChecked(boolean pBF4BrChecked) {
        this.pBF4BrChecked = pBF4BrChecked;
    }

    public boolean isrCTRBPBrChecked() {
        return rCTRBPBrChecked;
    }

    public void setrCTRBPBrChecked(boolean rCTRBPBrChecked) {
        this.rCTRBPBrChecked = rCTRBPBrChecked;
    }

    public Boolean getUpdatedFromBankStatement() {
        return updatedFromBankStatement;
    }

    public void setUpdatedFromBankStatement(Boolean updatedFromBankStatement) {
        this.updatedFromBankStatement = updatedFromBankStatement;
    }

    public boolean isIgnorePostingEngineBR() {
        return ignorePostingEngineBR;
    }

    public void setIgnorePostingEngineBR(boolean ignorePostingEngineBR) {
        this.ignorePostingEngineBR = ignorePostingEngineBR;
    }

    public Boolean getToBeRefundedPartially() {
        return toBeRefundedPartially;
    }

    public void setToBeRefundedPartially(Boolean toBeRefundedPartially) {
        this.toBeRefundedPartially = toBeRefundedPartially;
    }

    public Double getAmountToRefundPartially() {
        return amountToRefundPartially;
    }

    public void setAmountToRefundPartially(Double amountToRefundPartially) {
        this.amountToRefundPartially = amountToRefundPartially;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Long getRefundRequestId() {
        return refundRequestId;
    }

    public void setRefundRequestId(Long refundRequestId) {
        this.refundRequestId = refundRequestId;
    }

    public PaymentRequestPurpose getRefundPurpose() {
        return refundPurpose;
    }

    public void setRefundPurpose(PaymentRequestPurpose refundPurpose) {
        this.refundPurpose = refundPurpose;
    }

    public boolean ispCCBrChecked() {
        return pCCBrChecked;
    }

    public void setpCCBrChecked(boolean pCCBrChecked) {
        this.pCCBrChecked = pCCBrChecked;
    }

    public boolean istPEBrChecked() {
        return tPEBrChecked;
    }

    public void settPEBrChecked(boolean tPEBrChecked) {
        this.tPEBrChecked = tPEBrChecked;
    }

    public boolean ispRNRBrChecked() {
        return pRNRBrChecked;
    }

    public void setpRNRBrChecked(boolean pRNRBrChecked) {
        this.pRNRBrChecked = pRNRBrChecked;
    }

    public boolean issNBPBrChecked() {
        return sNBPBrChecked;
    }

    public void setsNBPBrChecked(boolean sNBPBrChecked) {
        this.sNBPBrChecked = sNBPBrChecked;
    }

    public boolean issBABPBrChecked() {
        return sBABPBrChecked;
    }

    public void setsBABPBrChecked(boolean sBABPBrChecked) {
        this.sBABPBrChecked = sBABPBrChecked;
    }

    public boolean isdPBrChecked() {
        return dPBrChecked;
    }

    public void setdPBrChecked(boolean dPBrChecked) {
        this.dPBrChecked = dPBrChecked;
    }

    public Boolean getBouncedLogAdded() {
        return bouncedLogAdded;
    }

    public void setBouncedLogAdded(Boolean bouncedLogAdded) {
        this.bouncedLogAdded = bouncedLogAdded;
    }

    public java.util.Date getContractScheduleDateOfTermination() {
        if (this.getContract() != null && this.getContract().getScheduledDateOfTermination() != null) {
            return this.getContract().getScheduledDateOfTermination();
        }
        return contractScheduleDateOfTermination;
    }

    public java.util.Date getPaymentScheduleDateOfTerminationValue() { return contractScheduleDateOfTermination; }

    public void setContractScheduleDateOfTermination(java.util.Date contractScheduleDateOfTermination) {
        this.contractScheduleDateOfTermination = contractScheduleDateOfTermination;
    }

    public boolean isCausedTermination() { return causedTermination; }

    public void setCausedTermination(boolean causedTermination) { this.causedTermination = causedTermination; }

    public Long getHumanSmsId() {
        return humanSmsId;
    }

    public void setHumanSmsId(Long humanSmsId) {
        this.humanSmsId = humanSmsId;
    }

    public Long getBankStatmentTransactionId() {
        return bankStatmentTransactionId;
    }

    public void setBankStatmentTransactionId(Long bankStatmentTransactionId) {
        this.bankStatmentTransactionId = bankStatmentTransactionId;
    }

    public ClientRefundToDo getClientRefundToDo() {
        return clientRefundToDo;
    }

    public void setClientRefundToDo(ClientRefundToDo clientRefundToDo) {
        this.clientRefundToDo = clientRefundToDo;
    }

    public String getOldApplicationId() {
        return oldApplicationId;
    }

    public void setOldApplicationId(String oldApplicationId) {
        this.oldApplicationId = oldApplicationId;
    }

    public boolean isSwitchingAccountActionTaken() {
        return switchingAccountActionTaken;
    }

    public void setSwitchingAccountActionTaken(boolean switchingAccountActionTaken) {
        this.switchingAccountActionTaken = switchingAccountActionTaken;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public Long getParentWrapperId() {
        return parentWrapperId;
    }

    public void setParentWrapperId(Long parentWrapperId) {
        this.parentWrapperId = parentWrapperId;
    }

    // ACC-2900
    @JsonIgnore
    public boolean isProPlusMonth() {
        return this.isProRated != null && this.isProRated &&
                this.contract != null &&
                this.contract.getProRatedPlusMonth() != null && this.contract.getProRatedPlusMonth();
    }

    public void copyRejectionAndBouncingPropsFrom(Payment payment) {
        this.trials = payment.getTrials();
        this.reminder = payment.getReminder();
        this.sentToBankByMDD = payment.getSentToBankByMDD();
    }

    @JsonIgnore
    public boolean isReplacedOrReceived() {
        boolean isIt = status.equals(PaymentStatus.RECEIVED) || BooleanUtils.toBoolean(replaced);
        logger.info("Replaced or Received: " + isIt);
        return isIt;
    }

    public Double getWorkerSalary() {
        return workerSalary;
    }

    public void setWorkerSalary(Double workerSalary) {
        this.workerSalary = workerSalary;
    }

    public java.util.Date getLastMessageDate() { return lastMessageDate; }

    public void setLastMessageDate(java.util.Date lastMessageDate) { this.lastMessageDate = lastMessageDate; }

    public Boolean getRecurring() { return recurring != null && recurring; }

    public void setRecurring(Boolean recurring) { this.recurring = recurring; }

    public boolean isPassStopConfirmationTodo() { return passStopConfirmationTodo; }

    public void setPassStopConfirmationTodo(boolean passStopConfirmationTodo) {
        this.passStopConfirmationTodo = passStopConfirmationTodo;
    }

    @Override
    public void addAttachment(Attachment attachment) {
        if (this.getId() != null) {
            logger.info("Payment: " + this.getId());
        } else if (this.getContract() != null && this.getContract().getId() != null) {
            logger.info("Contract: " + this.getContract().getId());
        }
        List<Attachment> attachments = this.getAttachments();
        attachments.add(attachment);
        this.setAttachments(attachments);

        // AttachmentBusinessRule
        Setup.getApplicationContext()
                .getBean(PaymentService.class)
                .addNewAttachmentToTransaction(this, attachment);
    }

    @Override
    public void setAttachments(List<Attachment> attachments) {
        if (this.getId() != null) {
            logger.info("Payment: " + this.getId());
        } else if (this.getContract() != null && this.getContract().getId() != null) {
            logger.info("Contract: " + this.getContract().getId());
        }
        super.setAttachments(attachments);
    }

    // From CM
    @JsonIgnore
    @Column(nullable = false)
    private Boolean isDeleted = false;

    @Transient
    private Boolean proRatedPlusMonth;

    //Jirra ACC-2503
    @Transient
    private Boolean rPBrChecked = false;

    public Boolean getrPBrChecked() {
        return rPBrChecked;
    }

    public void setrPBrChecked(Boolean rPBrChecked) {
        this.rPBrChecked = rPBrChecked;
    }

    public Date getLastLogDate(){
        if(bouncedPaymentLogs == null || bouncedPaymentLogs.isEmpty())
            return null;
        return bouncedPaymentLogs.get(0).getDateOfBouncing();
    }

    public Integer getBouncingTrials(){
        return bouncedPaymentLogs == null ? 0 : bouncedPaymentLogs.size();
    }

    @Column(columnDefinition = "boolean default false")
    private boolean sendNextPaymentReminder = false;

    @Column(columnDefinition = "boolean default false")
    private boolean nextPaymentReminderSent = false;

    public boolean isNextPaymentReminderSent() {
        return nextPaymentReminderSent;
    }

    public void setNextPaymentReminderSent(boolean nextPaymentReminderSent) {
        this.nextPaymentReminderSent = nextPaymentReminderSent;
    }

    public boolean isSendNextPaymentReminder() {
        return sendNextPaymentReminder;
    }

    public void setSendNextPaymentReminder(boolean sendNextPaymentReminder) {
        this.sendNextPaymentReminder = sendNextPaymentReminder;
    }

    public boolean isMsgBr3Checked() {
        return msgBr3Checked;
    }

    public Boolean getIsReplacedByPDP() {
        return isReplacedByPDP;
    }

    public void setIsReplacedByPDP(Boolean isReplacedByPDP) {
        this.isReplacedByPDP = isReplacedByPDP;
    }

    public void setProRatedPlusMonth(Boolean proRatedPlusMonth) {
        this.proRatedPlusMonth = proRatedPlusMonth;
    }

    public Boolean getProRatedPlusMonth() {
        return isProRated && !contract.getSpecialProRated() &&
                contract.getProRatedPlusMonth() != null &&
                contract.getProRatedPlusMonth();
    }

    @NotAudited
    @Formula("(EXISTS (select 1 from PAYMENTS p where p.STATUS = 'PDC' and p.REPLACEMENT_FOR_ID = ID))")
    private Boolean isReplacedByPDP;

    @Transient
    private Long bankStatementTransactionId;

    @Transient
    private Boolean overridePermissions;// ACC-4298

    @Column
    private Boolean affectsPaidEndDate;


    public Boolean getOverridePermissions() {
        return overridePermissions != null && overridePermissions;
    }

    public void setOverridePermissions(Boolean overridePermissions) {
        this.overridePermissions = overridePermissions;
    }


    public Boolean getAffectsPaidEndDate() {
        return affectsPaidEndDate;
    }

    public void setAffectsPaidEndDate(Boolean affectsPaidEndDate) {
        this.affectsPaidEndDate = affectsPaidEndDate;
    }

    @Column(columnDefinition = "boolean default false")
    private Boolean manuallyCreated = false;

    @NotAudited
    @Formula("(SELECT LAST_MODIFICATION_DATE FROM PAYMENTS_REVISIONS pr WHERE " +
            "pr.STATUS_MODIFIED AND pr.ID = ID " +
            "ORDER BY pr.LAST_MODIFICATION_DATE DESC LIMIT 1)")
    private Date statusLastModificationDate;

    public Date getStatusLastModificationDate() {
        return statusLastModificationDate;
    }

    public void setStatusLastModificationDate(Date statusLastModificationDate) {
        this.statusLastModificationDate = statusLastModificationDate;
    }

    public Boolean getManuallyCreated() {
        return manuallyCreated;
    }

    public void setManuallyCreated(Boolean manuallyCreated) {
        this.manuallyCreated = manuallyCreated;
    }

    public void setHumanSmsId(long humanSmsId) {
        this.humanSmsId = humanSmsId;
    }

    public Long getBankStatementTransactionId() {
        return bankStatementTransactionId;
    }

    public void setBankStatementTransactionId(Long bankStatementTransactionId) {
        this.bankStatementTransactionId = bankStatementTransactionId;
    }


    public Boolean getReplacedByPDP() {
        return isReplacedByPDP;
    }

    public void setReplacedByPDP(Boolean replacedByPDP) {
        isReplacedByPDP = replacedByPDP;
    }


    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }


    public boolean getPaymentIsInitial() {
        if (this.contract == null || this.contract.getId() == null)
            return false;
        Contract contract = Setup.getRepository(ContractRepository.class).findOne(this.contract.getId());

        boolean maidcc = contract.getContractProspectType().getCode().equals("maids.cc_prospect");
        boolean maidVisa = contract.getContractProspectType().getCode().equals("maidvisa.ae_prospect");
        boolean noAdjustedEndDate = contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE);
        ContractPaymentTerm contractPaymentTerm = getActiveContractPaymentTermByContrct(contract);
        if (contractPaymentTerm == null)
            return false;
        ContractPackageType packageType = contractPaymentTerm.getPackageType();

        boolean shortTerm = contract.getContractType() != null && contract.getContractType().equals(ContractType.SHORT_TERM);

        if (!this.getTypeOfPayment().getCode().equals("monthly_payment") || !noAdjustedEndDate)
            return false;
        else if (maidcc && shortTerm)
            return true;
        else if (maidcc && !shortTerm
                && (packageType == null || packageType.equals(ContractPackageType.NORMAL_LONG_TERM))) {
            int initialPaymentsCount = contractPaymentTerm.getDiscountEffectiveAfter();
            if (initialPaymentsCount == 0)
                return  false;
            initialPaymentsCount -= contract.getIsProRated() ? 0 : 1;
            initialPaymentsCount += contract.getDdGenerationNextMonth()?1:0;
            if(dateOfPayment.getTime()<= DateUtil.addMonths(contract.getStartOfContract(), initialPaymentsCount).getTime())
                return true;
            else
                return false;

        } else if (maidcc && !shortTerm &&
                (packageType.equals(ContractPackageType.PROBATION_PACKAGE)
                        || packageType.equals(ContractPackageType.TEMPORARY_PACKAGE)))
            return false;
        else if (maidVisa) {
            int initialPaymentsCount = contractPaymentTerm.getDiscountEffectiveAfter();
            if (initialPaymentsCount == 0)
                return  false;
            initialPaymentsCount -= contract.getIsProRated() ? 0 : 1;
            initialPaymentsCount += contract.getDdGenerationNextMonth()?1:0;
            if(dateOfPayment.getTime()<= DateUtil.addMonths(contract.getStartOfContract(), initialPaymentsCount).getTime())
                return true;
            else
                return false;

        } else return false;


    }

    @BeforeInsert
    public void copyWorkerSalary() {
        if (isDeepSilentSave()) return;
        ContractRepository repo = Setup.getRepository(ContractRepository.class);
        this.contract = repo.findOne(this.getContract().getId());
        if ((this.getWorkerSalary() == null || this.getWorkerSalary() == 0)
                && this.getContract() != null
                && this.getContract().getWorkerSalaryWithoutVat() != null)
            this.setWorkerSalary(getContract().getWorkerSalaryNew());
    }

    @BeforeUpdate
    @BeforeInsert
    public void vatTrigger() {
        if (isDeepSilentSave()) return;
        this.setTypeOfPayment(Setup.getRepository(PicklistItemRepository.class).findOne(this.getTypeOfPayment().getId()));
        this.setContract(Setup.getRepository(ContractRepository.class).findOne(this.getContract().getId()));

        //validateProRated();
        if(getIsInitial() == null) { this.setIsInitial(getPaymentIsInitial()); }
        updateDateChangedToPDP();
        updateDateChangedToReceived();
        Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .updateVatFields(this);
    }


    @AfterUpdate
    @AfterInsert
    public void receiptTrigger(){
        if (isDeepSilentSave()) return;
        addRecipt();
    }

    public void updateDateChangedToPDP(){
        if(this.status == PaymentStatus.PDC){
            if(this.getId() == null){
                this.setDateChangedToPDP(new java.util.Date());
            }else{
                Payment oldPayment = Setup.getRepository(PaymentRepository.class).findOne(this.getId());
                if(oldPayment.getDateChangedToPDP() == null)
                    this.setDateChangedToPDP(new java.util.Date());
            }
        }
    }

    public void updateDateChangedToReceived(){
        if(this.status == PaymentStatus.RECEIVED){
            if(this.getId() == null){
                this.setDateChangedToReceived(new java.util.Date());
            }else{
                Payment oldPayment = Setup.getRepository(PaymentRepository.class).findOne(this.getId());
                if(oldPayment.getDateChangedToReceived() == null)
                    this.setDateChangedToReceived(new java.util.Date());
            }
        } else {
            if(this.getId() != null) {
                Payment oldPayment = Setup.getRepository(PaymentRepository.class).findOne(this.getId());
                if (oldPayment.getDateChangedToReceived() != null)
                    this.setDateChangedToReceived(null);
            }
        }
    }

    @JsonIgnore
    public Map<String,String> getPaymentReceiptParameters(Attachment att) {
        if (att == null) {
            throw new BusinessException("Attachment null from getPaymentReceiptDownloadMsg");
        }
        Shortener sh = Setup.getApplicationContext().getBean(Shortener.class);
        String link = Setup.getParameter(Setup.getModule("accounting"), "backend_base_url") + "/public/download/" + att.getUuid();
        Map<String,String> parameters = new HashMap<>();
       //ACC-6103 SmsFormatter
        parameters.put("receiver_name", SmsFormatter.formatClientName(this.getContract().getClient()));
        //
        parameters.put("greetings", SmsFormatter.clientGreeting());
        parameters.put("link",(sh == null ? link : sh.shorten(link)));
        return parameters;
    }


    @AfterUpdate
    @AfterInsert
    private void sendMsg() {
        if (isDeepSilentSave()) return;

        if(this.getStatus()!=null && this.getStatus().equals(PaymentStatus.RECEIVED)
                && this.getPaymentIsReplaced() && this.getReplacementFor()!=null) {
            PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
            Payment replacedPayment = paymentRepository.findOne(replacementFor.getId());
            if(replacedPayment!=null){
                replacedPayment.setReplaced(true);
                Setup.getApplicationContext().getBean(PaymentService.class).forceUpdatePayment(replacedPayment);
            }
        }
    }

    private void addRecipt() {
        if (this.getActiveContractPaymentTermByContrct(this.contract) == null) {
            return;
        }
        BackgroundTaskService backgroundTaskService = Setup.getApplicationContext().getBean(BackgroundTaskService.class);
        Boolean conditionFulfiled = false;

        if ((!this.getTypeOfPayment().hasTag("NO_VAT"))
                && this.getStatus() == PaymentStatus.RECEIVED
                && this.getVatPaidByClient()
                && !hastaxInvoice()
                && !this.getTypeOfPayment().hasTag("refund")) {
            conditionFulfiled = true;
        }


        if (this.getStatus() == PaymentStatus.RECEIVED
                && this.getVatPaidByClient()
                && this.getTypeOfPayment() != null
                && !hasCreditNote()
                && this.getTypeOfPayment().hasTag("refund")) {
            conditionFulfiled = true;
        }
        if(conditionFulfiled) {
            try {
                Class<?>[] parametersTypes = {Long.class};
                backgroundTaskService.addDirectCallBackgroundTaskForEntity("createPaymentReceipt_" + getId(), "paymentBackgroundTasks", "clientmgmt", "addReceipt",
                        "Payment", getId(),10000L, true, false, BackgroundTaskQueues.SequentialQueue, parametersTypes, getId());

            } catch (Exception e) {
                logger.log(Level.SEVERE, "Error adding background task for receipt ", e);
                e.printStackTrace();
            }
        }
    }

    String getPackage(){
        ContractPaymentTerm cpt = this.getContract().getActiveContractPaymentTerm();
        if(cpt!=null && cpt.getPackageType() != null
                && (cpt.getPackageType().equals(ContractPackageType.PROBATION_PACKAGE)
                || cpt.getPackageType().equals(ContractPackageType.TEMPORARY_PACKAGE))){
            return cpt.getPackageType().getLabel()+" Fee";
        }else
            return "Flexible Package Fee";
    }

    public String getDescription() {
        PicklistItem maidcc = PicklistHelper.getItem("ProspectType", "maids.cc_prospect");
        PicklistItem maidvisa = PicklistHelper.getItem("ProspectType", "maidvisa.ae_prospect");
        if (this.getContract().getContractProspectType().getId().equals(maidcc.getId())) {
            switch (this.getTypeOfPayment().getCode()) {
                case "monthly_payment":
                    return getPackage();
                case "same_day_recruitment_fee":
                    return "Non-refundable employee settlement";
                case "accommodation_fee":
                    return "Company Accommodation Fee";
                case "day-to-day_extension":
                    return "Flexible Package Fee";
                case "deposit":
                    return "Deposit";
                case "employing_ex_dh":
                    return "Non-refundable employee settlement";
                case "service_charge":
                    return "Service Charge";

            }
        } else if (this.getContract().getContractProspectType().getId().equals(maidvisa.getId())) {
            switch (this.getTypeOfPayment().getCode()) {

                case "same_day_recruitment_fee":
                    return "Service Fee";
                case "accommodation_fee":
                    return "Company Accommodation Fee";
                case "day-to-day_extension":
                    return "Flexible Package Fee";
                case "deposit":
                    return "Deposit";
                case "employing_ex_dh":
                    return "Travel Fee";
                case "service_charge":
                    return "Service Charge";
            }
        }
        return "";
    }

    public boolean hastaxInvoice() {
        boolean flag = false;
        for (Attachment att : this.getAttachments()) {
            if (att == null || att.getTag() == null) {
                continue;
            }
            if (att.getTag().startsWith(FILE_TAG_PAYMENTS_TAX_INVOICE) || att.getTag().startsWith(FILE_TAG_PAYMENTS_TAX_INVOICE_DUPLICATE)) {
                flag = true;
            }
        }
        return flag;
    }

    public boolean hasCreditNote() {
        boolean flag = false;
        for (Attachment att : this.getAttachments()) {
            if (att == null || att.getTag() == null) {
                continue;
            }
            if (att.getTag().startsWith(FILE_TAG_PAYMENTS_TAX_CREDIT_NOTE)) {
                flag = true;
            }
        }
        return flag;
    }

    public boolean hasPaymentReceipt() {
        boolean flag = false;
        for (Attachment att : this.getAttachments()) {
            if (att == null || att.getTag() == null) {
                continue;
            }
            if (att.getTag().startsWith(FILE_TAG_PAYMENTS_RECEIPT)) {
                flag = true;
            }
        }
        return flag;
    }

    @Column(columnDefinition = "boolean default false")
    private Boolean adjustmentManagerNoteAdded = false;

    public Boolean getAdjustmentManagerNoteAdded() {
        return adjustmentManagerNoteAdded;
    }

    public void setAdjustmentManagerNoteAdded(Boolean adjustmentManagerNoteAdded) {
        this.adjustmentManagerNoteAdded = adjustmentManagerNoteAdded;
    }

    @BeforeInsert
    @BeforeUpdate
    public void addManagerNote(){
        if (isDeepSilentSave()) return;
        if( this.typeOfPayment != null && this.typeOfPayment.getCode() != null && this.getTypeOfPayment().getCode().equalsIgnoreCase("Filipina_Salary_Adjustment")
                && this.getStatus() != null && this.getStatus().equals(PaymentStatus.RECEIVED)
                && this.getAdjustmentManagerNoteAdded() != null && !this.getAdjustmentManagerNoteAdded()) {
            addHousemaidAdjustmentManagerNote();
            this.setAdjustmentManagerNoteAdded(true);
        }
    }

    void addHousemaidAdjustmentManagerNote(){
        if(this.getContract() !=null && this.getContract().getHousemaid()!=null) {
            Long housemaidId=this.getContract().getHousemaid().getId();
            Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                    .addDirectCallBackgroundTaskForEntity(
                            "Add Filipina Salary Adjustment for maid: "+housemaidId,
                            "managerNoteService", "payroll", "addFilipinaSalaryAdjustment",
                            null, null, false, false, new Class[]{Long.class, Double.class},
                            housemaidId, this.getAmountOfPayment());
        }
    }

    @JsonIgnore
    private ContractPaymentTerm getActiveContractPaymentTermByContrct(Contract c) {
        ContractPaymentTermRepository contractPaymentTermRepository = Setup.getRepository(ContractPaymentTermRepository.class);
        List<ContractPaymentTerm> contractPaymentTerms = contractPaymentTermRepository.findByContractAndIsActiveOrderByCreationDateDesc(this.getContract(), true);
        if (contractPaymentTerms.size() == 0)
            return null;
        return contractPaymentTerms.get(0);

    }

    public boolean getPaymentIsReplaced() {
        return isReplaced() ||
                Setup.getRepository(ContractPaymentRepository.class)
                        .existsByReplaceOfAndDirectDebitStatusNotIn(this,
                                Arrays.asList(
                                        DirectDebitStatus.REJECTED,
                                        DirectDebitStatus.CANCELED,
                                        DirectDebitStatus.PENDING_FOR_CANCELLATION));
    }

    @JsonIgnore
    public  static java.util.Date getPaymentShowDate(){
        String paymentShowDateStr =
                Setup.getParameter(Setup.getModule("clientmgmt"),
                        "PARAMETER_PAYMENTS_SHOW_DATE");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return dateFormat.parse(paymentShowDateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }


    public List<Attachment> getFilteredAttachments() {
        return Setup.getApplicationContext()
                .getBean(AttachmentService.class)
                .getFilteredAttachments(getAttachments());
    }

    @AfterUpdate
    private void updateClientRefundStatus(){
        if (isDeepSilentSave()) return;

        if(this.getTypeOfPayment() == null)return;
        if(Setup.getRepository(PicklistItemRepository.class).findOne(this.getTypeOfPayment().getId()).hasTag("refund"))return;

        Payment oldPayment = getLastPayment();
        if(oldPayment == null)return;

        boolean ok = oldPayment.getStatus() != null && !Arrays.asList(PaymentStatus.CANCELLED, PaymentStatus.DELETED).contains(oldPayment.getStatus())
                && this.getStatus() != null &&  Arrays.asList(PaymentStatus.CANCELLED, PaymentStatus.DELETED).contains(this.getStatus());

        if(ok){
            ClientRefundTodoRepository clientRefundTodoRepo = Setup.getRepository(ClientRefundTodoRepository.class);
            List<ClientRefundToDo> clientRefundToDos = clientRefundTodoRepo.findByConditionalRefundAndRequiredPayment(this);
            for (ClientRefundToDo clientRefundToDo : clientRefundToDos) {
                logger.info("clientRefundToDo id " + clientRefundToDo.getId());
                if(clientRefundToDo.getStatus()!=null && clientRefundToDo.getStatus().equals(ClientRefundStatus.REJECTED))
                    continue;
                clientRefundToDo.setStatus(ClientRefundStatus.REJECTED);
                clientRefundToDo.setRefundRejectReason(PicklistHelper.getItem("refund_reject_reason",
                        "condition_not_met"));
                clientRefundTodoRepo.save(clientRefundToDo);
            }

        }
    }

    @JsonIgnore
    private Payment getLastPayment(){

        HistorySelectQuery<Payment> historyQuery = new HistorySelectQuery<>(Payment.class);
        historyQuery.filterBy("id", "=", this.getId());
        historyQuery.filterByChanged("status");
        historyQuery.sortBy("lastModificationDate", false, true);
        historyQuery.setLimit(1);
        List<Payment> payments = historyQuery.execute();
        return !payments.isEmpty() ? payments.get(0) : null;

    }

    @JsonIgnore
    public static SelectQuery<Payment> buildPaymentQuery(Contract contract, SelectFilter filters , Boolean includeDeleted){
        SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
        SelectFilter filter1 = new SelectFilter("contract","=",contract);
        query.filterBy(filter1);
        if (includeDeleted != null && !includeDeleted)
            query.filterBy("status","!=",PaymentStatus.DELETED);
        query.sortBy("dateOfPayment", false);
        java.util.Date paymentShowDate = Payment.getPaymentShowDate();
        if(!hasPMasterPosition() && paymentShowDate!=null)
            query.filterBy("dateOfPayment",">=",paymentShowDate);

        query.filterBy(filters);
        return query;
    }

    public static boolean hasPMasterPosition() {

        User loggedUser = CurrentRequest.getUser();
        if (loggedUser == null)
            return true;

        Set<Position> positions = loggedUser.getPositions();

        return positions != null
                && (positions.size() > 0)
                && (positions.stream().filter(x -> x.getCode().equalsIgnoreCase("p_master")).count() > 0);

    }

    @AfterDelete
    private void fixAdjustedEndDateTriggerAfterDeletion() throws Exception {
        if (isDeepSilentSave()) return;

        PicklistItem typeOfPaymentItem = Setup.getRepository(PicklistItemRepository.class).findOne(this.getTypeOfPayment().getId());
        this.setTypeOfPayment(typeOfPaymentItem);
        this.contract = Setup.getRepository(ContractRepository.class).findOne(contract.getId());
        Boolean notFixAdjustedEndDate = (Boolean) CurrentRequest.getPropertyFromCurrentOperation("notFixAdjustedEndDate", Boolean.class);
        if(notFixAdjustedEndDate!=null && notFixAdjustedEndDate){
            return;
        }
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .fixAdjustedEndDate(contract, false);
    }

    @AfterUpdate
    @AfterInsert
    private void fixAdjustedEndDateTrigger() throws Exception {
        logger.info("Payment Id: " + (getId() == null ? "NULL" : getId()));
        logger.info("isDeepSilentSave: " + isDeepSilentSave());
        if (isDeepSilentSave()) return;

        logger.info("fixAdjustedEndDateTrigger started");
        PicklistItem typeOfPaymentItem = Setup.getRepository(PicklistItemRepository.class).findOne(this.getTypeOfPayment().getId());
        this.setTypeOfPayment(typeOfPaymentItem);
        this.contract = Setup.getRepository(ContractRepository.class).findOne(contract.getId());
        Boolean notFixAdjustedEndDate = (Boolean) CurrentRequest.getPropertyFromCurrentOperation("notFixAdjustedEndDate", Boolean.class);
        boolean removeOMAFlag = shouldUpdateContractAfterProRatedPayment();
        logger.info("contractShouldBeUpdated = " + removeOMAFlag);
        if (notFixAdjustedEndDate != null && notFixAdjustedEndDate){
            if (removeOMAFlag) {
                Setup.getRepository(ContractRepository.class).saveAndFlush(contract);
            }
            return;
        }
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .fixAdjustedEndDate(contract, removeOMAFlag);
    }

    @JsonIgnore
    public boolean shouldUpdateContractAfterProRatedPayment(){
        logger.info("pro rated = " + this.isProRated);
        logger.info("status = " + this.status);

        if(this.getIsProRated() != null && this.getIsProRated() &&
                this.getStatus() != null && this.getStatus().equals(PaymentStatus.RECEIVED) &&
                this.contract != null){

            contract.setIsProRated(Boolean.TRUE);
            if(contract.isOneMonthAgreement()){
                contract.setOneMonthAgreement(false);
            }
            return true;
        }
        return false;
    }

    @AfterInsert
    private void paymentTriggersS() {

        Setup.getApplicationContext()
                .getBean(PaymentTriggersService.class)
                .apply(this, BusinessEvent.AfterCreate);
    }

    @AfterUpdate
    private void afterUpdate() {

        Setup.getApplicationContext()
                .getBean(PaymentTriggersService.class)
                .apply(this, BusinessEvent.AfterUpdate);
    }

    public Double getWorkerSalaryVAT() { return workerSalaryVAT == null ? 0.0 : workerSalaryVAT; }

    public void setWorkerSalaryVAT(Double workerSalaryVAT) { this.workerSalaryVAT = Utils.roundDownMode(workerSalaryVAT, 1); }

    public Double getVisaFeesVAT() { return visaFeesVAT == null ? 0.0 : visaFeesVAT; }

    public void setVisaFeesVAT(Double visaFeesVAT) { this.visaFeesVAT = Utils.roundDownMode(visaFeesVAT, 1); }

    public Double getWorkerSalaryWithoutVAT() { return workerSalaryWithoutVAT == null ? 0.0 : workerSalaryWithoutVAT; }

    public void setWorkerSalaryWithoutVAT(Double workerSalaryWithoutVAT) {
        this.workerSalaryWithoutVAT = workerSalaryWithoutVAT;
    }
    public Long getContractPaymentId() { return contractPaymentId; }

    public void setContractPaymentId(Long contractPaymentId) { this.contractPaymentId = contractPaymentId; }

    public boolean isDeepSilentSave() { return deepSilentSave; }

    public void setDeepSilentSave(boolean deepSilentSave) { this.deepSilentSave = deepSilentSave; }
}