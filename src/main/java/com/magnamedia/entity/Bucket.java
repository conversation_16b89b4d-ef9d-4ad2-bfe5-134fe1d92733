package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.IdLabelCodeOwner;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.BucketIdLabelCodeTypeSerializer;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import com.magnamedia.mastersearch.Searchable;
import com.magnamedia.mastersearch.SearchableField;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.module.type.WealthBucketType;

import javax.persistence.*;
import java.util.Set;

/**
 *
 *
 */
@Entity
@Searchable(showunName = "Bucket", order = 5, permissionCode = "ManageBuckets")
public class Bucket extends BaseEntity implements IdLabelCodeOwner {

    @Label
    @SearchableField(headerName = "Code", order = 1)
    private String code;

    @Column
    @SearchableField(headerName = "Name", order = 2)
    private String name;

    @Column
    private Boolean isActive = true;

    @Column
    private String holderEmail;

    @Column
    private Double initialBalance = 0.0;

    @Column
    private Double balance = 0.0;

    @Column
    private Double chequesNotClearedAmount = 0.0;

    @Column
    private Double minLimit = 0.0;

    //Jirra ACC-1070
    @Enumerated(EnumType.STRING)
    private WealthBucketType wealthBucketType;

    //Jira ACC-2832
    @Column
    private String cardNumber;

    //ACC-2841
    @Transient
    public String nameWithCardNumber;

    //acc-2988
    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    private User holder;

    @Enumerated(EnumType.STRING)
    private BucketType bucketType;


    @Column
    private Boolean autoReplenishment;

    @Column
    private Double replenishmentLevel;

    @Column
    private Double LevelAfterReplenishment;

    @ManyToOne
    @JoinColumn(name = "refillerBucket")
    @JsonSerialize(using = BucketIdLabelCodeTypeSerializer.class)
    private Bucket refillerBucket;

    @JsonIgnore
    @OneToMany(mappedBy = "refillerBucket")
    @JsonSerialize(using = IdLabelListSerializer.class)
    private Set<Bucket> filledBuckets;

    @Column(columnDefinition = "double default 0")
    private Double authorizedBalance = 0D;

    @Column
    private String nameInStatement;

    @Column
    private Boolean transGuardService = false;

    @Column(columnDefinition = "boolean default false")
    private boolean transGuard = false;

    @Column
    private Double transGuardCeilingAmount;

    // ACC-4662
    @Column
    private Boolean isWallet = false;

    public void setIsWallet(Boolean isWallet){
         this.isWallet = isWallet;
    }

    public Boolean getIsWallet(){
        return isWallet;
    }

    @Column
    private Boolean isSupplierNameIgnored = false;

    @Column(columnDefinition = "boolean default false")
    private boolean isSecure;

    public Boolean getSupplierNameIgnored() {
        return isSupplierNameIgnored != null && isSupplierNameIgnored;
    }

    public void setSupplierNameIgnored(Boolean supplierNameIgnored) {
        isSupplierNameIgnored = supplierNameIgnored;
    }

    public Boolean getTransGuardService() {
        if (transGuardService == null)
            return false;
        return transGuardService;
    }

    public void setTransGuardService(Boolean transGuardService) {
        this.transGuardService = transGuardService;
    }

    public boolean isTransGuard() { return transGuard; }

    public void setTransGuard(boolean transGuard) { this.transGuard = transGuard; }

    public Double getTransGuardCeilingAmount() { return transGuardCeilingAmount; }

    public void setTransGuardCeilingAmount(Double transGuardCeilingAmount) {
        this.transGuardCeilingAmount = transGuardCeilingAmount;
    }

    public String getNameInStatement() {
        return nameInStatement;
    }

    public void setNameInStatement(String nameInStatement) {
        this.nameInStatement = nameInStatement;
    }

    public Double getAuthorizedBalance() {
        return authorizedBalance;
    }

    public void setAuthorizedBalance(Double authorizedBalance) {
        this.authorizedBalance = authorizedBalance;
    }

    public User getHolder() {
        return holder;
    }

    public void setHolder(User holder) {
        this.holder = holder;
    }

    public BucketType getBucketType() {
        return bucketType;
    }

    public void setBucketType(BucketType bucketType) {
        this.bucketType = bucketType;
    }

    public Boolean getAutoReplenishment() {
        return autoReplenishment != null && autoReplenishment;
    }

    public void setAutoReplenishment(Boolean autoReplenishment) {
        this.autoReplenishment = autoReplenishment;
    }

    public Double getReplenishmentLevel() {
        return replenishmentLevel;
    }

    public void setReplenishmentLevel(Double replenishmentLevel) {
        this.replenishmentLevel = replenishmentLevel;
    }

    public Double getLevelAfterReplenishment() {
        return LevelAfterReplenishment;
    }

    public void setLevelAfterReplenishment(Double LevelAfterReplenishment) {
        this.LevelAfterReplenishment = LevelAfterReplenishment;
    }

    public Bucket getRefillerBucket() {
        return refillerBucket;
    }

    public void setRefillerBucket(Bucket refillerBucket) {
        this.refillerBucket = refillerBucket;
    }

    public Set<Bucket> getFilledBuckets() {
        return filledBuckets;
    }

    public void setFilledBuckets(Set<Bucket> filledBuckets) {
        this.filledBuckets = filledBuckets;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean isActive() {
        return isActive;
    }

    public void setActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getHolderEmail() {
        return holderEmail;
    }

    public void setHolderEmail(String holderEmail) {
        this.holderEmail = holderEmail;
    }

    public Double getInitialBalance() {
        return initialBalance == null ? 0.0 : initialBalance;
    }

    public void setInitialBalance(Double initialBalance) {
        this.initialBalance = initialBalance;
    }

    public void setBalance(Double balance) {
        this.balance = balance == null ? null : (double) Math.round(balance * 100) / 100;
    }

    public Double getBalance() {
        return balance == null ? 0.0 : balance;
    }

    public Double getMinLimit() {
        return minLimit;
    }

    public void setMinLimit(Double minLimit) {
        this.minLimit = minLimit;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Double getChequesNotClearedAmount() {
        return chequesNotClearedAmount;
    }

    public void setChequesNotClearedAmount(Double chequesNotClearedAmount) {
        this.chequesNotClearedAmount = chequesNotClearedAmount;
    }

    public WealthBucketType getWealthBucketType() {
        return wealthBucketType;
    }

    public void setWealthBucketType(WealthBucketType wealthBucketType) {
        this.wealthBucketType = wealthBucketType;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    //ACC-2841
    public String getNameWithCardNumber() {
        String nameWithCardNumber = this.getName();
        if (this.getCardNumber() != null) {
            nameWithCardNumber = nameWithCardNumber.concat(" (").concat(this.getCardNumber()).concat(")");
        }
        return nameWithCardNumber;
    }

    public boolean getIsSecure() { return isSecure; }

    public void setIsSecure(boolean secure) { isSecure = secure; }
}
