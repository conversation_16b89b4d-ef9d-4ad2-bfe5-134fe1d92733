package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;

import javax.persistence.*;

@Entity
public class PaymentTypeDescription extends BaseEntity {

    public enum AdditionalCondition {
        PRO_RATED,
        WITH_WORKER_SALARY
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem paymentType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem paymentSubType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem contractProspectType;

    @Column
    private String description;

    @Enumerated(EnumType.STRING)
    @Column
    private AdditionalCondition additionalCondition;

    public PicklistItem getPaymentType() { return paymentType; }

    public void setPaymentType(PicklistItem paymentType) { this.paymentType = paymentType; }

    public PicklistItem getPaymentSubType() { return paymentSubType; }

    public void setPaymentSubType(PicklistItem paymentSubType) { this.paymentSubType = paymentSubType; }

    public PicklistItem getContractProspectType() { return contractProspectType; }

    public void setContractProspectType(PicklistItem contractProspectType) { this.contractProspectType = contractProspectType; }

    public String getDescription() { return description; }

    public void setDescription(String description) { this.description = description; }

    public AdditionalCondition getAdditionalCondition() { return additionalCondition; }

    public void setAdditionalCondition(AdditionalCondition additionalCondition) { this.additionalCondition = additionalCondition; }
}