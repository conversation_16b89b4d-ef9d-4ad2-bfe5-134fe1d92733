///*
// * To change this license header, choose License Headers in Project Properties.
// * To change this template file, choose Tools | Templates
// * and open the template in the editor.
// */
//package com.magnamedia.entity;
//
//import com.magnamedia.core.entity.BaseEntity;
//import javax.persistence.Entity;
//import java.util.Date;
//import javax.persistence.Lob;
//
///**
// *
// * <AUTHOR>
// */
//@Entity
//public class TransactionHistory extends BaseEntity {
//
//    String name;
//    //  String editedBy;
//    Date editTime;
//    String filedEvent;
//    String oldValue;
//    String newValue;
//    Boolean chequesNotClearedAmount;
//    Double transactionAmount;
//    String operationType;
//    @Lob
//    String note;
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
////    public String getEditedBy() {
////        return editedBy;
////    }
////
////    public void setEditedBy(String editedBy) {
////        this.editedBy = editedBy;
////    }
//    public Date getEditTime() {
//        return editTime;
//    }
//
//    public void setEditTime(Date editTime) {
//        this.editTime = editTime;
//    }
//
//    public String getFiledEvent() {
//        return filedEvent;
//    }
//
//    public void setFiledEvent(String filedEvent) {
//        this.filedEvent = filedEvent;
//    }
//
//    public String getOldValue() {
//        return oldValue;
//    }
//
//    public void setOldValue(String oldValue) {
//        this.oldValue = oldValue;
//    }
//
//    public String getNewValue() {
//        return newValue;
//    }
//
//    public void setNewValue(String newValue) {
//        this.newValue = newValue;
//    }
//
//    public Boolean getChequesNotClearedAmount() {
//        return chequesNotClearedAmount;
//    }
//
//    public void setChequesNotClearedAmount(Boolean chequesNotClearedAmount) {
//        this.chequesNotClearedAmount = chequesNotClearedAmount;
//    }
//
//    public Double getTransactionAmount() {
//        return transactionAmount;
//    }
//
//    public void setTransactionAmount(Double transactionAmount) {
//        this.transactionAmount = transactionAmount;
//    }
//
//    public String getOperationType() {
//        return operationType;
//    }
//
//    public void setOperationType(String operationType) {
//        this.operationType = operationType;
//    }
//
//    public String getNote() {
//        return note;
//    }
//
//    public void setNote(String note) {
//        this.note = note;
//    }
//
//}
