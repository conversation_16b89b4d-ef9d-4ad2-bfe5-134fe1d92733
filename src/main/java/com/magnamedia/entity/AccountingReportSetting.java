package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.*;


@Entity
public class AccountingReportSetting extends BaseEntity {

    public enum ReportCategory {
        DDS_AND_COLLECTION_COMPLIANCE_EXCEPTIONS,
        FAILED_BACKGROUND_TASKS
    }

    public enum QueryCategory {
        // Start DDS_AND_COLLECTION_COMPLIANCE_EXCEPTIONS
        CLIENT_IS_MISSING_DDS,
        WRONG_INCOMPLETE_DDS,
        IPAM_FLOW_NOT_TRIGGERED,
        IPAM_FLOW_NOT_SENDING_MESSAGES,
        IPAM_FLOW_DID_NOT_STOP,
        CLIENT_SHOULD_BE_PAYING_VIA_CC,
        CLIENT_PAYING_VIA_CC_BUT_NO_MESSAGES,
        MISSING_DDB_DDA_PAYMENTS,
        CONTRACT_WITH_DUPLICATED_DDBS,
        ERP_FAILED_TO_GENERATE_POSTPONED_DDS,
        REFUND_FOR_A_NON_RECEIVED_PAYMENT,
        CLIENT_WILL_PAY_TWICE,
        SKIPPED_PAYMENT,
        WRONG_TERMINATION_BY_SYSTEM,
        WRONG_TERMINATION_BY_SYSTEM_CLIENT_PAID,
        INCOMPLETE_FLOW_WITH_NO_MESSAGES,
        CLIENT_WITH_NO_WAY_TO_PAY,
        // End DDS_AND_COLLECTION_COMPLIANCE_EXCEPTIONS
        FAILED_BACKGROUND_TASKS
    }

    @Column
    @Enumerated(EnumType.STRING)
    private ReportCategory reportCategory;

    @Column
    @Enumerated(EnumType.STRING)
    private QueryCategory queryCategory;

    @Column
    @Lob
    private String query;

    @Column(columnDefinition = "boolean default true")
    private boolean active = true;

    public ReportCategory getReportCategory() { return reportCategory; }

    public void setReportCategory(ReportCategory reportCategory) { this.reportCategory = reportCategory; }

    public QueryCategory getQueryCategory() { return queryCategory; }

    public void setQueryCategory(QueryCategory queryCategory) { this.queryCategory = queryCategory; }

    public String getQuery() { return query; }

    public void setQuery(String query) { this.query = query; }

    public boolean isActive() { return active; }

    public void setActive(boolean active) { this.active = active; }
}
