package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Housemaid;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Sep 26, 2020
 */
public interface ContractProjection {

    Long getId();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getContractProspectType();

    Boolean getIsScheduledForTermination();

    @Value("#{T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"contractService\").getLastAttachedHousemaid(target)}")
    @JsonSerialize(using = IdLabelSerializer.class)
    Housemaid getHousemaid();
}
