package com.magnamedia.entity.projection;

import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

public interface ExpenseRequestSearchCsvProjection {
    /* ───── Columns exactly as shown in the grid ───── */

    /* ─── UI column: Expense ─── */
    @Value("#{target.getExpenseCaption()}")                // ex.caption
    String getExpense();

    /* ─── UI column: Transaction ─── */
    @Value("#{target.getPaymentTransactionId()}")          // ep.transaction.id
    String getTransaction();

    /* ─── UI column: Related To ─── */
    @Value("#{target.getRelatedToInfo()}")                 // relatedToInfo
    String getRelatedTo();

    /* ─── UI column: Beneficiary ─── */
    @Value("#{target.getBenefeciaryInfo()}")               // benefeciaryInfo
    String getBeneficiary();

    /* ─── UI column: Amount (masking already applied in DTO getter) ─── */
    String getAmount();

    /* ─── UI column: Loan Amount ─── */
    @Value("#{target.getLoanAmount()}")                    // loanAmount
    Double getLoanAmount();

    /* ─── UI column: Description ─── */
    String getDescription();                               // description (masking already applied in DTO getter)

    /* ─── UI column: Payment Method ─── */
    ExpensePaymentMethod getPaymentMethod();

    /* ─── UI column: Bucket From ─── */
    @Value("#{target.getBucketName()}")                    // bucketName
    String getBucketFrom();

    /* ─── UI column: Status ─── */
    ExpenseRequestStatus getStatus();

    /* ─── UI column: Pending For Approval ─── */
    String getPendingForApproval();

    /* ─── UI column: Requested By ─── */
    @Value("#{target.getRequestedByName()}")               // requestedByName
    String getRequestedBy();

    /* ─── UI column: Approved By ─── */
    String getApprovedBy();

    /* ─── UI column: Date ─── */
    @Value("#{target.getCreationDate()}")                  // creationDate
    Date getDate();

    /* ─── UI column: Attachment (semicolon-separated file names) ─── */
    @Value("#{(target.getAttachments() != null && !target.getAttachments().isEmpty()) ? "
            + "T(java.lang.String).join('; ', target.getAttachments().![get('name')]) "
            + ": ''}")
    String getAttachment();
}
