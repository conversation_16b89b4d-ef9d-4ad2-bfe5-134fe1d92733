/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;


import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Jan 26, 2019
 */
public interface PLComapanyProjection {
    Long getId();

    String getLabel();

    String getName();

    @Value("#{target.getCompany() != null ? target.getCompany().isIsActive() : false}")
    boolean isIsActive();
}
