package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Mar 15, 2018
 */
public interface DEWAProjection {

    @Value("#{(target.getHousemaid()==null?null:target.getHousemaid().getName())}")
    String getHousemaidName();
    @Value("#{(target.getHousemaid()==null?null:(target.getHousemaid().getVisaNewRequest()==null?null:target.getHousemaid().getVisaNewRequest().getMolCardNumber()))}")
    String getHousemaidMol();
    @Value("#{(target.getClient().getName())}")
    String getClientName();
    @Value("#{(target.getClient().getDewaNumber())}")
    String getClientDewa();
    @Value("#{(target.getLiving())}")
    String getLiving();
    
}
