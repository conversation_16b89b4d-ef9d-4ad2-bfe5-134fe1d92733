package com.magnamedia.entity.projection;


import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Apr 26, 2020
 */

public interface UnmatchedDDFromErpProjection {

    Long getId();

    Date getDateOfPayment();

    Double getAmountOfPayment();

    @Value("#{target.getDirectDebitFile() != null ? "
            + "target.getDirectDebitFile().getApplicationId() "
            + ": null}")
    String getApplicationId();

    @Value("#{target.getContract() != null ? "
            + "target.getContract().getId() "
            + ": null}")
    String getContractId();

    @Value("#{target.getDirectDebitFile() != null ? "
            + "target.getDirectDebitFile().getId() "
            + ": null}")
    String getDirectDebitFileId();

    @Value("#{target.getContract() != null ? "
            + "target.getContract().getClient().getName() "
            + ": null}")
    String getClientName();

    @Value("#{target.getContract() != null && target.getContract().getClient() != null ? "
            + "target.getContract().getClient().getId() "
            + ": null}")
    String getClientId();

}