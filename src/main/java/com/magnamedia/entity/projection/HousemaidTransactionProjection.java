package com.magnamedia.entity.projection;

import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Apr 18, 2020
 */
public interface HousemaidTransactionProjection {
    
    @Value("#{target.getTransaction() != null ? {"
            + "id: target.getTransaction().getId(), "
            + "pnlValueDate: target.getTransaction().getPnlValueDate(), "
            + "fromBucket:{(target.getTransaction().getFromBucket()!=null)?{id:target.getTransaction().getFromBucket().getId(), name:target.getTransaction().getFromBucket().getName(), code:target.getTransaction().getFromBucket().getCode()}:NULL}, "
            + "revenue:{(target.getTransaction().getRevenue()!=null)?{id:target.getTransaction().getRevenue().getId(), name:target.getTransaction().getRevenue().getName(), code:target.getTransaction().getRevenue().getCode()}:NULL}, "
            + "expense:{(target.getTransaction().getExpense()!=null)?{id:target.getTransaction().getExpense().getId(), name:target.getTransaction().getExpense().getNameLabel(), code:target.getTransaction().getExpense().getCodeLabel()}:NULL}, "
            + "toBucket:{(target.getTransaction().getToBucket()!=null)?{id:target.getTransaction().getToBucket().getId(), name:target.getTransaction().getToBucket().getName(), code:target.getTransaction().getToBucket().getCode()}:NULL}, "
            + "description:target.getTransaction().getDescription(), "
            + "paymentType:target.getTransaction().getPaymentType(), "
            + "amount:target.getTransaction().getAmount(), "
            + "date:target.getTransaction().getDate(), "
            + "creationDate:target.getTransaction().getCreationDate(), "
            + "previouslyUnknown:target.getTransaction().getPreviouslyUnknown(), "
            + "transactionType:target.getTransaction().getTransactionType(), "
            + "sales:target.getTransaction().getSales(), "
            + "prospects:target.getTransaction().getProspects(), "
            + "housemaids:target.getTransaction().getHousemaids(), "
            + "contracts:target.getTransaction().getContracts(), "
            + "officeStaffs:target.getTransaction().getOfficeStaffs(), "
            + "freedomOperators:target.getTransaction().getFreedomOperators(), "
            + "isDescriptionSecured:target.getTransaction().getIsDescriptionSecured(), "
            + "cancelRequestExpense:target.getTransaction().getCancelRequestExpense(), "
            + "newRequestExpense:target.getTransaction().getNewRequestExpense(), "
            + "repeatEIDRequestExpense:target.getTransaction().getRepeatEIDRequestExpense(), "
            + "renewRequestExpense:target.getTransaction().getRenewRequestExpense(), "
            + "visaExpenseName:target.getTransaction().getVisaExpenseName(), "
            + "attachments:target.getTransaction().getAttachments(), "
            + "vatType:target.getTransaction().getVatType(), "
            + "vatAmount:target.getTransaction().getVatAmount(), "
            + "license:target.getTransaction().getLicense(), "
            + "contractId:target.getContractId() "
            + "} "
            + ": NULL}")
    public Map<?, ?> getTransaction();

}
