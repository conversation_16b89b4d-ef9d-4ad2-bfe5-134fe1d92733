package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.Attachment;
import com.magnamedia.module.type.DirectDebitFileStatus;
import com.magnamedia.module.type.DirectDebitRejectCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 08, 2020
 * Jirra ACC-1435
 */
public interface DDFSearchProjection {

    Long getId();

    @Value("#{target.getSecuredAttachments()}")
    List<Attachment> getAttachments();

    DirectDebitStatus getDdStatus();

    String getAccountName();

    DirectDebitFileStatus getStatus();

    @Value("#{target.getDirectDebit() != null ? "
            + "{id: target.getDirectDebit().getId(), "
            + "additionalDiscount: target.getDirectDebit().getAdditionalDiscount(), "
            + "paymentType: target.getDirectDebit().getPaymentType() != null ? "
            + "{id: target.getDirectDebit().getPaymentType().getId(), "
            + "name: target.getDirectDebit().getPaymentType().getName()} : null, "
            + "paymentsCount: target.getDirectDebit().getPaymentsCount(), "
            + "paymentMethod: target.getDirectDebit().getPaymentMethod(), "
            + "attachments: target.getDirectDebit().getAttachments(), "
            + "additionalDiscountNotes: target.getDirectDebit().getAdditionalDiscountNotes(), "
            + "additionalDiscountAttachment: target.getDirectDebit().getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') != null ? "
            + "target.getDirectDebit().getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT') : "
            + "(target.getDirectDebit().getAdditionalDiscount() != null && target.getDirectDebit().getAdditionalDiscount() != 0 "
            + "&& target.getDirectDebit().getContractPaymentTerm() != null ? target.getDirectDebit().getContractPaymentTerm().getAttachment('ADDITIONAL_DISCOUNT_ATTACHMENT'): null), "
            + "contractPaymentTerm: target.getDirectDebit().getContractPaymentTerm() != null ? "
            + "{id: target.getDirectDebit().getContractPaymentTerm().getId(), "
            + "isActive:target.getDirectDebit().getContractPaymentTerm().isIsActive(), "
            + "attachments: target.getDirectDebit().getContractPaymentTerm().getAttachments(), "
            + "additionalDiscount: target.getDirectDebit().getContractPaymentTerm().getAdditionalDiscount(), "
            + "additionalDiscountNotes: target.getDirectDebit().getContractPaymentTerm().getAdditionalDiscountNotes(), "
            + "discount: target.getDirectDebit().getContractPaymentTerm().getDiscount(), "
            + "contract: (target.getDirectDebit().getContractPaymentTerm().getContract() != null ? "
            + "{id: target.getDirectDebit().getContractPaymentTerm().getContract().getId(), "
            + "contractType: target.getDirectDebit().getContractPaymentTerm().getContract().getContractType(), "
            + "contractProspectType: target.getDirectDebit().getContractPaymentTerm().getContract().getContractProspectType(), "
            + "client: (target.getDirectDebit().getContractPaymentTerm().getContract().getClient() != null ? "
            + " {id: target.getDirectDebit().getContractPaymentTerm().getContract().getClient().getId(), "
            + "name: target.getDirectDebit().getContractPaymentTerm().getContract().getClient().getName()}"
            + ": null), "
            + "housemaid: (target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid() != null ? "
            + " {id: target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getId(), "
            + "name: target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getName(),"
            + "nationality: (target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getNationality() != null ? "
            + " {id: target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getNationality().getId(), "
            + "name: target.getDirectDebit().getContractPaymentTerm().getContract().getHousemaid().getNationality().getName()}"
            + ": null)}"
            + ": null)} "
            + ": null), "
            + "bankName: target.getDirectDebit().getContractPaymentTerm().getBankName()} "
            + ": null}: null}")
    Map<?, ?> getDirectDebit();

    String getDdaRefNo();

    String getApplicationId();

    Date getCreationDate();

    Date getStartDate();

    Date getExpiryDate();

    String getRejectionReason();

    Double getAmount();

    @Value("#{target.getDirectDebit() != null && target.getDirectDebit().getPaymentsCount() != null ? target.getDirectDebit().getPaymentsCount() : 0}")
    Integer getPaymentsCount();

    @Value("#{target.getDdFrequency() != null ? target.getDdFrequency().getValue() : ''}")
    String getDdFrequency();

    Boolean getConfirmedBankInfo();

    @Value("#{target.getStatus().getValue().equals('SENT')}")
    Boolean getUploaded();

    Date getResultDate();

    String getNotes();

    String getType();

    Boolean getNeedAccountantReConfirmation();

    DirectDebitRejectCategory getRejectCategory();

    //Jirra ACC-2531
    String getBankName();
}
