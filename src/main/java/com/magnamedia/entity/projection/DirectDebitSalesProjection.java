package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.serializer.DDFListSecuredAttachmentsProjection;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitSource;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 07, 2020
 *         Jirra ACC-1435
 */

public interface DirectDebitSalesProjection {

    Long getId();

    String getAccountName();

    Double getAdditionalDiscount();

    String getAdditionalDiscountNotes();

    double getAmount();

    List<Attachment> getAttachments();

    int getAttachmentsCount();

    String getBankName();

    Boolean getConfirmedBankInfo();

    @Value("#{target.getContractPaymentTerm() != null ? "
            + "{id: target.getContractPaymentTerm().getId(), "
            + "label: target.getContractPaymentTerm().getLabel(), "
            + "isActive: target.getContractPaymentTerm().isActive()}"
            + ": null}")
    Map<?, ?> getContractPaymentTerm();

    Date getCreationDate();

    @Value("#{target.getCreator() != null ? target.getCreator().getLoginName() : ''}")
    String getCreator();

    @Value("#{target.getCreatorModule() != null ? target.getCreatorModule().getName() : ''}")
    String getCreatorModule();

    Long getDdBankInfoGroup();

    @JsonSerialize(using = DDFListSecuredAttachmentsProjection.class)
    List<DirectDebitFile> getDirectDebitFiles();

    String getEid();

    String getEntityType();

    Date getExpiryDate();

    Boolean getIsSigned();

    Date getLastModificationDate();

    @Value("#{target.getLastModifier() != null ? target.getLastModifier().getLoginName() : ''}")
    String getLastModifier();

    Boolean getNonCompletedInfo();

    @Value("#{(target.getPayments() != null && !target.getPayments().isEmpty())" +
            " ? target.getPayments().get(0).getPaymentType() : null}")
    PicklistItem getPaymentType();

    String getRejectionReason();

    DirectDebitSource getSource();

    Date getStartDate();

    @Value("#{{label:target.getStatusLabel() , value:target.getStatusValue()}}")
    Map<?,?> getStatus();

    Double getSuggestedAmount();

    DirectDebitType getType();

    String getUuid();

    long getVersion();

    String getIbanNumber();

    //Jirra ACC-1588 from here
    //    String getApplicationId();
    //    Date getPresentmentDate();

    DirectDebitStatus getMStatus();

    Boolean getProrated();

    Date getProratedFrom();

    Date getProratedTo();

    DirectDebitCategory getCategory();

    //Jirra ACC-1588 to here

    String getDescription();

    //Jirra ACC-1778
    @Value("#{target.isAddedManuallyFromClientProfile()}")
    Boolean getAddedManuallyFromClientProfile();
}
