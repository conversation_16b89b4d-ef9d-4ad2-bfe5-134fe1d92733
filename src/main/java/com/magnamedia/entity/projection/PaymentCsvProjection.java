package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.ContractType;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import java.sql.Date;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 27, 2017
 */
public interface PaymentCsvProjection {

    @Value("#{target.getId()}")
    Long getPaymentName();

    @Value("#{(target.getContract()!=null)?"
            + "(target.getContract().getId()):''}")
    String getContractName();

    @Value("#{(target.getContract()!=null &&"
            + "target.getContract().getClient()!=null)?"
            + "(target.getContract().getClient().getName()):''}")
    String getClientName();

    @Value("#{(target.getContract()!=null)?"
            + "(target.getContract().getContractType()):''}")
    ContractType getContractType();

    @Value("#{(target.getContract()!=null &&"
            + "target.getContract().getContractProspectType()!=null)?"
            + "(target.getContract().getContractProspectType().getName()):''}")
    String getContractProspectType();

    //Jirra ACC-1305
    @Value("#{(target.getContract()!=null &&"
            + "target.getContract().getStartOfContract()!=null)?"
            + "(target.getContract().getStartOfContractCSV()):''}")
    String getContractStartDate();

    @Value("#{(target.getTypeOfPayment()!=null)?"
            + "(target.getTypeOfPayment().getName()):''}")
    String getTypeOfPayment();

    PaymentMethod getMethodOfPayment();
    Double getAmountOfPayment();
    Date getDateOfPayment();
    String getChequeName();
    String getChequeNumber();
    @Value("#{(target.getStatus()!=null)?"
            + "(target.getStatus().getLabel()):''}")
    String getStatus();
    PicklistItem getBankName();
    java.util.Date getCreationDate();
    //Jirra ACC-758
    String getNote();
    Boolean getChequeWithTheBank();
    Boolean getReplaced();

    //Jirra ACC-1588
    @Value("#{(target.getDirectDebitFile()!=null) ?"
            + "(target.getDirectDebitFile().getApplicationId()) : ''}")
    String getApplicationId();
    //Jirra ACC-943
    Boolean getVatPaidByClient();
    //ACC-1036
    Boolean getIsInitial();
    //ACC-1056
    Double getVat();

    java.util.Date getDateChangedToPDP();
    //ACC-1544
    java.util.Date getDateChangedToReceived();

    @Value("#{(target.getContract()!=null && target.getContract().getHousemaid() != null && " +
            "target.getContract().getHousemaid().getNationality() != null ? " +
            "target.getContract().getHousemaid().getNationality().getName(): '')}")
    String getNationality();

    @Value("#{(target.getContract()!=null && target.getContract().getStatus() != null ? " +
            "target.getContract().getStatus(): '')}")
    String getContractStatus();
}
