package com.magnamedia.entity.projection;

import com.magnamedia.extra.EmployeeType;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR>
 */
public interface VisaRequestExpensesCsvProjection {
    @Value("#{(target.getName())}")
    String getExpenseName();

    java.util.Date getCreationDate();

    EmployeeType getEmployeeType();

    String getContractType();

    @Value("#{target.getHouseMaidName()}")
    String getName();

    @Value("#{(target.getPaymentType())}")
    String getTypeOfPayment();

    String getDescription();

    Double getAmount();

    @Value("#{target.getFromBucketName()}")
    String getFromBucket();

    @Value("#{target.getExpenseName()}")
    String getExpense();

    //Jirra ACC-1361
    @Value("#{(target.getReferenceNumberCSV())}")
    String getReferenceNumber();
}
