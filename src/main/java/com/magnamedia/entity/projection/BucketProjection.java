package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.module.type.BucketType;
import com.magnamedia.module.type.WealthBucketType;

/**
 * <AUTHOR>
 *         <p>
 *         updated 30-1-2021 Muhammed <PERSON>od
 */
public interface BucketProjection {

    Long getId();

    String getName();

    String getCode();

    Double getBalance();

    String getHolderEmail();

    Double getInitialBalance();

    WealthBucketType getWealthBucketType();

    String getCardNumber();

    String getNameWithCardNumber();

    //ACC-2988
    @JsonSerialize(using = IdLabelSerializer.class)
    User getHolder();

    BucketType getBucketType();

    Boolean getAutoReplenishment();

    Boolean getIsActive();

    Double getAuthorizedBalance();

}
