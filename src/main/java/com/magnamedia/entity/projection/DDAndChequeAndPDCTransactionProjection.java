package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdSerializer;
import com.magnamedia.entity.*;
import com.magnamedia.entity.serializer.ContractJsonSerializer;
import com.magnamedia.module.type.BankTransactionMatchType;
import com.magnamedia.module.type.BankTransactionType;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Apr 25, 2020
 */

public interface DDAndChequeAndPDCTransactionProjection {

    public Long getId();

    public String getDescription();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    public Revenue getRevenue();

    public Date getDate();

    public String getPaymentDetail();

    @Value("#{(target.getPayment()!=null)?"
            + "(target.getPayment().getId()):(target.getBouncedPaymentId())}")
    public Long getBouncedPaymentId();

    // Jira ACC- 1924
    public PaymentStatus getPaymentStatus();

    public String getClient();

    BankTransactionMatchType getBankTransactionMatchType();

    @JsonSerialize(using = ContractJsonSerializer.class)
    public Contract getContract();

    public Double getVatAmount();

    public VatType getVatType();

    @Value("#{(target.getTransactionAmount() == null && target.getPayment() != null && target.getPayment().getAmountOfPayment() != null) ?"
            + "(target.getPayment().getAmountOfPayment()) : (target.getTransactionAmount())}")
    public Double getTransactionAmount();

    public String getReason();

    @Value("#{(target.getExpense()!=null)?{id:target.getExpense().getId(), label:target.getExpense().getNameLabel(), code:target.getExpense().getCodeLabel()}:null}")
    public Map<?, ?> getExpense();

    public boolean isResolved();

    @JsonSerialize(using = IdSerializer.class)
    public Transaction getTransaction();

    @JsonSerialize(using = IdSerializer.class)
    public Payment getPayment();

    public BankTransactionType getBankTransactionType();

    @Value("#{(target.getDirectDebitFile()!=null)?"
            + "(target.getDirectDebitFile().getApplicationId()):target.getApplicationIdFromDetails()}")
    String getApplicationId();


    @JsonSerialize(using = IdSerializer.class)
    public DirectDebitFile getDirectDebitFile();

    @Value("#{(target.getPayment()!=null)?"
            + "(target.getPayment().getChequeNumber()):(target.getBankStatementRecord() !=null ? (target.getBankStatementRecord().getBankReferenceNo()):(''))}")
    String getChequeNumber();

    @Value("#{(target.getPayment()!=null)?"
            + "(target.getPayment().getChequeName()):''}")
    String getChequeName();

    @Value("#{(target.getPayment()!=null && target.getPayment().getAmountOfPayment()!=null)?"
            + "(target.getPayment().getAmountOfPayment()):(target.getTransactionAmount())}")
    Double getAmountOfPayment();

    String getNote();

    @Value("#{(target.getContract() != null && target.getContract().getClient() !=null)? "
            + "target.getContract().getClient().getName() "
            + ": target.getClient()}")
    String getClientName();

    @Value("#{(target.getPayment()!=null)?"
            + "('Payment-' + target.getPayment().getId()):''}")
    String getPaymentId();

    @Value("#{(target.getPayment()!=null)?"
            + "(\"'\"+target.getPayment().getChequeNumber()):(target.getBankStatementRecord() !=null ? (\"'\"+target.getBankStatementRecord().getBankReferenceNo()):(''))}")
    String getChequeNumberCSV();
}