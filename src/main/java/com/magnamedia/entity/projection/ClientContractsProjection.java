package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.module.type.ContractStatus;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 6-8-2020
 * Jirra ACC-2307
 */
public interface ClientContractsProjection {
    
    public Long getId();

    ContractStatus getStatus();

    // JIRA ACC-4654
    PicklistItem getContractProspectType();

    @Value("#{T(com.magnamedia.core.Setup).getApplicationContext().getBean(\"contractService\").getLastAttachedHousemaid(target)}")
    @JsonSerialize(using = IdLabelSerializer.class)
    Housemaid getHousemaid();
}
