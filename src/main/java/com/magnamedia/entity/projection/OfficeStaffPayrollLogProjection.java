package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.CooQuestion;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Mamon.Masod on 2/20/2021.
 */
public interface OfficeStaffPayrollLogProjection {

    Long getId();

    Date getCreationDate();

    String getTodoCategory();

    @Value("#{target.getPayrollAccountantTodo().getTaskName()}")
    String getTaskName();

    String getLabel();

    @Value("#{target.getPayrollAccountantTodo().shouldAmountBeSecured() ? '****' : ((target.getTotalInAED() != null && target.getTotalInAED() != 0D) ? target.getTotalInAED().toString(): '' )}")
    String getTotalInAED();

    @Value("#{target.getPayrollAccountantTodo().shouldAmountBeSecured() ? '****' : (target.getAmountAED() != null ? target.getAmountAED().toString(): '' )}")
    String getAmountAED();

    @Value("#{target.getPayrollAccountantTodo().shouldAmountBeSecured() ? '****' : (target.getAmountInForeignCurrency() != null ? target.getAmountInForeignCurrency().toString(): '' )}")
    String getAmountInForeignCurrency();

    String getForeignCurrency();

    String getCurrency();

    @Value("#{target.getAccountNumber()}")
    String getAccountName();

    String getIban();

    @JsonSerialize(using = IdLabelSerializer.class)
    @Value("#{target.getPayrollAccountantTodo().getItemType()}")
    PicklistItem getTodoType();

    String getBeneficiary();

    @Value("#{target.getPayrollAccountantTodo().getDueSince()}")
    String getDueSince();

    @Value("#{target.getPayrollAccountantTodo().getRequester()}")
    String getRequester();

    @JsonSerialize(using = IdLabelSerializer.class)
    @Value("#{target.getPayrollAccountantTodo().getRequesterUser()}")
    User getRequesterUser();

    @Value("#{target.getPayrollAccountantTodo().getApprovalFlow()}")
    String getApprovalFlow();

    @JsonSerialize(using = IdLabelSerializer.class)
    @Value("#{target.getPayrollAccountantTodo().getApprover()}")
    User getApprover();

    String getDescription();

    String getManagerUserName();

    String getManagerAction();

    String getEntityType();

    List<CooQuestion> getCooQuestions();

    boolean isOneQuestionAnswered();

    boolean isNoneQuestionAnswered();

    boolean isAllQuestionsAnswered();

    @Value("#{target.attachments != null ? target.attachments.?[tag != null && (tag.equals('proofOfTransfer') || tag.equals('client_refund_transfer_slip'))] : null }")
    List<Attachment> getAttachments();

    @Value("#{target.getBeneficiaryType()}")
    public Map<String, String> getBeneficiaryType();

    @Value("#{target.getBeneficiaryTypeId()}")
    public Long getBeneficiaryTypeId();
}
