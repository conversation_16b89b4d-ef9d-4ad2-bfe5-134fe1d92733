package com.magnamedia.entity.projection;

import com.magnamedia.core.type.HousemaidStatus;
import java.util.Date;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public interface HousemaidList {
    
    Long getId();
    String getName();
    HousemaidStatus getStatus();
    Date getStartDate();
    Double getBasicSalary();
    
    Boolean getNotArabicSpeaker();
    
    @Value("#{target.getNationality() != null ? "
                + "{id:target.getNationality().getId(), "
                + "name:target.getNationality().getName()} "
                + ": null}")
    Map<?, ?> getNationality();
    //Jirra ACC-1093
    Double getAdditionToBalanceDeductionLimit();
}
