package com.magnamedia.entity.projection;

import java.sql.Date;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Dec 30, 2017
 */
public interface NonClientPDCProjection {

    Long getId();
    
    String getName();
    
    Integer getAmount();
    
    Date getChequeDueDate();
    
    Date getContractEndDate();
    
    String getLeaseCompanyName();
    
    String getChequeIssuerName();
    
    String getPropertyDiscription();
    
    java.util.Date getCreationDate();
    
    Boolean getBounced();
    
    Boolean getWithdrawn();
}
