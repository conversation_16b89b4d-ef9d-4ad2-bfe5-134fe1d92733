package com.magnamedia.entity.projection;

import java.util.Date;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Mar 13, 2019
 * Jirra ACC-469
 */
public interface PaymentMatchingFileProjection {
    
    String getId();
    String getFileName();
    Date getCreationDate();
    
    Integer getMatchedRecordsCount();
    
    Integer getConfirmedRecordsCount();
    
    Integer getNotMatchedRecordsCount();
    
    @Value("#{(target.getLastModifier() != null) ? "
            + "({id:target.getLastModifier().getId(), fullName:target.getLastModifier().getFullName()}) : null}")
    Map<?, ?> getLastModifier();
    
    @Value("#{target.getAttachments().size()>0?target.getAttachments().get(0).getUuid():null}")
    String getUuid();
}
