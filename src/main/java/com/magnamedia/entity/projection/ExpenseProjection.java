/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Expense;
import com.magnamedia.entity.serializer.ExpenseChildrenSerializer;
import com.magnamedia.module.type.ExpensePaymentMethod;
import com.magnamedia.module.type.LoanType;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public interface ExpenseProjection {

    Long getId();

    String getName();
    
    String getNameLabel();

    String getCode();

    Boolean getRequireInvoice();

    Set<ExpensePaymentMethod> getPaymentMethods();

    public Boolean getAutoDeducted();

    public LoanType getLoanType();
    public String getCaption();
    public String getLabel();
    @JsonSerialize(using = IdLabelSerializer.class)
    public User getManager();
    public boolean getDisabled();

    @JsonSerialize(using = ExpenseChildrenSerializer.class)
    public List<Expense> getChildren();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getRequestedFrom();

    @Value("#{(target.getApprovalMethod() == null) ? null : target.getApprovalMethod().getLabel()}")
    String getApprovalMethod();

    @Value("#{ (target.getApproveHolder() != null && target.getApproveHolder().getFullName() != null) ? " +
                "target.getApproveHolder().getFullName() : " +
                    "target.getApproveHolderType() == null ? null : " +
                        "target.getApproveHolderType() == T(com.magnamedia.module.type.ExpenseApproveHolderType).EMAIL ? " +
                        "target.getApproveHolderEmail() : null}")
    String getApproveHolder();

    Boolean getIsLimitedCOO();

    Double getLimitCOO();

    Double getLimitForApproval();
}
