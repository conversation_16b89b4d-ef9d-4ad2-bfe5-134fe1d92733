package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on June 30, 2020
 * Jirra ACC-2154
 */

public interface DDUnMatchedTransactionCSVProjection {

    String getReason();

    String getPaymentDetail();

    Double getTransactionAmount();

    Date getDate();

    // acc-2343
    @Value("#{(target.getPayment()!=null)?"
            + "(target.getPayment().getChequeNumber()):(target.getBankStatementRecord() !=null ? (target.getBankStatementRecord().getBankReferenceNo()):(''))}")
    String getChequeNumber();

    @Value("#{(target.getPayment()!=null)?"
            + "(\"'\"+target.getPayment().getChequeNumber()):(target.getBankStatementRecord() !=null ? (\"'\"+target.getBankStatementRecord().getBankReferenceNo()):(''))}")
    String getChequeNumberCSV();

    public String getDescription();

    String getNote();
}