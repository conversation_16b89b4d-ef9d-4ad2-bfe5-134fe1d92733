package com.magnamedia.entity.projection;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 */

public interface BucketCSVProjection {

    String getName();

    String getCode();

    Double getBalance();
    
    Double getInitialBalance();

    Double getAuthorizedBalance();

    @Value("#{target.getHolder() != null ? target.getHolder().getLabel() : ''}")
    String getHolder();

    @Value("#{target.getBucketType() != null ? target.getBucketType().getLabel() : ''}")
    String getBucketType();

    @Value("#{target.getAutoReplenishment() != null && target.getAutoReplenishment() ? 'Yes' : 'No'}")
    String getAutoReplenished();

    @Value("#{target.getIsActive()}")
    Boolean getEnabled();
}
