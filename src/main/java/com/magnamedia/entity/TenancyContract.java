

package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.extra.LabelValueEnum;

import java.sql.Date;
import javax.persistence.*;


/**
 *
 * <AUTHOR> <<EMAIL>> 
 * Created at May 9, 2018
 */
@Entity
public class TenancyContract extends BaseEntity {

    public enum NotifyType implements LabelValueEnum {
        BEFORE_TWO_WEEKS("Before 2 weeks"),
        BEFORE_TWO_MONTHS("Before 2 months"),
        BEFORE_THREE_MONTHS("Before 3 months"),
        BEFORE_SIX_MONTHS("Before 6 months");

        private final String label;
        NotifyType(String label) {
            this.label = label;
        }
        @Override
        public String getLabel() {
            return label;
        }
    }

    @Label
    @Column
    private String name;
    
    @Column
    private Date startDate;
    
    @Column
    private Date endDate;
    
    @Column
    private String description ;
    
    @Column
    private String ownerName; // now we call it "Second Party Name"
    
    @Column
    private String tenantName; // now we call it "First Party Name"
    
    @Column
    private Boolean active = true;

    @Column
    @Enumerated(EnumType.STRING)
    private NotifyType notifyDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private PicklistItem typeOfDocument;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User primaryPerson;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private User secondaryPerson;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public PicklistItem getTypeOfDocument() {
        return typeOfDocument;
    }

    public void setTypeOfDocument(PicklistItem typeOfDocument) {
        this.typeOfDocument = typeOfDocument;
    }

    public NotifyType getNotifyDate() { return notifyDate; }

    public void setNotifyDate(NotifyType notifyDate) { this.notifyDate = notifyDate; }

    public User getPrimaryPerson() { return primaryPerson; }

    public void setPrimaryPerson(User primaryPerson) { this.primaryPerson = primaryPerson; }

    public User getSecondaryPerson() { return secondaryPerson; }

    public void setSecondaryPerson(User secondaryPerson) { this.secondaryPerson = secondaryPerson; }
}
