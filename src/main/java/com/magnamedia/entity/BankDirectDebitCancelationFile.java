package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.module.type.DDActivationFileStatus;
import com.magnamedia.repository.BankDirectDebitCancelationFileRepository;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 20, 2019
 *         Jirra ACC-1134
 */
@Entity
public class BankDirectDebitCancelationFile extends BaseEntity {

    @Label
    private String bankName;

    @Column
    private Date date;

    @Column
    private Date OldestPendingForCancellationDD;

    @Column
    private boolean hidden = false;

    @Enumerated(EnumType.STRING)
    private DDActivationFileStatus status = DDActivationFileStatus.UNDER_PARSING;

    @Column
    @ColumnDefault("0")
    private boolean confirmedByRPA = false;

    @Column
    @ColumnDefault("0")
    private boolean addedByRPA = false;

    @JsonIgnore
    @NotAudited
    @OneToMany(mappedBy = "bankDirectDebitCancelationFile",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL)
    private List<BankDirectDebitCancelationRecord> records;

    public BankDirectDebitCancelationFile() {
    }

    public BankDirectDebitCancelationFile(Long attachmentId, Date date) {
        this(Setup.getRepository(AttachementRepository.class).getOne(attachmentId), date);
    }

    public BankDirectDebitCancelationFile(Attachment attachment, Date date) {
        this.addAttachment(attachment);
        this.setDate(date);
    }

    public DDActivationFileStatus getStatus() { return status; }

    public void setStatus(DDActivationFileStatus status) { this.status = status; }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public boolean isHidden() {
        return hidden;
    }

    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }

    public boolean isConfirmedByRPA() {
        return confirmedByRPA;
    }

    public void setConfirmedByRPA(boolean confirmedByRPA) {
        this.confirmedByRPA = confirmedByRPA;
    }

    public List<BankDirectDebitCancelationRecord> getRecords() {
        return records;
    }

    public void setRecords(List<BankDirectDebitCancelationRecord> records) {
        this.records = records;
    }

    public void setOldestPendingForCancellationDD(Date oldestPendingForCancellationDD) {
        OldestPendingForCancellationDD = oldestPendingForCancellationDD;
    }

    public Date getOldestPendingForCancellationDD() {
        return OldestPendingForCancellationDD;
    }

    public boolean isAddedByRPA() { return addedByRPA; }

    public void setAddedByRPA(boolean addedByRPA) { this.addedByRPA = addedByRPA; }

    @BeforeUpdate
    public void validateUpdate() {

        BankDirectDebitCancelationFileRepository bankDirectDebitCancelationFileRepository =
                Setup.getApplicationContext().getBean(BankDirectDebitCancelationFileRepository.class);

        BankDirectDebitCancelationFile old = bankDirectDebitCancelationFileRepository.getOne(getId());
        List<Long> oldRecordsIDs = old.getRecords().stream().map(x -> x.getId()).collect(Collectors.toList());
        if (getRecords().size() != oldRecordsIDs.size())
            throw new RuntimeException("Records is not updatable.");
        for (BankDirectDebitCancelationRecord record : getRecords())
            if (record.getId() == null || !oldRecordsIDs.contains(record.getId()))
                throw new RuntimeException("Records is not updatable.");
    }
}
