package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import org.hibernate.envers.NotAudited;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1227
 */

@Entity
public class MaidsAtCandidateWATransaction extends BaseEntity {
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private MaidsAtCandidateWA applicant;

    public MaidsAtCandidateWATransaction(){}

    public MaidsAtCandidateWATransaction(Transaction transaction, MaidsAtCandidateWA applicant) {
        this.transaction = transaction;
        this.applicant = applicant;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof MaidsAtCandidateWATransaction)) {
            return false;
        } else {
            MaidsAtCandidateWATransaction other = (MaidsAtCandidateWATransaction) object;
            return this.getId().equals(other.getId())
                    && this.applicant.getId().equals(other.applicant.getId()) && this.transaction.getId().equals(other.transaction.getId());
        }
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public MaidsAtCandidateWA getApplicant() {
        return applicant;
    }

    public void setApplicant(MaidsAtCandidateWA applicant) {
        this.applicant = applicant;
    }
}
