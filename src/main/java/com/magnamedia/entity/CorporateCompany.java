/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.entity;

import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.Max;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 25, 2020
 *         Jirra ACC-1435
 */
@Entity
public class CorporateCompany extends BaseEntity{
    
    @Column
    @Label
    private String name;

    @Column
    private boolean hasExpirityDate;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isHasExpirityDate() {
        return hasExpirityDate;
    }

    public void setHasExpirityDate(boolean hasExpirityDate) {
        this.hasExpirityDate = hasExpirityDate;
    }
}
