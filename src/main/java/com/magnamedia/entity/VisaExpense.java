package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.AfterInsert;
import com.magnamedia.core.entity.BackgroundTask;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.extra.EmployeeType;
import com.magnamedia.extra.ExpenseStatus;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.MaidContractType;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.*;
import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.List;

/**
 * <AUTHOR> Jabr <<EMAIL>>
 * Created on Oct 11, 2017
 */
@MappedSuperclass
public class VisaExpense<T extends VisaRequest> extends BaseEntity implements Serializable {

    @ManyToOne
    @JsonIgnore
    private T request;

    @Enumerated(EnumType.STRING)
    @Column
    private PaymentType paymentType;

    @Enumerated(EnumType.STRING)
    @Column
    private ExpensePurpose purpose;

    @Enumerated(EnumType.STRING)
    private ExpenseStatus status = ExpenseStatus.Pending;

    @Column
    private String referenceNumber;

    @OneToOne
    @JsonIgnore
    private Transaction transaction;

    @Column
    private String defaultBucketCode;

    @Column
    private Long contractId;

    @Transient
    private String name;

    public String getName() {
        return (this.getId() != null) ?
                this.visaExpenseType.replace("RequestExpense", "") + "_" + this.getId()
                : "";
    }

    @Column
    private double amount;

    @Column
    private Double charge;

    @Column
    private Double vatCharge;

    @Transient
    private String equation;

    //Jirra ACC-558
    @Transient
    @JsonIgnore
    private EmployeeType employeeType;

    public EmployeeType getEmployeeType() {
        if (this.request != null) {
            if (this.request.getOfficeStaff() != null) {
                this.employeeType = EmployeeType.Officestaff;
            } else if (this.request.getHousemaid() != null) {
                this.employeeType = EmployeeType.Housemaid;
            }
        }
        return employeeType;
    }

    //Jirra ACC-558
    @Transient
    @JsonIgnore
    private String employeeName;

    public String getEmployeeName() {
        if (this.request != null) {
            if (this.request.getOfficeStaff() != null) {
                this.employeeName = this.request.getOfficeStaff().getName();
            } else if (this.request.getHousemaid() != null) {
                this.employeeName = this.request.getHousemaid().getName();
            }
        }
        return employeeName;
    }

    //Jirra ACC-558
    @Transient
    @JsonIgnore
    private MaidContractType maidContractType;

    public MaidContractType getMaidContractType() {
        this.maidContractType = getMaidVisaAEContract() ?
                MaidContractType.Maid_Visa :
                MaidContractType.Maid_cc;
        return this.maidContractType;
    }

    //Jirra ACC-558
    @Transient
    @JsonIgnore
    private Boolean maidVisaAEContract = false;

    public Boolean getMaidVisaAEContract() {
        if (this.request != null && this.request.getHousemaid() != null) {
            this.maidVisaAEContract = false;
            PicklistRepository picklistRepository =
                    Setup.getRepository(PicklistRepository.class);
            PicklistItemRepository picklistItemRepository =
                    Setup.getRepository(PicklistItemRepository.class);
            PicklistItem maidVisa = picklistItemRepository.findByListAndCodeIgnoreCase(
                    picklistRepository.findByCode(AccountingModule.PICKLIST_PROSPECTTYPE),
                    "maidvisa.ae_prospect");
            if (maidVisa != null) {
                List<Contract> contracts =
                        Setup.getRepository(ContractRepository.class)
                                .findByHousemaidAndStatus(
                                        this.request.getHousemaid(),
                                        ContractStatus.ACTIVE);
                if (contracts.size() > 0 &&
                        contracts.get(0).getContractProspectType() != null &&
                        contracts.get(0).getContractProspectType().getId().equals(maidVisa.getId())) {
                    this.maidVisaAEContract = true;
                }
            }
        }
        return maidVisaAEContract;
    }

    @Transient
    private String visaExpenseType = this.getClass().getSimpleName();

    public VisaExpense(T request,
                       ExpensePurpose purpose) {
        this.request = request;
        this.purpose = purpose;
    }

    public T getRequest() {
        return request;
    }

    public void setRequest(T request) {
        this.request = request;
    }

    public PaymentType getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(PaymentType paymentType) {
        this.paymentType = paymentType;
    }

    public ExpensePurpose getPurpose() {
        return purpose;
    }

    public void setPurpose(ExpensePurpose purpose) {
        this.purpose = purpose;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public double getAmount() {
        return amount > 0 ?
                amount + (charge != null ? charge : 0) + (vatCharge != null ? vatCharge : 0) :
                amount - (charge != null ? charge : 0) - (vatCharge != null ? vatCharge : 0);
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public ExpenseStatus getStatus() {
        return status;
    }

    public void setStatus(ExpenseStatus status) {
        this.status = status;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }


    public String getVisaExpenseType() {
        return visaExpenseType;
    }

    public void setVisaExpenseType(String visaExpenseType) {
        this.visaExpenseType = visaExpenseType;
    }

    public void setEquation(String equation) {
        this.equation = equation;
    }

    public String getEquation() {
        DecimalFormat decimalFormat = new DecimalFormat("############.##");
        return amount + " (amount) + " + (charge != null ? charge : 0) + " (charge) + " + (vatCharge != null ? decimalFormat.format(vatCharge) : 0) + " ( vat charge ) ";
    }

    public Double getCharge() {
        return charge;
    }

    public void setCharge(Double charge) {
        this.charge = charge;
    }

    public Double getVatCharge() {
        return vatCharge;
    }

    public void setVatCharge(Double vatCharge) {
        this.vatCharge = vatCharge;
    }

    public Long getContractId() { return contractId; }

    public void setContractId(Long contractId) { this.contractId = contractId; }

    @JsonIgnore
    public String getDefaultBucketCode() { return PaymentType.Credit_Card.equals(getPaymentType()) ? defaultBucketCode : null; }

    public void setDefaultBucketCode(String defaultBucketCode) { this.defaultBucketCode = defaultBucketCode; }

}
