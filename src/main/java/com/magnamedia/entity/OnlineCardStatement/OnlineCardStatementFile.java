package com.magnamedia.entity.OnlineCardStatement;



import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.epayment.EPaymentProvider;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.NotAudited;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.Date;

/**
 * <AUTHOR>
 **/

// ACC-5587
@Entity
public class OnlineCardStatementFile extends BaseEntity {

    private Date uploadedDate;
    private boolean resolved = false;
    private boolean deleted = false;

    @NotAudited
    @Formula("(select count(o.ID) from ONLINECARDSTATEMENTRECORDS o " +
            "inner join ONLINECARDSTATEMENTTRANSACTIONS t on o.ID = t.ONLINE_CARD_STATEMENT_RECORD_ID " +
            "where o.Online_Card_Statement_File_ID = ID and t.TRANSACTION_ID is not null)")
    private int totalTransactions;

    @NotAudited
    @Formula("(select count(o.ID) from ONLINECARDSTATEMENTRECORDS o " +
            "where o.Online_Card_Statement_File_ID = ID and o.STATUS = 'MATCHED')")
    private int totalMatchedPayments;

    @NotAudited
    @Formula("(select count(o.ID) from ONLINECARDSTATEMENTRECORDS o " +
            "where o.Online_Card_Statement_File_ID = ID and o.STATUS = 'UNMATCHED')")
    private int totalUnMatchedPayments;

    @NotAudited
    @Formula("(select count(o.ID) from ONLINECARDSTATEMENTRECORDS o " +
            "where o.Online_Card_Statement_File_ID = ID and o.STATUS IN ('UNMATCHED_REFUND', 'MATCHED_REFUND'))")
    private int totalRefundedPayments;

    @NotAudited
    @Formula("(select count(o.ID) from ONLINECARDSTATEMENTRECORDS o " +
            "where o.Online_Card_Statement_File_ID = ID and o.STATUS = 'MATCHED_REFUND')")
    private int totalMatchedRefunds;

    @NotAudited
    @Formula("(select count(o.ID) from ONLINECARDSTATEMENTRECORDS o " +
            "where o.Online_Card_Statement_File_ID = ID and o.STATUS = 'UNMATCHED_REFUND')")
    private int totalUnmatchedRefunds;

    @NotAudited
    @Formula("(POS_TRANSACTION_ID IS NULL AND (SALES_AMOUNT IS NOT NULL OR CHECKOUT_PAYMENTS_SUM IS NOT NULL) AND " +
            "NOT EXISTS (SELECT 1 FROM ONLINECARDSTATEMENTRECORDS O " +
                        "where O.Online_Card_Statement_File_ID = ID and O.STATUS IN ('UNMATCHED_REFUND', 'UNMATCHED')))")
    private boolean allowCreatePosTransaction;

    @Enumerated(EnumType.STRING)
    private EPaymentProvider provider;

    private Double commissionSum = 0.0; // Sum of column P

    private Double transactionFeeSum = 0.0; // Sum of column AH

    private Double vatOnFeeSum = 0.0; // Sum of column AI

    private Double vatOnCommissionSum = 0.0; // Sum of column AJ

    private Double salesAmount = 0.0; // Sum of column O

    private Double netAmount = 0.0; // Sum of column Q

    private Date relatedToDate; // The date in the column J

    private Double commissionLastRow = 0.0; // The value of the last row of column P

    private Double vatOnFeeLastRow = 0.0; // The value of the last row of column AI

    private Double vatOnCommissionLastRow = 0.0; // The value of the last row of column AJ

    private Long posTransactionId;

    // CheckOUT
    private Double checkoutPaymentsSum = 0.0; // Sum of column AF when AD = Capture

    private Double checkoutRefundsSum = 0.0; // Sum of Column AF when AD = Refund

    private Double checkoutGatewayFeesSum = 0.0; // Sum of Column AF when AD = " Authorization Fixed Fee" Or "Refund Fixed Fee"

    private Double checkoutAcquiringPremiumsSum = 0.0; // Sum of Column AF when AD = "Premium Variable Fee"

    private Double checkoutSchemeFeesSum = 0.0; // Sum of Column AF when AD = "Scheme Variable Fee" or " Scheme Fixed fee"

    private Double checkoutInterchangeFeeSum = 0.0; // Sum of Column AF when AD = " Interchange Fixed Fee"

    private Double checkoutNetworkTokenFee = 0.0; // Sum of Column AF when AD = "Network Token Provisioning Fixed Fee" or "Network Token Update Fixed Fee"

    private Double checkoutVatSum = 0.0; // Sum of column AI

    private Double checkoutHoldingCurrencyAmount ; // Sum of column AF

    private String checkoutID ; // column S

    public Date getUploadedDate() { return uploadedDate; }

    public void setUploadedDate(Date uploadedDate) { this.uploadedDate = uploadedDate; }

    public boolean isResolved() { return resolved; }

    public void setResolved(boolean resolved) { this.resolved = resolved; }

    public boolean isDeleted() { return deleted; }

    public void setDeleted(boolean deleted) { this.deleted = deleted; }

    public int getTotalTransactions() { return totalTransactions; }

    public void setTotalTransactions(int totalTransactions) { this.totalTransactions = totalTransactions; }

    public int getTotalMatchedPayments() { return totalMatchedPayments; }

    public void setTotalMatchedPayments(int totalMatchedPayments) { this.totalMatchedPayments = totalMatchedPayments; }

    public int getTotalUnMatchedPayments() { return totalUnMatchedPayments; }

    public void setTotalUnMatchedPayments(int totalUnMatchedPayments) {
        this.totalUnMatchedPayments = totalUnMatchedPayments;
    }

    public int getTotalRefundedPayments() { return totalRefundedPayments; }

    public void setTotalRefundedPayments(int totalRefundedPayments) { this.totalRefundedPayments = totalRefundedPayments; }

    public int getTotalMatchedRefunds() { return totalMatchedRefunds; }

    public void setTotalMatchedRefunds(int totalMatchedRefunds) { this.totalMatchedRefunds = totalMatchedRefunds; }

    public int getTotalUnmatchedRefunds() { return totalUnmatchedRefunds; }

    public void setTotalUnmatchedRefunds(int totalUnmatchedRefunds) { this.totalUnmatchedRefunds = totalUnmatchedRefunds; }

    public EPaymentProvider getProvider() { return provider == null ? EPaymentProvider.CHECKOUT : provider; }

    public void setProvider(EPaymentProvider provider) { this.provider = provider; }

    public Double getCommissionSum() { return commissionSum; }

    public void setCommissionSum(Double commissionSum) { this.commissionSum = commissionSum; }

    public Double getTransactionFeeSum() { return transactionFeeSum; }

    public void setTransactionFeeSum(Double transactionFeeSum) { this.transactionFeeSum = transactionFeeSum; }

    public Double getVatOnFeeSum() { return vatOnFeeSum; }

    public void setVatOnFeeSum(Double vatOnFeeSum) { this.vatOnFeeSum = vatOnFeeSum; }

    public Double getVatOnCommissionSum() { return vatOnCommissionSum; }

    public void setVatOnCommissionSum(Double vatOnCommissionSum) { this.vatOnCommissionSum = vatOnCommissionSum; }

    public Double getSalesAmount() { return salesAmount; }

    public void setSalesAmount(Double salesAmount) { this.salesAmount = salesAmount; }

    public Double getNetAmount() { return netAmount; }

    public void setNetAmount(Double netAmount) { this.netAmount = netAmount; }

    public Date getRelatedToDate() { return relatedToDate; }

    public void setRelatedToDate(Date relatedToDate) { this.relatedToDate = relatedToDate; }

    public Double getCommissionLastRow() { return commissionLastRow; }

    public void setCommissionLastRow(Double commissionLastRow) { this.commissionLastRow = commissionLastRow; }

    public Double getVatOnFeeLastRow() { return vatOnFeeLastRow; }

    public void setVatOnFeeLastRow(Double vatOnFeeLastRow) { this.vatOnFeeLastRow = vatOnFeeLastRow; }

    public Double getVatOnCommissionLastRow() { return vatOnCommissionLastRow; }

    public void setVatOnCommissionLastRow(Double vatOnCommissionLastRow) { this.vatOnCommissionLastRow = vatOnCommissionLastRow; }

    public boolean isAllowCreatePosTransaction() { return allowCreatePosTransaction; }

    public void setAllowCreatePosTransaction(boolean allowCreatePosTransaction) { this.allowCreatePosTransaction = allowCreatePosTransaction; }

    public Long getPosTransactionId() { return posTransactionId; }

    public void setPosTransactionId(Long posTransactionId) { this.posTransactionId = posTransactionId; }

    public Double getCheckoutPaymentsSum() { return checkoutPaymentsSum; }

    public void setCheckoutPaymentsSum(Double checkoutPaymentsSum) { this.checkoutPaymentsSum = checkoutPaymentsSum; }

    public Double getCheckoutRefundsSum() { return checkoutRefundsSum; }

    public void setCheckoutRefundsSum(Double checkoutRefundsSum) { this.checkoutRefundsSum = checkoutRefundsSum; }

    public Double getCheckoutGatewayFeesSum() { return checkoutGatewayFeesSum; }

    public void setCheckoutGatewayFeesSum(Double checkoutGatewayFeesSum) { this.checkoutGatewayFeesSum = checkoutGatewayFeesSum; }

    public Double getCheckoutAcquiringPremiumsSum() { return checkoutAcquiringPremiumsSum; }

    public void setCheckoutAcquiringPremiumsSum(Double checkoutAcquiringPremiumsSum) { this.checkoutAcquiringPremiumsSum = checkoutAcquiringPremiumsSum; }

    public Double getCheckoutSchemeFeesSum() { return checkoutSchemeFeesSum; }

    public void setCheckoutSchemeFeesSum(Double checkoutSchemeFeesSum) { this.checkoutSchemeFeesSum = checkoutSchemeFeesSum; }

    public Double getCheckoutInterchangeFeeSum() { return checkoutInterchangeFeeSum; }

    public void setCheckoutInterchangeFeeSum(Double checkoutInterchangeFeeSum) { this.checkoutInterchangeFeeSum = checkoutInterchangeFeeSum; }

    public Double getCheckoutNetworkTokenFee() { return checkoutNetworkTokenFee; }

    public void setCheckoutNetworkTokenFee(Double checkoutNetworkTokenFee) { this.checkoutNetworkTokenFee = checkoutNetworkTokenFee; }

    public Double getCheckoutVatSum() { return checkoutVatSum; }

    public void setCheckoutVatSum(Double checkoutVatSum) { this.checkoutVatSum = checkoutVatSum; }

    public String getCheckoutID() { return checkoutID; }

    public void setCheckoutID(String checkoutID) { this.checkoutID = checkoutID; }

    public Double getCheckoutHoldingCurrencyAmount() { return checkoutHoldingCurrencyAmount; }

    public void setCheckoutHoldingCurrencyAmount(Double checkoutHoldingCurrencyAmount) { this.checkoutHoldingCurrencyAmount = checkoutHoldingCurrencyAmount; }
}
