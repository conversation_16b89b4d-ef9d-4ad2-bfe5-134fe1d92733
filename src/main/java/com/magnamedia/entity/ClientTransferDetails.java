package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.type.CoreParameter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 27, 2019
 * CM-442 
 */
@Entity
public class ClientTransferDetails extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Contract contract;
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Payment payment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Client client;

    @Column
    private Double amount;

    @Column(columnDefinition = "boolean default false")
    private Boolean confirmed;
    
    //Jirra ACC-1092
    @Column(columnDefinition = "boolean default false")
    private Boolean firstBouncedMsg;

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public Payment getPayment() {
        return payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public Double getAmount() {
        return amount;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }
    
    public Boolean getFirstBouncedMsg() {
        return firstBouncedMsg;
    }

    public void setFirstBouncedMsg(Boolean firstBouncedMsg) {
        this.firstBouncedMsg = firstBouncedMsg;
    }

    @JsonIgnore
    public String getTransfereDetailsLink() {
        return getServerUrl() + "/modules/client-mgmt/client-transfer-details/index.html#!/" + getUuid();
    }

    @JsonIgnore
    private String getServerUrl() {
        return Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE);
    }

}
