package com.magnamedia.entity;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.maidsatv2.actions.employeragreement.SendRequestForApprovalAction;
import com.magnamedia.entity.serializer.ContractJsonSerializer;
import com.magnamedia.entity.serializer.ExpensePaymentSerializer;
import com.magnamedia.entity.serializer.IdOnlySerializer;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.module.type.*;
import com.magnamedia.service.CreditCardReconciliationStatementParsingService;
import com.magnamedia.service.ExpensePaymentService;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Haj <PERSON> <<EMAIL>>
 *         Created At Apr 18, 2020
 **/


@Entity
@Table(indexes = {
        @Index(columnList = "uniqueId", name = "IDX_uniqueId")
})

public class BankStatementTransaction extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private BankStatementFile file;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private BankStatementRecord bankStatementRecord;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private Payment payment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = ExpensePaymentSerializer.class)
    private ExpensePayment expensePayment;

    // Jira ACC-1924
    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private DirectDebitFile directDebitFile;

    @Column
    @Label
    private String uniqueId;

    @Column
    private String reason;

    @Column
    private String description;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private Expense expense;

    @Column
    private String paymentDetail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private Revenue revenue;

    @Column
    private Long BouncedPaymentId;

    @Column
    String client;

    @Column
    private Date date;

    @Column(columnDefinition = "boolean default false")
    private boolean resolved = false;

    @Enumerated(EnumType.STRING)
    private BankTransactionType bankTransactionType;

    @Enumerated(EnumType.STRING)
    private BankTransactionMatchType bankTransactionMatchType;

    @Enumerated(EnumType.STRING)
    private BankTransactionStatus bankTransactionStatus;

    @Column
    private String toBucket;

    @Column
    private String fromBucket;

    @Column
    private Double vatAmount;

    @Column
    @Enumerated(EnumType.STRING)
    private VatType vatType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem license;

    @Column
    private String expenseCode;

    @Column
    private String forEntity;

    @Column
    private String forName;

    @Column
    private String expensifyId;

    @Column
    private String erpObjectId;

    @Column
    private Double transactionAmount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem bouncingReason;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = ContractJsonSerializer.class)
    private Contract contract;

    //Jirra ACC-3189
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ClientRefundToDo clientRefundToDo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdOnlySerializer.class)
    private Transaction transaction;

    @Column(length = 2500)
    private String note;

    @Column(columnDefinition = "boolean default false")
    private boolean transactionCreatedByUser = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private SendRequestForApprovalAction sendRequestForApprovalAction;

    @Column(columnDefinition = "boolean default false")
    private boolean processing = false;

    @Column
    private String beneficiaryName;

    public boolean isProcessing() {
        return processing;
    }

    public void setProcessing(boolean processing) {
        this.processing = processing;
    }

    public BankStatementFile getFile() {
        return file;
    }

    public void setFile(BankStatementFile file) {
        this.file = file;
    }

    public BankStatementRecord getBankStatementRecord() {
        return bankStatementRecord;
    }

    public void setBankStatementRecord(BankStatementRecord bankStatementRecord) {
        this.bankStatementRecord = bankStatementRecord;
    }

    public String getBeneficiaryName() { return beneficiaryName; }

    public void setBeneficiaryName(String beneficiaryInfo) { this.beneficiaryName = beneficiaryInfo; }

    public String getUniqueId() {
        return uniqueId;
    }

    public Payment getPayment() {
        return payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }

    public ExpensePayment getExpensePayment() {
        return expensePayment;
    }

    public void setExpensePayment(ExpensePayment expensePayment) {
        this.expensePayment = expensePayment;
    }

    public Expense getExpense() {
        return expense;
    }

    public PaymentStatus getPaymentStatus() {
        if (payment != null)
            return payment.getStatus();
        return paymentStatus;
    }

    public void setPaymentStatus(PaymentStatus paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public void setExpense(Expense expense) {
        this.expense = expense;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public boolean isResolved() {
        return resolved;
    }

    public PicklistItem getBouncingReason() {
        return bouncingReason;
    }

    public void setBouncingReason(PicklistItem bouncingReason) {
        this.bouncingReason = bouncingReason;
    }

    public String getPaymentDetail() {
        return paymentDetail;
    }

    public DirectDebitFile getDirectDebitFile() {
        return directDebitFile;
    }

    public void setDirectDebitFile(DirectDebitFile directDebitFile) {
        this.directDebitFile = directDebitFile;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public Long getBouncedPaymentId() {
        return BouncedPaymentId;
    }

    public void setBouncedPaymentId(Long bouncedPaymentId) {
        BouncedPaymentId = bouncedPaymentId;
    }

    public void setPaymentDetail(String paymentDetail) {
        this.paymentDetail = paymentDetail;
    }

    public void setResolved(boolean resolved) {
        this.resolved = resolved;
    }

    public BankTransactionType getBankTransactionType() {
        return bankTransactionType;
    }

    public Date getDate() {
        return date;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setBankTransactionType(BankTransactionType bankTransactionType) {
        this.bankTransactionType = bankTransactionType;
    }

    public BankTransactionStatus getBankTransactionStatus() {
        return bankTransactionStatus;
    }

    public void setBankTransactionStatus(BankTransactionStatus bankTransactionStatus) {
        this.bankTransactionStatus = bankTransactionStatus;
    }

    public String getToBucket() {
        return toBucket;
    }

    public void setToBucket(String toBucket) {
        this.toBucket = toBucket;
    }

    public String getFromBucket() {
        return fromBucket;
    }

    public void setFromBucket(String fromBucket) {
        this.fromBucket = fromBucket;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public PicklistItem getLicense() {
        return license;
    }

    public void setLicense(PicklistItem license) {
        this.license = license;
    }

    public String getExpenseCode() {
        return expenseCode;
    }

    public void setExpenseCode(String expenseCode) {
        this.expenseCode = expenseCode;
    }

    public String getForEntity() {
        return forEntity;
    }

    public void setForEntity(String forEntity) {
        this.forEntity = forEntity;
    }

    public String getForName() {
        return forName;
    }

    public void setForName(String forName) {
        this.forName = forName;
    }

    public String getExpensifyId() {
        return expensifyId;
    }

    public void setExpensifyId(String expensifyId) {
        this.expensifyId = expensifyId;
    }

    public String getErpObjectId() {
        return erpObjectId;
    }

    public void setErpObjectId(String erpObjectId) {
        this.erpObjectId = erpObjectId;
    }

    public Double getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(Double transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public Contract getContract() {
        return contract;
    }

    public void setContract(Contract contract) {
        this.contract = contract;
    }

    public ClientRefundToDo getClientRefundToDo() {
        return clientRefundToDo;
    }

    public void setClientRefundToDo(ClientRefundToDo clientRefundToDo) {
        this.clientRefundToDo = clientRefundToDo;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public BankTransactionMatchType getBankTransactionMatchType() {
        return bankTransactionMatchType;
    }

    public void setBankTransactionMatchType(BankTransactionMatchType bankTransactionMatchType) {
        this.bankTransactionMatchType = bankTransactionMatchType;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public boolean isTransactionCreatedByUser() {
        return transactionCreatedByUser;
    }

    public void setTransactionCreatedByUser(boolean transactionCreatedByUser) {
        this.transactionCreatedByUser = transactionCreatedByUser;
    }

    public SendRequestForApprovalAction getSendRequestForApprovalAction() {
        return sendRequestForApprovalAction;
    }

    public void setSendRequestForApprovalAction(SendRequestForApprovalAction sendRequestForApprovalAction) {
        this.sendRequestForApprovalAction = sendRequestForApprovalAction;
    }

    public String getApplicationIdFromDetails() {
        String paymentDetail = this.getBankStatementRecord().getPaymentDetail();
        String applicationId = paymentDetail.substring(paymentDetail.lastIndexOf("_") + 1, paymentDetail.length());
        applicationId = applicationId.trim().toUpperCase();
        return applicationId;
    }

    @Transient
    public ExpensePaymentMethod getExpensePaymentType() {
        return expensePayment != null ? expensePayment.getMethod() : null;
    }

    @Transient
    public List<Attachment> getVatAttachments() {
        ExpensePaymentService expensePaymentService = Setup.getApplicationContext().getBean(ExpensePaymentService.class);
        if (expensePayment != null && bankTransactionType.equals(BankTransactionType.EXPENSE_REQUEST)) {
            return expensePaymentService.getVatAttachments(expensePayment);
        }

        return null;
    }

    @Transient
    public List<Attachment> getNoVatAttachments() {
        ExpensePaymentService expensePaymentService = Setup.getApplicationContext().getBean(ExpensePaymentService.class);
        if (expensePayment != null && bankTransactionType.equals(BankTransactionType.EXPENSE_REQUEST)) {
            return expensePaymentService.getNoVatAttachments(expensePayment);
        }

        return null;
    }

    @Transient
    public boolean getMissingVatInvoice() {
        CreditCardReconciliationStatementParsingService parsingService = Setup.getApplicationContext().getBean(CreditCardReconciliationStatementParsingService.class);
        return parsingService.isMissingTaInvoice(getVatAttachments(), this.getVatAmount());
    }
}
