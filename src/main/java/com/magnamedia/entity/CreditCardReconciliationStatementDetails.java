package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.workflow.BucketReplenishmentTodo;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2/1/2021
 */
@Entity
public class CreditCardReconciliationStatementDetails extends BaseEntity {


    public enum CRDRAction {
        DR,
        CR
    }

    public enum MatchType {
        EXISTING_EXPENSE,
        REPLENISHMENT,
        AUTO_DEDUCTED,
        REFUND,
        ALREADY_MATCHED_TRANSACTION,
        UNMATCHED
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private CreditCardReconciliationStatement creditCardReconciliationStatement;

    @Column
    private String recordDescription;

    @Column
    private Date recordTransactionDate;

    @Column
    private Double recordAmount;

    @Column
    private Double requestAmount;

    @Column
    private Double requestAmountInLocalCurrency;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem requestCurrency;

    @Column
    @Enumerated(EnumType.STRING)
    private CRDRAction crdrAction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense matchedAutoDeduct;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private ExpenseRequestTodo matchedExpenseRequest;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private BucketReplenishmentTodo replenishmentTodo;

    @OneToOne
    @JsonIgnore
    private ReconciliationTransaction reconciliationTransaction;

    @Column
    private Long transactionId;

    @Column
    private Date paymentDate;

    @Column
    private String status;

    @Column
    @Enumerated(EnumType.STRING)
    private MatchType matchType;

    @Column
    private Boolean confirmed = false;

    public CreditCardReconciliationStatement getCreditCardReconciliationStatement() {
        return creditCardReconciliationStatement;
    }

    public void setCreditCardReconciliationStatement(CreditCardReconciliationStatement creditCardReconciliationStatement) {
        this.creditCardReconciliationStatement = creditCardReconciliationStatement;
    }

    public BucketReplenishmentTodo getReplenishmentTodo() {
        return replenishmentTodo;
    }

    public ReconciliationTransaction getReconciliationTransaction() {
        return reconciliationTransaction;
    }

    public void setReconciliationTransaction(ReconciliationTransaction reconciliationTransaction) {
        this.reconciliationTransaction = reconciliationTransaction;
    }

    public Long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Long transactionId) {
        this.transactionId = transactionId;
    }

    public void setReplenishmentTodo(BucketReplenishmentTodo replenishmentTodo) {
        this.replenishmentTodo = replenishmentTodo;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public String getRecordDescription() {
        return recordDescription;
    }

    public void setRecordDescription(String recordDescription) {
        this.recordDescription = recordDescription;
    }

    public Date getRecordTransactionDate() {
        return recordTransactionDate;
    }

    public void setRecordTransactionDate(Date recordTransactionDate) {
        this.recordTransactionDate = recordTransactionDate;
    }

    public Double getRecordAmount() {
        return recordAmount;
    }

    public void setRecordAmount(Double recordAmount) {
        this.recordAmount = recordAmount;
    }

    public Double getRequestAmount() {
        return requestAmount;
    }

    public void setRequestAmount(Double requestAmount) {
        this.requestAmount = requestAmount;
    }

    public Double getRequestAmountInLocalCurrency() {
        return requestAmountInLocalCurrency;
    }

    public void setRequestAmountInLocalCurrency(Double requestAmountInLocalCurrency) {
        this.requestAmountInLocalCurrency = requestAmountInLocalCurrency;
    }

    public PicklistItem getRequestCurrency() {
        return requestCurrency;
    }

    public void setRequestCurrency(PicklistItem requestCurrency) {
        this.requestCurrency = requestCurrency;
    }

    public CRDRAction getCrdrAction() {
        return crdrAction;
    }

    public void setCrdrAction(CRDRAction crdrAction) {
        this.crdrAction = crdrAction;
    }

    public Expense getMatchedAutoDeduct() {
        return matchedAutoDeduct;
    }

    public void setMatchedAutoDeduct(Expense matchedAutoDeduct) {
        this.matchedAutoDeduct = matchedAutoDeduct;
    }

    public ExpenseRequestTodo getMatchedExpenseRequest() {
        return matchedExpenseRequest;
    }

    public void setMatchedExpenseRequest(ExpenseRequestTodo matchedExpenseRequest) {
        this.matchedExpenseRequest = matchedExpenseRequest;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public MatchType getMatchType() {
        return matchType;
    }

    public void setMatchType(MatchType matchType) {
        this.matchType = matchType;
    }

    @Override
    public String toString() {
        return "Date: " + recordTransactionDate + ", Description: " + recordDescription + ", crdrAction: " + crdrAction + ", Amount: " + recordAmount;
    }

    @JsonIgnore
    public boolean isAbleToEditVatInfo() {
        return matchType != null && matchType.equals(MatchType.AUTO_DEDUCTED) &&
                matchedAutoDeduct != null && matchedAutoDeduct.getSuppliers() != null && matchedAutoDeduct.getSuppliers().size() == 1 &&
                (matchedAutoDeduct.getSuppliers().get(0).getVatRegistered() == null || matchedAutoDeduct.getSuppliers().get(0).getVatRegistered());
    }
}
