package com.magnamedia.entity.maidsatv2.actions.employeragreement;

import com.magnamedia.entity.maidsatv2.actions.MaidsAtHustlerAction;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;

/**
 * <PERSON> (May 03, 2021)
 */
@Entity
@Setter
@Getter
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public abstract class EmployerAgreementAction extends MaidsAtHustlerAction {

    private Double amount;
    private EmployerAgreementPaymentMethod paymentMethod;
    private String notes;
}