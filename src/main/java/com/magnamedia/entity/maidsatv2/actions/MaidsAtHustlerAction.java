package com.magnamedia.entity.maidsatv2.actions;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.MaidsAtCandidateWA;

import javax.persistence.*;

/**
 * <PERSON> (Nov 28, 2020)
 */
@Entity
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public abstract class MaidsAtHustlerAction extends BaseEntity {

    @JsonSerialize(using = IdLabelSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "MAIDS_AT_CANDIDATE_WA_ID")
    private MaidsAtCandidateWA maidsAtCandidateWA;

    public MaidsAtCandidateWA getMaidsAtCandidateWA() {
        return maidsAtCandidateWA;
    }

    public void setMaidsAtCandidateWA(MaidsAtCandidateWA maidsAtCandidateWA) {
        this.maidsAtCandidateWA = maidsAtCandidateWA;
    }
}