package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.module.type.PLNodeType;
import com.magnamedia.repository.BaseCompanyReportRepository;
import com.magnamedia.repository.BasePLNodeRepository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@MappedSuperclass
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(value = PLNode.class, name = "PLNode"), @JsonSubTypes.Type(value = AdhocNode.class, name = "AdhocNode")})
public class BasePLNode<T extends BaseReportCompany> extends BaseEntity {

    @Column(insertable = false, updatable = false, length = 255)
    protected String type = this.getClass().getSimpleName();

    @Label
    protected String name;

    @Column
    protected Integer nodeOrder;

    @Column
    protected PLNodeType pLNodeType;

    // ACC-448 Adding Color field to BasePLNode
    @Column
    protected String color;

    @Transient
    protected Double value;

    //Jirra ACC-2500
    @Transient
    protected Double vatAmount;

    //Jirra ACC-1389
    @Transient
    protected Double average;

    //Jirra ACC-1389
    @Transient
    protected Double profitAdjustment;

    @Transient
    protected Long ratio;

    @Transient
    protected Long relatedRatio;

    @Transient
    protected boolean noValidate = false;

    @Transient
    protected BaseCompanyReportController.SearchCriteria searchCriteria;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getNodeOrder() {
        return nodeOrder;
    }

    public void setNodeOrder(Integer nodeOrder) {
        this.nodeOrder = nodeOrder;
    }

    public PLNodeType getpLNodeType() {
        return pLNodeType;
    }

    public void setpLNodeType(PLNodeType pLNodeType) {
        this.pLNodeType = pLNodeType;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public Double getVatAmount() {
        if (vatAmount == null) return 0D;
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public Double getAverage() {
        if (average == null) return 0.0;
        return average;
    }

    public void setAverage(Double average) {
        this.average = average;
    }

    public Double getProfitAdjustment() {
        if (profitAdjustment == null) return 0.0;
        return profitAdjustment;
    }

    public void setProfitAdjustment(Double profitAdjustment) {
        this.profitAdjustment = profitAdjustment;
    }

    public T getPLCompany() {
        return null;
    }

    public void setPLCompany(T pLCompany) {
    }

    public BasePLNode getParent() {
        return null;
    }

    public void setParent(BasePLNode parent) {
    }


    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getReportColor() {
        if (this.color != null && (!this.color.isEmpty()))
            return this.color;
        else if (this.getParent() != null)
            return this.getParent().getReportColor();
        else
            return null;
    }

    public List<BasePLNode> getSortedChildren() {
        return null;
    }

    public List<BasePLNode> getChildren() {
        return null;
    }

    public void setChildren(List<BasePLNode> children) {
    }

    public Long getRatio() {
        return ratio;
    }

    public void setRatio(Long ratio) {
        this.ratio = ratio;
    }

    public Long getRelatedRatio() {
        return relatedRatio;
    }

    public void setRelatedRatio(Long relatedRatio) {
        this.relatedRatio = relatedRatio;
    }

    public Double calculateAndSetValue(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        this.value = 0.0D;
        this.vatAmount = 0.0D;
        List<BasePLNode> nodes = getSortedChildren();
        for (BasePLNode plNode : nodes) {
            this.value += plNode.calculateAndSetValue(fromDate, toDate, searchCriteria);
            this.vatAmount += plNode.calculateVatAmount(fromDate, toDate);
        }
        Long childrenRatio = 0L;
        for (int i = 0; i < nodes.size(); i++) {
            BasePLNode plNode = nodes.get(i);
            Long childRatio = Math.round(plNode.getValue() * 100 / this.value);
            plNode.setRatio(childRatio);
            childrenRatio += childRatio;
        }
        if (childrenRatio != 100L) {
            Long complementary = 100L - childrenRatio;
            for (int i = nodes.size() - 1; i >= 0; i--)
                if (nodes.get(i).getValue() != 0.0D) {
                    nodes.get(i).setRatio(
                            nodes.get(i).getRatio() + complementary);
                    break;
                }
        }
        return this.value;
    }

    public Double calculateVatAmount(Date fromDate, Date toDate) {
        this.vatAmount = 0.0D;
        List<BasePLNode> nodes = getSortedChildren();
        for (BasePLNode plNode : nodes) {
            this.vatAmount += plNode.calculateVatAmount(fromDate, toDate);
        }

        return this.vatAmount;
    }

    public Double calculateValue(Date fromDate, Date toDate) {
        Double amountTemp = 0.0D;
        List<BasePLNode> nodes = getSortedChildren();
        for (BasePLNode plNode : nodes) {
            amountTemp += plNode.calculateValue(fromDate, toDate);
        }

        return amountTemp;
    }

    //Jirra ACC-1389
    public Double calculateProfitAdjustmentValue(Date fromDate, Date toDate) {
        this.profitAdjustment = 0.0D;
        List<BasePLNode> nodes = getSortedChildren();
        for (BasePLNode plNode : nodes) {
            this.profitAdjustment += plNode.calculateProfitAdjustmentValue(fromDate, toDate);
        }

        return this.profitAdjustment;
    }

    //Jirra ACC-1389
    public Double calculateAverageValue(Date fromDate, Date toDate) {
        this.average = 0.0D;
        List<BasePLNode> nodes = getSortedChildren();
        for (BasePLNode plNode : nodes) {
            this.average += plNode.calculateAverageValue(fromDate, toDate);
        }

        return this.average;
    }

    //Jirra ACC-385
    public Long calculateRelatedRatio(Double otherValue) {

        Long childrenRatio = 0L;
        if (getChildren() != null && !getChildren().isEmpty()) {
            List<BasePLNode> nodes = getSortedChildren();
            for (int i = 0; i < nodes.size(); i++) {
                BasePLNode plNode = nodes.get(i);
                Long childRatio = Math.round(plNode.getValue() * 100 / otherValue);
                plNode.setRelatedRatio(childRatio);
                childrenRatio += childRatio;
            }
            if (childrenRatio != this.relatedRatio) {
                Long complementary = this.relatedRatio - childrenRatio;
                for (int i = nodes.size() - 1; i >= 0; i--)
                    if (nodes.get(i).getValue() != 0.0D) {
                        nodes.get(i).setRelatedRatio(
                                nodes.get(i).getRelatedRatio() + complementary);
                        break;
                    }
            }
            for (BasePLNode node : getChildren())
                node.calculateRelatedRatio(otherValue);
        }
        return childrenRatio;
    }

    // ACC-496 2) Get all transactions behind a row in P&Ls page | Majd Bousaad
    public List<Transaction> getTransactionsBehindNode(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        List<Transaction> transactions = new ArrayList<>();

        getChildren().stream().forEach((pLNode) -> {
            transactions.addAll(pLNode.getTransactionsBehindNode(fromDate, toDate, searchCriteria));
        });

        return transactions;

    }

    //Jirra ACC-1389
    public List<TransactionDetails> getTransactionDetailsBehindNode(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
        List<TransactionDetails> transactionDetails = new ArrayList<>();

        getChildren().stream().forEach((pLNode) -> {
            transactionDetails.addAll(pLNode.getTransactionDetailsBehindNode(fromDate, toDate, searchCriteria));
        });

        return transactionDetails;

    }

    //Jirra ACC-804
    public Double calculateOutputVATCollected(Date fromDate, Date toDate) {
        Double result = 0D;
        for (BasePLNode plNode : getChildren()) {
            result += plNode.calculateOutputVATCollected(fromDate, toDate);
        }
        return result;
    }

    //Jirra ACC-804
    public Double calculateInputVATCollected(Date fromDate, Date toDate) {
        Double result = 0D;
        for (BasePLNode plNode : getChildren()) {
            result += plNode.calculateInputVATCollected(fromDate, toDate);
        }
        return result;
    }

    @BeforeInsert
    @BeforeUpdate
    public void validate() {
        if (!isNoValidate()) {
            if (getName() == null || getName().isEmpty())
                throw new RuntimeException("Name should not be empty.");

            if ((getPLCompany() == null || getPLCompany().getId() == null)
                    && (getParent() == null || getParent().getId() == null))
                throw new RuntimeException("You should add node to either Company or another node.");

            //could not add node to variable
            if (this.getParent() != null && this.getParent().type.equals("PLVariableNode"))
                if (this.type.equals("PLVariableNode"))
                    throw new RuntimeException("Variable could not be added to another variable.");
                else
                    throw new RuntimeException("Node could not be added to a variable.");
        }
    }

    @Transactional
    public boolean move(Integer newOrder, Integer newParent) {
        if ((newParent == -1 && getParent() != null) ||
                (newParent != -1 && getParent() == null))
            throw new RuntimeException("can not change Parent while moving node");
        else if (newParent != null && getParent() != null &&
                newParent.longValue() != getParent().getId()) {
            throw new RuntimeException("can not change Parent while moving node");
        }
        // get all nodes before current node and make order = order -1
        BasePLNodeRepository pLNodeRepository = getRepository();
        List<BasePLNode> pLNodes;
        if (getParent() == null)
            pLNodes =
                    pLNodeRepository.
                            findByNodeOrderGreaterThanAndPLCompanyAndParentIsNull(
                                    getNodeOrder(), getPLCompany());
        else
            pLNodes =
                    pLNodeRepository.findByNodeOrderGreaterThanAndParent(
                            this.getNodeOrder(), getParent());
        for (BasePLNode plNode : pLNodes) {
            plNode.setNodeOrder(plNode.getNodeOrder() - 1);
        }
        pLNodeRepository.save(pLNodes);

        List<BasePLNode> pLNodes2;
        if (getParent() == null)
            pLNodes2 =
                    pLNodeRepository.
                            findByNodeOrderGreaterThanAndPLCompanyAndParentIsNull(
                                    newOrder - 1, getPLCompany());
        else
            pLNodes2 =
                    pLNodeRepository.findByNodeOrderGreaterThanAndParent(
                            newOrder - 1, getParent());
        for (BasePLNode plNode : pLNodes2) {
            plNode.setNodeOrder(plNode.getNodeOrder() + 1);
        }
        pLNodeRepository.save(pLNodes2);
        BasePLNode node = (BasePLNode) pLNodeRepository.findOne(this.getId());
        node.setNodeOrder(newOrder);
        pLNodeRepository.save(node);
        this.nodeOrder = newOrder;
        return true;
    }

    @BeforeDelete
    public void beforDelete() {
        BasePLNodeRepository pLNodeRepository = getRepository();
        BaseCompanyReportRepository pLCompanyRepository = getPLCompany().getRepository();
        List<BasePLNode> pLNodes;
        if (getParent() == null)
            pLNodes =
                    pLNodeRepository.
                            findByNodeOrderGreaterThanAndPLCompanyAndParentIsNull(
                                    getNodeOrder(), getPLCompany());
        else
            pLNodes =
                    pLNodeRepository.
                            findByNodeOrderGreaterThanAndParent(
                                    getNodeOrder(), getParent());
        for (BasePLNode node : pLNodes) {
            node.setNodeOrder(node.getNodeOrder() - 1);
        }
        pLNodeRepository.save(pLNodes);
    }

    public BaseCompanyReportController.SearchCriteria getSearchCriteria() {
        return searchCriteria;
    }

    @JsonIgnore
    protected BasePLNodeRepository getRepository() {
        return null;
    }

    public boolean isNoValidate() { return noValidate; }

    public void setNoValidate(boolean noValidate) { this.noValidate = noValidate; }
}
