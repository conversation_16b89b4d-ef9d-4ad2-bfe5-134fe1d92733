package com.magnamedia.entity.sms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.Sms;
import com.magnamedia.core.Setup;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.type.CoreParameter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import org.hibernate.envers.Audited;
import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created at Jun 20, 2019
 * <AUTHOR> kanaan <<EMAIL>>
 * Jirra ACC-1092
 */
@Entity
@Table(name="CM_SMS_EXTENDED_LOGS")
public class SmsExtendedLog extends BaseEntity  {

    @OneToOne( fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    @Audited(targetAuditMode = NOT_AUDITED)
    private Sms coreLog;
    
    @Column(columnDefinition = "boolean default false")
    private Boolean confirmed = false;
    
    @ManyToOne( fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private SmsTemplate template;
    @ManyToOne( fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private SmsTranslation translation;

    public Sms getCoreLog() {
        return coreLog;
    }

    public void setCoreLog(Sms coreLog) {
        this.coreLog = coreLog;
    }

    
    
    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public SmsTemplate getTemplate() {
        return template;
    }

    public void setTemplate(SmsTemplate template) {
        this.template = template;
    }

    public SmsTranslation getTranslation() {
        return translation;
    }

    public void setTranslation(SmsTranslation translation) {
        this.translation = translation;
    }
    @JsonIgnore
    public String getConfirmationLink(){
        return getServerUrl()+"/modules/client-mgmt/confirm/index.html#!/"+getUuid();
    }
    @JsonIgnore
    private String getServerUrl() {
        return Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE);
    }

}
