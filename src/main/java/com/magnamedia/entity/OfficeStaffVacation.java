///*
// * To change this license header, choose License Headers in Project Properties.
// * To change this template file, choose Tools | Templates
// * and open the template in the editor.
// */
//package com.magnamedia.entity;
//
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.magnamedia.core.entity.BaseEntity;
//import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
//import java.util.Date;
//import javax.persistence.Column;
//import javax.persistence.Entity;
//import javax.persistence.FetchType;
//import javax.persistence.ManyToOne;
//
///**
// *
// * <AUTHOR> <<EMAIL>>
// */
//
//@Entity
//public class OfficeStaffVacation extends BaseEntity{
//
//
//    @ManyToOne(fetch = FetchType.LAZY)
//    @JsonSerialize(using = CustomIdLabelSerializer.class)
//    private OfficeStaff officeStaff;
//
//
//
//    @Column
//    private String information;
//
//    @Column
//    private Date VacationFrom;
//
//    @Column
//    private Date VacationTo;
//
//    public OfficeStaff getOfficeStaff() {
//        return officeStaff;
//    }
//
//    public void setOfficeStaff(OfficeStaff officeStaff) {
//        this.officeStaff = officeStaff;
//    }
//
//    public String getInformation() {
//        return information;
//    }
//
//    public void setInformation(String information) {
//        this.information = information;
//    }
//
//    public Date getVacationFrom() {
//        return VacationFrom;
//    }
//
//    public void setVacationFrom(Date VacationFrom) {
//        this.VacationFrom = VacationFrom;
//    }
//
//    public Date getVacationTo() {
//        return VacationTo;
//    }
//
//    public void setVacationTo(Date VacationTo) {
//        this.VacationTo = VacationTo;
//    }
//
//
//}
