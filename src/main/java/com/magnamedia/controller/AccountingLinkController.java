package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.AccountingLink;
import com.magnamedia.entity.Contract;
import com.magnamedia.repository.AccountingLinkRepository;
import com.magnamedia.repository.ContractRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/accountingLink")
public class AccountingLinkController extends BaseRepositoryController<AccountingLink> {

    @Autowired
    private AccountingLinkRepository accountingLinkRepository;

    @Override
    public BaseRepositoryParent<AccountingLink> getRepository() {
        return accountingLinkRepository;
    }

//    @PreAuthorize("hasPermission('accountingLink','getLinkStatus')")
//    @GetMapping("/getLinkStatus/{uuid}")
//    public ResponseEntity<?> getLinkStatus(@PathVariable("uuid") String linkUuid) {
//
//        return ResponseEntity.ok(AccountingLinkService.getLinkStatus(linkUuid));
//    }

    @NoPermission
    @GetMapping("/getLastSignDdLink")
    public Map<String, String> getLastSignDdLink(String contractUuid){
        Map<String, String> r = new HashMap<>();
        Contract c = Setup.getRepository(ContractRepository.class).findByUuid(contractUuid);
        if (c == null) return r;

        try {
            AccountingLink a = accountingLinkRepository.findTopByContractIdAndTypeOrderByCreationDateDesc(c.getId(), AccountingLink.AccountingLinkType.SIGN_DD_WEB_PAGE);
            if (a == null) return r;

            String url = a.getOriginalLink();
            int contractUUidIndex = url.indexOf(contractUuid);
            if (contractUUidIndex != -1) {
                url = url.substring(contractUUidIndex);
            }

            // Split the URL into parts based on '&'
            String[] parts = url.split("&");

            // Iterate over the parts and populate the map
            for (String part : parts) {
                String[] keyValue = part.split("=");
                if (keyValue.length == 2) {
                    r.put(keyValue[0], keyValue[1]);
                }
            }
        } catch (Exception e) {
           e.printStackTrace();
           return new HashMap<>();
        }
        return r;
    }
}