package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.projection.DEWAProjection;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.module.type.ContractStatus;

import java.io.InputStream;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Esrawi <<EMAIL>>
 *         Created at Mar 14, 2018
 */
@RequestMapping("/dewa")
@RestController
public class DEWAController extends BaseController {

    @Autowired
    ProjectionFactory projectionFactory;

    static final Logger logger = Logger.getLogger(
            BaseRepositoryController.class.getName());


    @PreAuthorize("hasPermission('dewa','getDEWA')")
    @RequestMapping(path = "/getDEWA",
            method = RequestMethod.GET)
    public ResponseEntity<?> getDEWA(
            Sort sort,
            @RequestParam(name = "limit",
                    required = false) Integer limit,
            @RequestParam(name = "search",
                    required = false) String queryString, Pageable pageable) {

        SelectQuery<Contract> query = new SelectQuery<>(Contract.class);
        query.leftJoinFetch("client");
        query.leftJoinFetch("housemaid");
        query.filterBy("status", "=", ContractStatus.ACTIVE);

        if (queryString != null && !queryString.isEmpty()) {
            query.filterBy(
                    new SelectFilter("client.name", "like", "%" + queryString + "%")
                            .or("housemaid.name", "like", "%" + queryString + "%")
                            .or("client.dewaNumber", "like", "%" + queryString + "%")
            );
        }
        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("housemaid.name", true, false);
        }

        return new ResponseEntity<>(query.execute(pageable).map(obj -> projectionFactory.createProjection(DEWAProjection.class, obj)), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('dewa','getDEWA_CSV')")
    @RequestMapping(path = "/getDEWA/csv",
            method = RequestMethod.GET)
    public void getDEWA_CSV(
            Sort sort,
            @RequestParam(name = "limit",
                    required = false) Integer limit,
            @RequestParam(name = "search",
                    required = false) String queryString,
            HttpServletResponse response
    ) throws Exception {
        if (limit == null) {
            limit = 2000;
        }
        SelectQuery<Contract> query = new SelectQuery<>(Contract.class);

        query.leftJoinFetch("client");
        query.leftJoinFetch("housemaid");
        query.filterBy("status", "=", ContractStatus.ACTIVE);

        if (queryString != null && !queryString.isEmpty()) {
            query.filterBy(
                    new SelectFilter("client.name", "like", "%" + queryString + "%")
                            .or("housemaid.name", "like", "%" + queryString + "%")
                            .or("client.dewaNumber", "like", "%" + queryString + "%")
            );
        }
        //Sorting
        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("housemaid.name", true, false);
        }

        InputStream is = null;
        try {
            String[] namesOrdared = {"housemaidName", "housemaidMol", "clientName", "clientDewa", "living"};
            is = generateCsv(query, DEWAProjection.class, namesOrdared, limit);
            createDownloadResponse(response, "DEWA.csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }   
    }
}
