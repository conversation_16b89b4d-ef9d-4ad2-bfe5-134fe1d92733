package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.BaseReportCompany;
import com.magnamedia.entity.PLCompany;
import com.magnamedia.entity.PLVariableBucket;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.report.BaseReport;
import com.magnamedia.report.PNL_Report;
import com.magnamedia.report.PNL_T_Report;
import com.magnamedia.repository.BasePLNodeRepository;
import com.magnamedia.repository.BasePLVariableBucketRepository;
import com.magnamedia.repository.PLNodeRepository;
import com.magnamedia.repository.PLVariableBucketRepository;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.io.IOUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> kanaan <<EMAIL>> Created on Oct 1, 2018
 */
@RequestMapping("/plcompanies")
@RestController
public class PLCompanyController extends BaseCompanyReportController<PLCompany> {

    // ACC-496 | API to show the transactions behind a row in p&l page | Majd Bousaad
    @PreAuthorize("hasPermission('plcompanies','downloadreport')")
    @RequestMapping(value = "/showtransactionsbehindrow/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> showTransactionsBehindRow(
            @PathVariable(value = "id") Long nodeId,
            @RequestParam(required = false, value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(required = false, value = "toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @RequestParam(value = "searchCriteria", defaultValue = "PNL_DATE") BaseCompanyReportController.SearchCriteria searchCriteria) {

        BasePLNode baseNode = (BasePLNode) getBaseNodeRepository().findOne(nodeId);
        PNL_T_Report pNL_T_Report = new PNL_T_Report(baseNode, fromDate, toDate, searchCriteria);

        try {
            return new ResponseEntity<>(pNL_T_Report.render(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(ExceptionUtils.getStackTrace(e), HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    protected Class<PLCompany> getEntityClass() {
        return PLCompany.class;
    }

    @Override
    protected BaseReport getReport(BaseReportCompany pLCompany, Date fromDate, Date toDate, boolean isColored, int levels, String baseUrl, String format, boolean showFormula, boolean withRounding
            , SearchCriteria searchCriteria) throws Exception {
        return new PNL_Report(pLCompany, fromDate, toDate, isColored, levels, baseUrl, format.toString(), withRounding, searchCriteria);
    }

    @Override
    public void generateExcelReportFile(HttpServletResponse response, PLCompany pLCompany, Date fromDate, Date toDate, boolean isColored, Integer maxLevel, boolean showFormula, boolean withRounding) throws Exception {

        DateTime now = new DateTime(fromDate);
        DateTime before1MonthDate = now.minusMonths(1).withDayOfMonth(1);
        DateTime before2MonthDate = now.minusMonths(2).withDayOfMonth(1);
        DateTime before3MonthDate = now.minusMonths(3).withDayOfMonth(1);

        List<BasePLNode> headNodes = pLCompany.getSortedChildren();
        XSSFWorkbook workbook = new XSSFWorkbook();

        XSSFFont boldTitleFont = workbook.createFont();
        boldTitleFont.setBold(true);
        boldTitleFont.setFontHeightInPoints((short) 16);

        XSSFFont boldFont = workbook.createFont();
        boldFont.setBold(true);

        DataFormat fmt = workbook.createDataFormat();

        XSSFSheet spreadsheet = workbook.createSheet("Sheet1");
        spreadsheet.setColumnWidth(0, 25 * 256);
        spreadsheet.setColumnWidth(1, 25 * 256);
        spreadsheet.setColumnWidth(2, 25 * 256);
        spreadsheet.setColumnWidth(3, 25 * 256);
        spreadsheet.setColumnWidth(4, 25 * 256);
        spreadsheet.setColumnWidth(5, 80 * 256);

        int rowId = 0;

        // Header Text Row
        buildTableNameRow(workbook, spreadsheet, pLCompany.getReportTitle(), fmt, boldFont, rowId);
        buildTableNameRow(workbook, spreadsheet, "From Date: " + com.magnamedia.helper.DateUtil.formatFullDate(fromDate), fmt, boldFont, ++rowId);
        buildTableNameRow(workbook, spreadsheet, "To Date: " + com.magnamedia.helper.DateUtil.formatFullDate(toDate), fmt, boldFont, ++rowId);

        //Summary Table
        buildSpacerRow(spreadsheet, ++rowId);
        buildTableNameRow(workbook, spreadsheet, "Summary Table", fmt, boldFont, ++rowId);
        buildHeadTableRow(
                workbook,
                spreadsheet,
                new String[]{"Name", "Amount (AED)", "Expense Ratio (Expense Total / Revenues)", ""},
                fmt,
                boldFont,
                ++rowId);

        Map<String, Double> inputVatMap = getInputVatMap(pLCompany, fromDate, toDate);
        Map<String, Double> outputVatMap = getOutputVatMap(pLCompany, fromDate, toDate);
        //Summary Table Body
        for (BasePLNode pLNode : headNodes) {
            rowId = buildNodeBlock(fromDate, toDate, inputVatMap, outputVatMap,
                    workbook, pLNode, spreadsheet, rowId, 1, maxLevel, true, isColored, fmt, boldFont, true, withRounding);
        }

        buildTotalProfitRow(pLCompany, spreadsheet, ++rowId, workbook, fmt, boldFont, outputVatMap, inputVatMap, withRounding);
        //Jirra ACC-1389
        buildAccrualProfitRow(pLCompany, spreadsheet, ++rowId, workbook, fmt, boldFont, outputVatMap, inputVatMap, withRounding);

        //Details Table
        for (BasePLNode pLNode : headNodes) {

            buildSpacerRow(spreadsheet, ++rowId);
            buildTableNameRow(workbook, spreadsheet, pLNode.getName(), fmt, boldFont, ++rowId);
            buildHeadTableRow(
                    workbook,
                    spreadsheet,
                    new String[]{"Name", "Amount (AED)", DateUtil.formatMonth(before1MonthDate.toDate()), DateUtil.formatMonth(before2MonthDate.toDate()),
                            DateUtil.formatMonth(before3MonthDate.toDate()), "Average", "Profit Adjustment", "Ratio", "Formula"},
                    fmt,
                    boldFont,
                    ++rowId);
            //Details Table Body
            rowId = buildNodeBlock(fromDate, toDate, inputVatMap, outputVatMap,
                    workbook, pLNode, spreadsheet, rowId, null, maxLevel, false, isColored, fmt, boldFont, true, withRounding);

//            if (!vatRowsAdded) {
//                buildTotalOutputVATCollectedRow(pLCompany, spreadsheet, ++rowId, workbook, fmt, boldFont);
//                buildTotalInputVATCollectedRow(pLCompany, spreadsheet, ++rowId, workbook, fmt, boldFont);
//                vatRowsAdded = true;
//            }
        }

        //File
        File file = Paths.get(System.getProperty("java.io.tmpdir"),
                new Date().getTime() + "")
                .toFile();
        FileOutputStream out = new FileOutputStream(file);
        workbook.write(out);
        out.close();
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition",
                "attachment; filename=\""
                        + pLCompany.getName()
                        + ".xlsx"
                        + "\"");
        try {
            IOUtils.copy(
                    new FileInputStream(file),
                    response.getOutputStream());

            response.getOutputStream().flush();

        } catch (IOException ex) {
            logger.log(
                    Level.SEVERE,
                    ex.getMessage(),
                    ex);
        }
    }

    @Override
    public BasePLNodeRepository getBaseNodeRepository() {
        return Setup.getRepository(PLNodeRepository.class);
    }

    @Override
    protected Class getVariableBucketEntityClass() {
        return PLVariableBucket.class;
    }

    @Override
    public BasePLVariableBucketRepository getBasePLVariableBucketRepository() {
        return Setup.getRepository(PLVariableBucketRepository.class);
    }
}
