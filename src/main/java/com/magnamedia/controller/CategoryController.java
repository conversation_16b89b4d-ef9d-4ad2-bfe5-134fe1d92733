package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.Item;
import com.magnamedia.repository.CategoryRepository;
import com.magnamedia.repository.ItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <PERSON> (Jan 26, 2021)
 */
@RestController
@RequestMapping("/category")
public class CategoryController extends BaseRepositoryController<Category> {
    @Autowired
    CategoryRepository categoryRepository;

    @Override
    public BaseRepository<Category> getRepository() {
        return categoryRepository;
    }

    @Autowired
    ItemRepository itemRepository;

    @PreAuthorize("hasPermission('category','get-items')")
    @RequestMapping("/get-items/{categoryId}")
    public ResponseEntity<?> getItems(
            @PathVariable("categoryId") Category category, Pageable pageable) {
        Page<Item> result = itemRepository.findByCategoryOrderByCreationDateDesc(category, pageable);
        return ResponseEntity.ok(result);
    }
}
