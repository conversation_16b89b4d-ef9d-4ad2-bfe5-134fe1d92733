package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDFBatchForRPA;
import com.magnamedia.entity.DDFExportingConfig;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.extra.DDFAsCsvHelper;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.module.type.DDFBatchStatus;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DDFBatchForRPARepository;
import com.magnamedia.repository.DDFExportingConfigRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.stream.Collectors;


/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Sep 25, 2020
 */
@RestController
@RequestMapping("/ddf_batch_for_rpa")
public class DDFBatchForRPAController extends BaseRepositoryController<DDFBatchForRPA> {

    @Autowired
    DDFBatchForRPARepository dDFBatchForRPARepository;

    @Autowired
    DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitFileController directDebitFileController;

    @Autowired
    AttachementRepository attachementRepository;

    @Autowired
    private DDFExportingConfigRepository ddfExportingConfigRepository;

    @Override
    public BaseRepository<DDFBatchForRPA> getRepository() {
        return dDFBatchForRPARepository;
    }

    @PreAuthorize("hasPermission('ddf_batch_for_rpa','advancesearch')")
    @RequestMapping(value = "/advancesearch",
            method = RequestMethod.POST)
    public ResponseEntity<?> advanceSearch(
            @RequestBody List<FilterItem> filters,
            Pageable pageable) {

        SelectQuery<DDFBatchForRPA> query = new SelectQuery<>(DDFBatchForRPA.class);

        //Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(DDFBatchForRPA.class));
        }

        query.filterBy(selectFilter);

        return new ResponseEntity(query.execute(pageable), HttpStatus.OK);
    }

    @Transactional
    public void excludeDDFFromRPABatch(DirectDebitFile f) throws Exception {
        List<DDFBatchForRPA> ddfBatchForRPAs = dDFBatchForRPARepository.findByIdsContainsAndStatusNotIn(
                f.getId().toString(), Arrays.asList(DDFBatchStatus.SENT_TO_BANK, DDFBatchStatus.ERROR_OCCURRED_SENT_TO_BANK));

        for (DDFBatchForRPA ddfBatchForRPA : ddfBatchForRPAs) {
            // ACC-3198
            if (!f.isForceUpdate() && !ddfBatchForRPA.getStatus().equals(DDFBatchStatus.NOT_SENT)) {
                throw new RuntimeException("DD is under RPA process, it cannot be cancelled");
            }

            List<Long> ids = Arrays.stream(ddfBatchForRPA.getIds().split(",")).map(x -> Long.parseLong(x.trim())).collect(Collectors.toList());
            List<DirectDebitFile> directDebitFiles = directDebitFileRepository.findAll(ids);

            List<DirectDebitFile> newDirectDebitFiles = directDebitFiles.stream()
                    .filter(ddf -> !ddf.getDdStatus().equals(DirectDebitStatus.CANCELED) &&
                            ddf.getAttachment(DirectDebitFile.FILE_TAG_DD_ACTIVATION) != null)
                    .collect(Collectors.toList());
            List<Long> newIDs = newDirectDebitFiles.stream().map(ddf -> ddf.getId()).collect(Collectors.toList());

            Attachment csvAttachmentOld = ddfBatchForRPA.getAttachment(DirectDebitFileController.DDF_BATCH_FILE_CSV);
            Attachment rarAttachmentOld = ddfBatchForRPA.getAttachment(DirectDebitFileController.DDF_BATCH_FILE_RAR);

            if (csvAttachmentOld == null || rarAttachmentOld == null) continue;

            if (!newDirectDebitFiles.isEmpty()) {
                DDFExportingConfig ddfExportingConfig;
                if (directDebitFiles.get(0).getDdMethod().equals(DirectDebitMethod.AUTOMATIC)) {
                    ddfExportingConfig = ddfExportingConfigRepository.findFirstByName("Automatic");
                } else {
                    ddfExportingConfig = ddfExportingConfigRepository.findFirstByName("Manual");
                }

                DDFAsCsvHelper ddfBeans = new DDFAsCsvHelper(ddfExportingConfig);
                File csvFile = ddfBeans.generateCsvFile(newIDs, csvAttachmentOld.getName());

                File zipFile = directDebitFileController.generateBatchZipFile(newDirectDebitFiles);

                Attachment csvAttachment = Storage.storeTemporary("dds.rar", new FileInputStream(zipFile), DirectDebitFileController.DDF_BATCH_FILE_RAR, true);
                Attachment rarAttachment = Storage.storeTemporary(csvAttachmentOld.getName(), new FileInputStream(csvFile), DirectDebitFileController.DDF_BATCH_FILE_CSV, true);
                ddfBatchForRPA.addAttachment(csvAttachment);
                ddfBatchForRPA.addAttachment(rarAttachment);
                ddfBatchForRPA.setIds(newIDs.toString().substring(1, newIDs.toString().length() - 1));
                getRepository().save(ddfBatchForRPA);

                // ACC-9315
                newDirectDebitFiles.forEach(ddf -> {
                    ddf.setDdfBatchForRpaId(ddfBatchForRPA.getId());
                });
                directDebitFileRepository.save(newDirectDebitFiles);

                attachementRepository.delete(csvAttachmentOld);
                attachementRepository.delete(rarAttachmentOld);
            } //Jirra ACC-2815
            else {
                logger.log(Level.SEVERE, "DDFBatch#" + ddfBatchForRPA.getId() + " has became empty -> to be Deleted");
                getRepository().delete(ddfBatchForRPA);
            }
        }
    }
}