/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.AdsSpending;
import com.magnamedia.repository.AdsSpendingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ads")
public class AdsSpendingController extends BaseRepositoryController<AdsSpending> {

    @Autowired
    AdsSpendingRepository adsSpendingRepository;

    @Override
    public BaseRepository<AdsSpending> getRepository() {
        return adsSpendingRepository;
    }

}
