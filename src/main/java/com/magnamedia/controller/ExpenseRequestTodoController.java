package com.magnamedia.controller;


import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.annotation.caching.ApiCacheable;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.*;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.ExpenseRequestForMaidByGPTDTO;
import com.magnamedia.entity.dto.ExpenseRequestRefundSearchDto;
import com.magnamedia.entity.dto.ExpenseRequestSearchDto;
import com.magnamedia.entity.dto.ExpenseRequestTodoSearchApiDto;
import com.magnamedia.entity.projection.*;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.*;
import com.magnamedia.extra.ExpenseFlow.CovidTestExpenseRequestDto;
import com.magnamedia.extra.ExpenseFlow.ParsedTransportationExpenseAttachmentDto;
import com.magnamedia.extra.ExpenseFlow.TransportationExpenseValidationDto;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.helper.UserHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import com.magnamedia.service.ExpenseRequestService;
import com.magnamedia.workflow.entity.projection.ClientRefundFlowProjection;
import com.magnamedia.workflow.service.ExpenseRequestFlow;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.workflow.type.ExpenseRequestStatus;
import com.magnamedia.workflow.type.ExpenseRequestTodoFlowActions;
import com.magnamedia.workflow.type.ExpenseRequestTodoType;
import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URI;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.type.ExpenseBeneficiaryType.SUPPLIER;

/**
 * <AUTHOR> Ketrawi
 *         Created on Jan 20, 2021
 *         ACC-291
 */
@RestController
@RequestMapping("/expenseRequestTodo")
public class ExpenseRequestTodoController extends WorkflowController<ExpenseRequestTodo, ExpenseRequestFlow> {

    public static final Integer csvMaxLimit = 2000;

    @Autowired
    CreditCardReconciliationStatementParsingService parsingService;
    @Autowired
    private EmailHelper emailHelper;
    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private ExpenseRepository expenseRepository;
    @Autowired
    private ExpensesController expensesController;
    @Autowired
    private TransportationExpenseAttachmentParser transportationExpenseAttachmentParser;
    @Autowired
    private PicklistRepository picklistRepository;
    @Autowired
    private PicklistItemRepository picklistItemRepository;
    @Autowired
    private TelecomPhoneRepository telecomPhoneRepository;
    @Autowired
    private SupplierRepository supplierRepository;
    @Autowired
    private OfficeStaffRepository officeStaffRepository;
    @Autowired
    private InsuranceBalanceLogRepository insuranceBalanceLogRepository;
    @Autowired
    private CurrencyExchangeSevice currencyExchangeSevice;
    @Autowired
    private ExpenseNotificationService expenseNotificationService;
    @Autowired
    private FreedomOperatorRepository freedomOperatorRepository;
    @Autowired
    private QueryService queryService;
    @Autowired
    private ExpenseRequestTodoService expenseRequestTodoService;
    @Autowired
    private ExpensePaymentService expensePaymentService;
    @Autowired
    private HousemaidService housemaidService;

    @Autowired
    private ExpenseRequestService expenseService;

    @Override
    public BaseRepository<ExpenseRequestTodo> getRepository() {
        return expenseRequestTodoRepository;
    }

    // ACC-9568
    @UsedBy(modules = UsedBy.Modules.Staff_Management)
    @PreAuthorize("hasPermission('expenseRequestTodo', 'createExpenseRequestTodoWithCreator')")
    @PostMapping("/createExpenseRequestTodoWithCreator")
    @Transactional
    public ResponseEntity<?> createExpenseRequestTodoWithCreator(
            @RequestParam(name = "userId") Long userId,
            @RequestBody Map<String, Object> body) {

        try {
            logger.info("requestBody for API : '/expenseRequestTodo/createExpenseRequestTodoWithCreator'" + getObjectMapper().writeValueAsString(body));
        } catch (Exception e) {
            e.printStackTrace();
        }

        User user = Setup.getRepository(UserRepository.class).findOne(userId);
        ExpenseRequestTodo todo = (ExpenseRequestTodo) createExpenseRequestTodo(
                getObjectMapper().convertValue(body,
                        ExpenseRequestTodo.class), user)
                .getBody();

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put("id", todo.getId());
        }});
    }

    @Transactional
    @Override
    public ResponseEntity<?> createEntity(ExpenseRequestTodo expenseRequestTodo) {

        return createExpenseRequestTodo(expenseRequestTodo, CurrentRequest.getUser());
    }

    public ResponseEntity<?> createExpenseRequestTodo(ExpenseRequestTodo expenseRequestTodo, User user) {
        this.validateAndPrepareEntity(expenseRequestTodo, user);
        ResponseEntity<?> response = super.createEntity(expenseRequestTodo);

        expenseRequestTodo.paymentTrigger();

        if (expenseRequestTodo.getPaymentMethod() != null && expenseRequestTodo.getPaymentMethod().equals(ExpensePaymentMethod.CREDIT_CARD)) {
            expenseNotificationService.expenseToDoCreatedEmail(
                    expenseRequestTodo.getEntityType(), expenseRequestTodo.getTaskName());
        }

        return response;
    }

    private static final Logger logger = Logger.getLogger(ExpenseRequestTodoController.class.getName());

    public void validateAndPrepareEntity(ExpenseRequestTodo expenseRequestTodo, User creator) {

        logger.log(Level.SEVERE, "expenseRequestType: " + expenseRequestTodo.getExpenseRequestType());
        logger.log(Level.SEVERE, "typesCreatedFromSystemParameters: " + ExpenseRequestTodo.getTypesCreatedFromSystemParameters().contains(expenseRequestTodo.getExpenseRequestType()));

        if ((expenseRequestTodo.getExpense() == null ||
                (expenseRequestTodo.getExpense().getId() == null && expenseRequestTodo.getExpense().getCode() == null)) &&
                (expenseRequestTodo.getExpenseRequestType() == null ||
                        !ExpenseRequestTodo.getTypesCreatedFromSystemParameters()
                                .contains(expenseRequestTodo.getExpenseRequestType())))
            throw new BusinessException("Couldn't Find the proper Expense setup!");

        Supplier supplier = null;

        //VPM-2148
        if(expenseRequestTodo.getBeneficiaryType() != null) {
            switch (expenseRequestTodo.getBeneficiaryType()) {
                case SUPPLIER:
                    if (expenseRequestTodo.getBeneficiaryId() != null) {
                        supplier = supplierRepository.findOne(expenseRequestTodo.getBeneficiaryId());
                    } else if (expenseRequestTodo.getBeneficiaryName() != null) {
                        supplier = supplierRepository.findFirstByName(expenseRequestTodo.getBeneficiaryName());
                    }

                    if (supplier == null) {
                        throw new BusinessException("No supplier found with name '" + expenseRequestTodo.getBeneficiaryName() +
                                "'; with ID '" + expenseRequestTodo.getBeneficiaryId() + "'");
                    }

                    expenseRequestTodo.setBeneficiaryId(supplier.getId());
                    expenseRequestTodo.setBeneficiaryName(supplier.getName());
                    expenseRequestTodo.setBeneficiaryHasNoIban(supplier.getHasNoIban());
                    expenseRequestTodo.setBeneficiaryIban(supplier.getIban());

                    if (expenseRequestTodo.getPaymentMethod() == null) {
                        if (supplier.getFirstPaymentMethod() == null) {
                            throw new BusinessException(supplier.getName() + " supplier must have payment method.");
                        }
                        expenseRequestTodo.setPaymentMethod(supplier.getFirstPaymentMethod());
                    } else if (supplier.getPaymentMethods().isEmpty() ||
                            !supplier.getPaymentMethods().contains(expenseRequestTodo.getPaymentMethod())) {
                        throw new BusinessException("The payment method you chose is not defined in the supplier profile. " +
                                "Please update the supplier’s profile with this payment method to proceed.");
                    }
                    break;

                case OFFICE_STAFF:
                    if (expenseRequestTodo.getBeneficiaryId() == null)
                        throw new BusinessException("Office Staff should be filled");

                    OfficeStaff staff = officeStaffRepository.findOne(expenseRequestTodo.getBeneficiaryId());

                    if (staff == null) {
                        throw new BusinessException("No staff found with ID: " + expenseRequestTodo.getBeneficiaryId());
                    }

                    expenseRequestTodo.setBeneficiaryName(staff.getName());
                    expenseRequestTodo.setBeneficiaryHasNoIban(staff.getIban() == null);
                    expenseRequestTodo.setBeneficiaryIban(staff.getIban());
                    expenseRequestTodo.setBeneficiaryAccountName(staff.getAccountName());
                    expenseRequestTodo.setBeneficiaryAccountNumber(staff.getAccountNumber());
                    expenseRequestTodo.setAddress(staff.getFullAddress());

                    break;
            }
        }

        Expense expense = null;

        if (expenseRequestTodo.getCurrency() == null)
            expenseRequestTodo.setCurrency(currencyExchangeSevice.getLocalCurrency());

        expenseRequestTodo.setAmountsTrigger();

        // ACC-3557
        if(expenseRequestTodo.getExpenseRequestType() != null) switch(expenseRequestTodo.getExpenseRequestType()) {
            case ONE_TIME:
                if (expenseRequestTodo.getExpense().getId() != null) {
                    expense = expenseRepository.findOne(expenseRequestTodo.getExpense().getId());
                } else if(expenseRequestTodo.getExpense().getCode() != null) {
                    expense = expenseRepository.findByCodeAndDeletedFalse(expenseRequestTodo.getExpense().getCode());
                }

                /*supplier = supplierRepository.findOne(expenseRequestTodo.getBeneficiaryId());
                if (supplier == null)
                    throw new BusinessException("Supplier can't be null");
                if (supplier.getPaymentMethod() == null)
                    throw new BusinessException(supplier.getName() + " supplier must have payment method.");

                expenseRequestTodo.setPaymentMethod(supplier.getPaymentMethod());*/
                break;

            case TRANSPORTATION:
                String transportationExpenseCode = Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_TRANSPORTATION_EXPENSE_CODE);

                expense = expenseRepository.findOneByCode(transportationExpenseCode);
                if (expense == null)
                    throw new BusinessException("Transportation expense setup doesn't exist, please add it with code " + transportationExpenseCode + ".");

                Double taxiOrdersAmounts = 0D;
                BillType billType = BillType.HALA;

                supplier = supplierRepository.findOne(expenseRequestTodo.getBeneficiaryId());
                if (supplier == null) throw new BusinessException("Supplier can't be null");

                String hala = Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_HALA_SUPPLIER_NAME);
                String careem = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CAREEM_SUPPLIER_NAME);

                if (supplier.getName().equals(careem))
                    billType = BillType.CAREEM;
                else if (!supplier.getName().equals(hala))
                    throw new BusinessException("Supplier should be " + hala + " or " + careem);

                taxiOrdersAmounts = transportationExpenseAttachmentParser.getTaxiAmountSum(billType, expenseRequestTodo.getRequestFrom(), expenseRequestTodo.getRequestTo());

           if (Math.abs(taxiOrdersAmounts - expenseRequestTodo.getAmount()) > 100 )
                    throw new BusinessException("Please attach audit excel sheet , difference is bigger than 100!");
                if (!expenseRequestTodoRepository.findByExpenseAndInvoiceNumberOrderByCreationDateAsc(expense, expenseRequestTodo.getInvoiceNumber()).isEmpty())
                    throw new BusinessException("Invoice number is not unique!");

                expenseRequestTodo.setInvoiceAttached(true);

                if (supplier.getVatRegistered() != null && supplier.getVatRegistered())
                    expenseRequestTodo.setAttachedInvoiceIsValidVatInvoice(true);
                break;

            case TAXI_REIMBURSEMENT:
                String taxiExpenseCode = "";
                if (expenseRequestTodo.getRelatedToType() == null)
                    throw new BusinessException("Please define the Related Type.");
                else if (expenseRequestTodo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.MAID)
                    taxiExpenseCode = Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_TAXI_REIMBURSEMENT_EXPENSE_CODE);
                else if (expenseRequestTodo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.APPLICANT)
                    taxiExpenseCode = Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_TAXI_REIMBURSEMENT_APPLICANT_EXPENSE_CODE);
                else throw new BusinessException("Related Type should be Maid or Applicant.");

                expense = expenseRepository.findOneByCode(taxiExpenseCode);
                if (expense == null)
                    throw new BusinessException("Taxi expense setup doesn't exist, please add it with code " + taxiExpenseCode + ".");
                if (expense.getBeneficiaryType() == null || !expense.getBeneficiaryType().equals(ExpenseBeneficiaryType.TAXI_DRIVER))
                    throw new BusinessException(expense.getName() + " expense beneficiary type should be taxi driver ");

                expenseRequestTodo.setBeneficiaryType(ExpenseBeneficiaryType.TAXI_DRIVER);
                break;

            case DEWA:
            case TELECOM:
                if (expenseRequestTodo.getExpenseRequestType().equals(ExpenseRequestType.DEWA)) {
                    expense = telecomPhoneRepository.findOne(expenseRequestTodo.getTelecomPhones().get(0).getId()).getPrimaryExpense();
                } else if (expenseRequestTodo.getExpenseRequestType().equals(ExpenseRequestType.TELECOM)) {
                    if (expenseRequestTodo.getExpense().getId() != null || expenseRequestTodo.getExpense().getCode() != null) {
                        expense = expenseRequestTodo.getExpense().getId() != null ? expenseRepository.findOne(expenseRequestTodo.getExpense().getId()) : expenseRepository.findByCodeAndDeletedFalse(expenseRequestTodo.getExpense().getCode());
                    }
                }

                if (expense.getBeneficiaryType() == null || !expense.getBeneficiaryType().equals(SUPPLIER))
                    throw new BusinessException(expense.getName() + " expense beneficiary type should be Supplier");
                if (expense.getSuppliers() == null || expense.getSuppliers().isEmpty() || expense.getSuppliers().size() > 1)
                    throw new BusinessException(expense.getName() + " expense must have one supplier");
                if (expense.getPaymentMethods() == null || expense.getPaymentMethods().isEmpty() || expense.getPaymentMethods().size() > 1)
                    throw new BusinessException(expense.getName() + " expense must have one payment method");

                expenseRequestTodo.setBeneficiaryId(expense.getSuppliers().get(0).getId());
                expenseRequestTodo.setBeneficiaryType(SUPPLIER);
                expenseRequestTodo.setPaymentMethod(expense.getPaymentMethods().iterator().next());
                break;

            case INSURANCE:
                String insuranceExpenseCode = Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_INSURANCE_EXPENSE_CODE);

                expense = expenseRepository.findOneByCode(insuranceExpenseCode);

                if (expense == null)
                    throw new BusinessException("Taxi expense setup doesn't exist, please add it with code " + insuranceExpenseCode + ".");
                if (expense.getBeneficiaryType() == null || !expense.getBeneficiaryType().equals(SUPPLIER))
                    throw new BusinessException(expense.getName() + " expense beneficiary type should be Supplier");
                if (expense.getSuppliers() == null || expense.getSuppliers().isEmpty() || expense.getSuppliers().size() > 1)
                    throw new BusinessException(expense.getName() + " expense must have one supplier");

                supplier = expense.getSuppliers().get(0);
                expenseRequestTodo.setBeneficiaryId(supplier.getId());
                expenseRequestTodo.setBeneficiaryType(SUPPLIER);

                InsuranceBalanceLog insuranceBalanceLog = insuranceBalanceLogRepository.findTopByOrderByLogDateDesc();
                if (insuranceBalanceLog != null && insuranceBalanceLog.getBalance() < expenseRequestTodo.getAmount())
                    throw new BusinessException("According to Insurance Log, request amount can't be more than " + insuranceBalanceLog.getBalance());

                //expenseRequestTodo.setInvoiceAttached(true);
                if (supplier.getVatRegistered() != null && supplier.getVatRegistered())
                    expenseRequestTodo.setAttachedInvoiceIsValidVatInvoice(true);
                break;

            case MAID_PAYMENT:
                if (expenseRequestTodo.getExpense().getId() != null) {
                    expense = expenseRepository.findOne(expenseRequestTodo.getExpense().getId());
                } else if(expenseRequestTodo.getExpense().getCode() != null) {
                    expense = expenseRepository.findByCodeAndDeletedFalse(expenseRequestTodo.getExpense().getCode());
                }

                if (expense == null)
                    throw new BusinessException("Couldn't Find the proper Expense setup!");

                expenseRequestTodo.setExpense(expense);

                if (expenseRequestTodo.getRelatedToId() == null || expenseRequestTodo.getBeneficiaryId() == null)
                    throw new BusinessException(expense.getName() + "No maid selected.");

                if (!expense.getBeneficiaryType().equals(ExpenseBeneficiaryType.MAID))
                    throw new BusinessException("Beneficiary Type of Medical Expense should be only Maid.");

                expenseRequestTodo.setBeneficiaryType(ExpenseBeneficiaryType.MAID);
                expenseRequestTodo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.MAID);
                break;

            case MEDICAL:
                if (expenseRequestTodo.getExpense().getId() != null) {
                    expense = expenseRepository.findOne(expenseRequestTodo.getExpense().getId());
                } else if(expenseRequestTodo.getExpense().getCode() != null) {
                    expense = expenseRepository.findByCodeAndDeletedFalse(expenseRequestTodo.getExpense().getCode());
                }

                if (expense == null) throw new BusinessException("Couldn't Find the proper Expense setup!");

                if (!Arrays.asList(ExpenseBeneficiaryType.MAID, ExpenseBeneficiaryType.OFFICE_STAFF)
                        .contains(expense.getBeneficiaryType())) {
                    throw new BusinessException("Beneficiary Type of Medical Expense should be Maid or Office Staff.");
                }

                expenseRequestTodo.setBeneficiaryType(expense.getBeneficiaryType());
                expenseRequestTodo.setBeneficiaryId(expenseRequestTodo.getRelatedToId());
                break;

            case TICKETING:
                if (expenseRequestTodo.getExpense().getId() != null) {
                    expense = expenseRepository.findOne(expenseRequestTodo.getExpense().getId());
                } else if(expenseRequestTodo.getExpense().getCode() != null) {
                    expense = expenseRepository.findByCodeAndDeletedFalse(expenseRequestTodo.getExpense().getCode());
                }
                if (expense != null) {
                    expenseRequestTodo.setExpense(expense);
                    if (expense.getPaymentMethods() == null ||
                            expense.getPaymentMethods().isEmpty() ||
                            expense.getPaymentMethods().size() > 1)
                        throw new BusinessException("Expense must have one payment method");

                    if (!expense.getBeneficiaryType().equals(SUPPLIER))
                        throw new BusinessException("Beneficiary Type of TICKETING Expense should be only SUPPLIER.");

                    expenseRequestTodo.setPaymentMethod(expense.getPaymentMethods().iterator().next());
                }
                break;

            default:
                if (expenseRequestTodo.getExpense().getId() != null) {
                    expense = expenseRepository.findOne(expenseRequestTodo.getExpense().getId());
                } else if(expenseRequestTodo.getExpense().getCode() != null) {
                    expense = expenseRepository.findByCodeAndDeletedFalse(expenseRequestTodo.getExpense().getCode());
                }
        }

        //VPM-2148
        if(expenseRequestTodo.getPaymentMethod() == null && expense != null) {
            if (expense.getPaymentMethods() == null || expense.getPaymentMethods().size() != 1)
                throw new BusinessException(expense.getName() + " expense must have one payment method");

            expenseRequestTodo.setPaymentMethod(expense.getPaymentMethods().iterator().next());
        }

        if (expenseRequestTodo.getExpenseToPost() != null &&
                expenseRequestTodo.getExpenseToPost().getName() != null) {

            Expense subExpense = expensesController.createSubExpense(
                    expense, expenseRequestTodo.getExpenseToPost().getName());
            expenseRequestTodo.setExpenseToPost(expenseRepository.save(subExpense));
        }

        expenseRequestTodo.setExpense(expense);

        List<User> requesters = expense.getRequestors();
        if (requesters != null && !requesters.isEmpty() &&
                !requesters.stream().anyMatch(user -> user.getId().equals(creator.getId())) &&
                (expenseRequestTodo.getExpenseRequestType() == null ||
                        !expenseRequestTodo.getExpenseRequestType().equals(ExpenseRequestType.COVID_LASER_TEST)))
            throw new BusinessException("you are not authorized!");

        if (expenseRequestTodo.getRequestedBy() == null)
            expenseRequestTodo.setRequestedBy(creator);

        expenseRequestTodo.setStatus(ExpenseRequestStatus.PENDING);
        Logger logger = Logger.getLogger("Expense Request Todo Entity.");
        logger.log(Level.INFO, "Before AUTO_APPROVED");

        if (expenseRequestTodo.getExpenseRequestType() != null && expense.getApprovalMethod() != null &&
                !Arrays.asList(ExpenseRequestType.PURCHASING, ExpenseRequestType.AUTO_DEDUCT)
                        .contains(expenseRequestTodo.getExpenseRequestType())) {

            switch (expense.getApprovalMethod()) {
                case AUTO_APPROVED: {
                    logger.log(Level.INFO, "AUTO_APPROVED");
                    expenseRequestTodo.moveToCooApprovalStep(false);
                    break;
                }

                case APPROVAL_REQUIRED:
                case APPROVAL_REQUIRED_ON_LIMIT: {
                    logger.log(Level.INFO, "APPROVAL_REQUIRED, APPROVAL_REQUIRED_ON_LIMIT");
                    if ((expense.getApprovalMethod().equals(ExpenseApprovalMethod.APPROVAL_REQUIRED) || expense.limitExceeded(expenseRequestTodo.getAmountInLocalCurrency())) && (expenseRequestTodo.getExpenseRequestType() == null || !expenseRequestTodo.getExpenseRequestType().equals(ExpenseRequestType.MAINTENANCE))) {
                        User approval = expense.getApproveHolder();
                        logger.log(Level.INFO, "APPROVAL_REQUIRED");
                        switch (expense.getApproveHolderType()) {
                            case FINAL_MANAGER: {
                                logger.log(Level.INFO, "FINAL_MANAGER");
                                User finalManager = null;
                                OfficeStaff creatorStaff = creator.getRelatedEntityId() != null && creator.getRelatedEntityType() != null && creator.getRelatedEntityType().equals("OfficeStaff") ?
                                        officeStaffRepository.findOne(creator.getRelatedEntityId()) : null;
                                if (creatorStaff == null)
                                    throw new BusinessException("Sorry, we couldn't locate your final manager-office staff");

                                finalManager = creatorStaff.getFinalManagerUser();

                                if (finalManager == null)
                                    throw new BusinessException("Sorry, we couldn't locate your final manager");

                                else if (finalManager.getId().equals(creator.getId())) {
                                    logger.info("Manager is the Creator");
                                    expenseRequestTodo.moveToCooApprovalStep(false);
                                } else if (finalManager.hasPosition(AccountingModule.EXPENSE_COO_USER_POSITION)) {
                                    logger.info("Manager has COO Position");
                                    expenseRequestTodo.moveToCooApprovalStep(true);
                                } else {
                                    expenseRequestTodo.setApproveHolder(finalManager);
                                    expenseRequestTodo.setTaskName(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
                                }

                                break;
                            }
                            case USER: {
                                logger.log(Level.INFO, "USER");
                                if (approval != null && approval.getId().equals(creator.getId())) {
                                    expenseRequestTodo.addApproval(creator.getName());
                                    expenseRequestTodo.moveToCooApprovalStep(false);
                                } else if (approval.hasPosition(AccountingModule.EXPENSE_COO_USER_POSITION)) {
                                    logger.info("Approver has COO Position");
                                    expenseRequestTodo.moveToCooApprovalStep(true);
                                } else {
                                    expenseRequestTodo.setApproveHolder(approval);
                                    expenseRequestTodo.setTaskName(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
                                }
                                break;
                            }
                            case EMAIL: {
                                logger.log(Level.INFO, "EMAIL");
                                expenseRequestTodo.setSendApprovalEmail(true);
                                expenseRequestTodo.setApproveHolderEmail(expense.getApproveHolderEmail());
                                expenseRequestTodo.setTaskName(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
                                break;
                            }
                        }
                    } else {
                        expenseRequestTodo.moveToCooApprovalStep(false);
                    }
                    break;
                }
            }
        } else if (!expenseRequestTodo.getExpenseRequestType().equals(ExpenseRequestType.AUTO_DEDUCT)) {
            logger.log(Level.INFO, "else");
            expenseRequestTodo.setStatus(ExpenseRequestStatus.PENDING_PAYMENT);
            expenseRequestTodo.setTaskName(ExpenseRequestTodoType.PAYMENT_OBJECT_CREATED.toString());
        }
    }

    @Override
    protected SelectFilter filter(SelectFilter filter, String
            search, List<String> joins, Map<String, String> joinType) {

        return filter;
    }

    @Override
    protected Class<?> getProjectionClass() {
        return ClientRefundFlowProjection.class;
    }

    // this API is only for manager
    @NoPermission
    @RequestMapping(value = "/approve-request/from-email", method = RequestMethod.GET)
    public ResponseEntity approveRequestFromEmailAPI(
            @RequestParam(name = "id") Long id,
            @RequestParam(name = "token") String token) throws Exception {

        ExpenseRequestTodo todo = getRepository().findOne(id);

        if (todo == null) {
            throw new BusinessException("The request was deleted");
        }

        if (todo.getToken() == null || !todo.getToken().equals(token))
           throw new BusinessException("You're unauthorized");

        if (!validateTakeManagerActionFromEmail(id)) {
            return new ResponseEntity(todo.getManagerAction(), HttpStatus.NOT_MODIFIED);
        }
        approveRequest(id, token, true);
        return okResponse();
    }

    // this API is for manager & Coo
    @PreAuthorize("hasPermission('expenseRequestTodo','approve-request')")
    @RequestMapping(value = "/approve-request", method = RequestMethod.GET)
    public ResponseEntity approveRequestFromAPI(
            @RequestParam(name = "id") Long id,
            @RequestParam(name = "token") String token) {

        if (approveRequest(id, token, false)) {
            return new ResponseEntity("Approved!", HttpStatus.OK);
        }

        return new ResponseEntity("Sorry, this expense request doesn't need action anymore!", HttpStatus.BAD_REQUEST);
    }

    // this API is for manager & Coo
    @PreAuthorize("hasPermission('expenseRequestTodo','approve-all-requests')")
    @RequestMapping(value = "/approve-all-requests", method = RequestMethod.POST)
    public ResponseEntity approveAllRequestsAPI(@RequestParam("gridLabel") String gridLabel, @RequestParam(value = "fromEmail", required = false) Boolean fromEmail) {

        Page<ExpenseRequestTodo> page;
        if (gridLabel.equalsIgnoreCase("Maid Addition")) {
            page = getCooMaidAdditionRequests(PageRequest.of(0, Integer.MAX_VALUE));
        } else if (gridLabel.equalsIgnoreCase("Expense Request")) {
            page = getCooExpenseRequests(PageRequest.of(0, Integer.MAX_VALUE));
        } else return new ResponseEntity("Wrong Data", HttpStatus.BAD_REQUEST);

        List<Map> requests = new ArrayList();

        for (ExpenseRequestTodo todo : page.getContent()) {
            requests.add(new HashMap() {
                {
                    put("id", todo.getId().toString());
                    put("token", todo.getToken());
                }
            });
        }

        approveList(requests, fromEmail);

        return okResponse();
    }

    // this API is for manager & Coo
    @NoPermission
    @RequestMapping(value = "/approve-list-requests", method = RequestMethod.POST)
    public ResponseEntity approveListOfRequestAPI(@RequestBody List<Map> requests, @RequestParam(value = "fromEmail", required = false) Boolean fromEmail) {

        approveList(requests, fromEmail);

        return okResponse();
    }

    public void approveList(List<Map> requests, Boolean fromEmail) {
        if (requests == null) return;

        for (Map request : requests) {
            try {
                if (!request.containsKey("id") || !request.containsKey("token")) continue;

                approveRequest(Long.parseLong(request.get("id").toString()), request.get("token").toString(), fromEmail);
            } catch (Exception e) {
                logger.severe("Failed to Approve ToDo#" + request.get("id"));
                ExceptionUtils.printStackTrace(e);
            }
        }
    }

    @Transactional
    public boolean approveRequest(Long todoId, String token, Boolean fromEmail) {
        ExpenseRequestTodo todo = getRepository().findOne(todoId);

        if (todo == null) {
            throw new RuntimeException("The request was deleted");
        }

        if (todo.getToken() == null || !todo.getToken().equals(token))
            throw new RuntimeException("You're unauthorized");

        if (todo.getTaskName() == null) return false;

        switch(todo.getTaskName()) {
            case "WAITING_MANAGER_APPROVAL":
                todo.setManagerAction(ExpenseRequestTodoFlowActions.APPROVE);
                todo.setApprovedFromEmail(fromEmail != null ? fromEmail : false);

                this.completeTask(todo, ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
                break;
            case "WAITING_COO_APPROVAL":
                todo.setCooAction(ExpenseRequestTodoFlowActions.APPROVE);
                this.completeTask(todo, ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString());
                break;
        }

        expenseNotificationService.sendExpenseRejectedApprovedRequestEmail(
                todo, todo.getNotes(), "Approved");
        return true;
    }

    // this API is for manager & Coo
    @PreAuthorize("hasPermission('expenseRequestTodo','rejectrequest')")
    @RequestMapping(value = "/rejectrequest", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity rejectRequest(
            @RequestParam(name = "id", required = false) Long id,
            @RequestParam(name = "token", required = false) String token,
            @RequestParam(name = "rejectionNotes", required = false, defaultValue = "") String rejectionNotes)
            throws Exception {

        ExpenseRequestTodo todo = getRepository().findOne(id);

        if (todo == null) {
            throw new BusinessException("The request was deleted");
        }

        if (todo.getToken() == null || !todo.getToken().equals(token))
            throw new BusinessException("You're unauthorized");

        if (todo.getTaskName() != null) {
            rejectRequest(todo, rejectionNotes);

            return ResponseEntity.ok("Done");
        }

        throw new BusinessException("Nothing was changed please try again");
    }


    // this API is for manager
    @NoPermission
    @RequestMapping(value = "/reject-request-from-email", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity rejectRequestFromEmail(
            @RequestParam(name = "id", required = false) Long id,
            @RequestParam(name = "token", required = false) String token,
            @RequestParam(name = "rejectionNotes", required = false, defaultValue = "")  String rejectionNotes,
            @RequestParam(name = "withoutRedirect", required = false, defaultValue = "false") boolean withoutRedirect)
            throws Exception {

        ExpenseRequestTodo todo = getRepository().findOne(id);

        if (todo == null) {
            throw new BusinessException("The request was deleted");
        }

        if (todo.getToken() == null || !todo.getToken().equals(token))
            throw new BusinessException("You're unauthorized");

        String redirectPageAction;
        ExpenseRequestTodoFlowActions status = todo.getManagerAction();
        if (validateTakeManagerActionFromEmail(id)) {
            rejectRequest(todo, rejectionNotes);
            redirectPageAction = "2";
        } else {
            redirectPageAction = "3";
        }

        URI redirectPage = new URI(Setup.getCoreParameter(CoreParameter.PUBLIC_LINK_BASE) + "/" +
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_EXPENSE_REQUEST_REDIRECT_AFTER_ACTION_PAGE) +
                "?action=" + redirectPageAction + (status != null ? "&status=" + status : ""));

        //ACC-3906
        if (!withoutRedirect) {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setLocation(redirectPage);
            return new ResponseEntity(httpHeaders, HttpStatus.SEE_OTHER);
        } else {
            return new ResponseEntity(redirectPage, HttpStatus.OK);
        }
    }

    public void rejectRequest(
            ExpenseRequestTodo todo,
            String rejectionNotes) {

        if (todo.getTaskName() != null) {
            todo.setRejectionNotes(rejectionNotes);

            switch(todo.getTaskName()) {
                case "WAITING_MANAGER_APPROVAL":
                todo.setRejectionNotes(rejectionNotes);
                todo.setManagerAction(ExpenseRequestTodoFlowActions.REJECT);

                this.completeTask(todo, ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
                    break;
                case "WAITING_COO_APPROVAL":
                    todo.setCooAction(ExpenseRequestTodoFlowActions.REJECT);
                    this.completeTask(todo, ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString());
                    break;
                default:
                    expenseRequestTodoRepository.save(todo);
                    break;
            }

            expenseNotificationService.sendExpenseRejectedApprovedRequestEmail(
                    todo, rejectionNotes, "Rejected");
        }
    }

    private boolean validateTakeManagerActionFromEmail(Long id) {
        ExpenseRequestTodo todo = expenseRequestTodoRepository.findOne(id);
        return todo.getTaskName() != null && todo.getTaskName().equals(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString());
    }

    @PreAuthorize("hasPermission('expenserequesttodo','getbetterprice')")
    @RequestMapping(value = "/getbetterprice", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> getbetterprice(@RequestParam(name = "id", required = false) Long id, @RequestParam(name = "token", required = false) String token,
                                            @RequestParam(name = "sendToGetBetterPriceNote", required = true) String sndToGetBetterPriceNote) {
        ExpenseRequestTodo todo = getRepository().findOne(id);

        if (todo == null) {
            return new ResponseEntity<>("The request was deleted", HttpStatus.NOT_MODIFIED);
        }

        if (todo.getToken() == null || !todo.getToken().equals(token))
            return new ResponseEntity<>("You're unauthorized", HttpStatus.UNAUTHORIZED);


        if (todo.getTaskName() == null)
            return new ResponseEntity<>("Sorry, this expense request doesn't need action anymore!", HttpStatus.BAD_REQUEST);

        if (!todo.getTaskName().equals(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString()))
            return new ResponseEntity<>("Sorry, this expense request is not in WAITING_COO_APPROVALstep anymore!", HttpStatus.BAD_REQUEST);

        todo.setCooAction(ExpenseRequestTodoFlowActions.GET_BETTER_PRICE);
        todo.setSendToGetBetterPriceNote(sndToGetBetterPriceNote);
        this.completeTask(todo, ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString());

        return new ResponseEntity<>("Approved!", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('expenseRequestTodo','search')")
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    public ResponseEntity<?> search(
            @RequestBody ExpenseRequestSearchDto search,
            @RequestParam(name = "expensesRequestPage", required = false) String expensePage,
            Pageable pageable) {

        if (expensePage != null && expensePage.equalsIgnoreCase("expense_requests")) {
            List<String> pendingForApproval = new ArrayList<>();
            pendingForApproval.add("Pending CEO");
            pendingForApproval.addAll(expenseRepository.findApproveHolderEmailsForPendingForApprovalFieldSearch());

            return ResponseEntity.ok(new HashMap<String, Object>() {{
                put("todos", queryService.getExpenseRequestTodoSearchApi(search, pageable)
                        .map(todo -> projectionFactory.createProjection(ExpenseRequestSearchApiProjection.class, todo)));
                put("paidAmount", queryService.calculateTheAmountOfExpenseRequestTodoDependingOnItsStatus(search, ExpenseRequestStatus.PAID));
                put("pendingAmount", queryService.calculateTheAmountOfExpenseRequestTodoDependingOnItsStatus(search, ExpenseRequestStatus.PENDING));
                put("pendingPaymentAmount", queryService.calculateTheAmountOfExpenseRequestTodoDependingOnItsStatus(
                        search, ExpenseRequestStatus.PENDING_PAYMENT));
                put("pendingForApproval", pendingForApproval);

            }});
        } else {
            return ResponseEntity.ok(queryService.getExpenseRequestTodoSearchApi(search, pageable)
                    .map(todo -> projectionFactory.createProjection(ExpenseRequestSearchApiProjection.class, todo)));
        }
    }

    @PreAuthorize("hasPermission('expenseRequestTodo','search/csv')")
    @PostMapping("/search/csv")
    public void exportExpenseRequestsCsv(
            @RequestBody ExpenseRequestSearchDto search,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {

        if (limit == null || limit > csvMaxLimit) limit = csvMaxLimit;


        InputStream is = null;
        try {
            Page<ExpenseRequestTodoSearchApiDto> page =
                    queryService.getExpenseRequestTodoSearchApi(search, PageRequest.of(0, limit));

            /* 2️⃣  Technical property names that match projection getters */
            String[] propertyNames = {
                    "expense", "transaction", "relatedTo", "beneficiary",
                    "amount", "paymentMethod", "bucketFrom", "status",
                    "pendingForApproval", "requestedBy", "approvedBy",
                    "date", "loanAmount", "description", "attachment"
            };

            /* 3️⃣  Generate CSV: (data, projection, headers, propertyNames) */
            is = generateCsv(
                    page.getContent(),
                    ExpenseRequestSearchCsvProjection.class,
                    propertyNames             // bean-property mapping
            );

            createDownloadResponse(response, "ExpenseRequestTodos.csv", is);

        } catch (Exception ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    // Jira ACC-9426 – Send Expense-Request todos via e-mail
    @PreAuthorize("hasPermission('expenseRequestTodo','search/csv/mail')")
    @PostMapping("/search/csv/mail")
    public ResponseEntity<?> exportExpenseRequestsCsvMail(
            @RequestBody ExpenseRequestSearchDto search,
            @RequestParam(name = "limit", required = false) Integer limit) {

        int csvCap = CsvHelper.getCsvMailMaxLimit(
                AccountingModule.PARAMETER_EXPENSE_REQUEST_TODO_CSV_ROWS_LIMIT,
                5000);
        if (limit == null || limit > csvCap) limit = csvCap;

        /* 1 ─ Query ------------------------------------------------------- */
        Page<ExpenseRequestTodoSearchApiDto> page =
                queryService.getExpenseRequestTodoSearchApi(
                        search, PageRequest.of(0, limit));

        /* 2 ─ Pretty labels vs property names ----------------------------- */
        String[] prettyHeaders = {
                "Expense", "Transaction", "Related To", "Beneficiary",
                "Amount", "Payment Method", "Bucket From", "Status",
                "Pending For Approval", "Requested By", "Approved By",
                "Date", "Loan Amount", "Description", "Attachment"
        };

        String[] propertyNames = {
                "expense", "transaction", "relatedTo", "beneficiary",
                "amount", "paymentMethod", "bucketFrom", "status",
                "pendingForApproval", "requestedBy", "approvedBy",
                "date", "loanAmount", "description", "attachment"
        };

        /* 3 ─ Delegate to helper (async) ---------------------------------- */
        try {
            emailHelper.sendCsvMail(
                    page.getContent(),
                    ExpenseRequestSearchCsvProjection.class,
                    prettyHeaders,
                    propertyNames,
                    "ExpenseRequestTodos",
                    "Expense-Request Todos (" + page.getTotalElements() + " rows)",
                    CurrentRequest.getUser().getEmail());
        } catch (Exception ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
            throw new BusinessException("Unable to generate / send CSV file.");
        }

        /* 4 ─ Immediate HTTP response ------------------------------------ */
        return new ResponseEntity<>(
                "The file is being generated and will arrive in your mailbox shortly.",
                HttpStatus.OK);
    }


    //ACC-8876
    @PreAuthorize("hasPermission('expenseRequestTodo', 'getMaidExpenseRequests')")
    @GetMapping("/getMaidExpenseRequests/{housemaidId}")
    public ResponseEntity<?> getMaidExpenseRequests(
            @PathVariable Long housemaidId,
            @RequestParam(name = "contractId", required = false) Long contractId,
            @RequestParam(name = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(name = "toDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            Pageable pageable) {

        String fromStatement = " from ExpenseRequestTodo e " +
                "inner join e.expense ex " +
                "left join e.expensePayment ep";

        String query = "select new map(" +
                "e.id as id, " +
                "ex.id as expenseId, " +
                "ex.name as expenseName, " +
                "ex.caption as expenseCaption, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.purposeAdditionalDescription) as purposeAdditionalDescription, " +
                "e.creationDate as creationDate, " +
                "e.amount as amount, " +
                "e.loanAmount as loanAmount, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.currency) as currency, " +
                "e.paymentMethod as paymentMethod, " +
                "e.description as description, " +
                "e.status as status, " +
                "e.rejectionNotes as rejectionNotes, " +
                "(select u.fullName from User u where u = e.requestedBy) as requestedBy, " +
                "e.approvedBy as approvedBy, " +
                "e.notes as notes, " +
                "ep.id as paymentId," +
                "e.contractId as contractId) ";

        String conditions = " where e.relatedToId = :housemaidId and e.relatedToType = 'MAID' and ep.transaction is null ";
        query = queryService.sortDirectQueryByPageable(pageable, query, "e");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("housemaidId", housemaidId);

        if (contractId != null) {
            conditions += " and e.contractId = :contractId ";
            parameters.put("contractId", contractId);
        }
        if (fromDate != null) {
            conditions += " and e.creationDate >= :fromDate ";
            parameters.put("fromDate", fromDate);
        }
        if (toDate != null) {
            conditions += " and e.creationDate <= :toDate ";
            parameters.put("toDate", toDate);
        }

        query += fromStatement + conditions;

        String countQuery = "select count(e.id) " + fromStatement + conditions;

        SelectQuery<Map> q = new SelectQuery<>(query, countQuery, java.util.Map.class, parameters);
        Page<Map> p = q.execute(pageable);

        // fill attachments
        List<Map> attachments = queryService.getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(e -> (Long) e.get("id")).collect(Collectors.toList()), "ExpenseRequestTodo");
        p.getContent().forEach(e -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> e.get("id").equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            e.put("attachments", attachmentList);
        });

        // calculate total amount
        Double totalAmount = housemaidService.getHousemaidExpenseRequestsTotalAmount(housemaidId, null);

        AccountingPage accountingPageResult = new AccountingPage(p.getContent(), pageable, p.getTotalElements(), totalAmount);
        return ResponseEntity.ok(accountingPageResult);
    }

    @PreAuthorize("hasPermission('expenseRequestTodo','searchRefund')")
    @PostMapping("/searchRefund")
    public ResponseEntity<?> searchRefund(
            @RequestBody ExpenseRequestRefundSearchDto search,
            Pageable pageable) {

        SelectQuery<ExpenseRequestTodo> query = new SelectQuery(ExpenseRequestTodo.class);
        query.leftJoin("expense");

        //since this API will only be used for Refunds Records then
        query.filterBy("isRefunded", "=", true);

        if (search.getRefundId() != null) {
            query.filterBy("id", "=", search.getRefundId());
        }

        if (search.getExpenseId() != null) {
            query.filterBy("expense.id", "=", search.getExpenseId());
        }

        if (search.getRefundConfirmed() != null) {
            SelectFilter selectFilter = new SelectFilter("refundConfirmed", "=", search.getRefundConfirmed());
            if (!BooleanUtils.toBoolean(search.getRefundConfirmed())) {
                selectFilter = selectFilter.or("refundConfirmed", "IS NULL", null);
            }
            query.filterBy(selectFilter);
        }

        if (search.getExpenseAmount() != null && search.getExpenseAmountOperator() != null) {
            query.filterBy("amount", search.getExpenseAmountOperator(), search.getExpenseAmount());
        }
        if (search.getRefundAmount() != null && search.getRefundAmountOperator() != null) {
            query.filterBy("refundAmount", search.getRefundAmountOperator(), search.getRefundAmount());
        }

        if (search.getSupplierId() != null) {
            query.filterBy("beneficiaryType", "=", SUPPLIER);
            query.filterBy("beneficiaryId", "=", search.getSupplierId());
        }

        if (search.getRelatedToType() != null) {
            query.filterBy("relatedToType", "=", search.getRelatedToType());

            if (search.getRelatedToId() == null) {
                switch(search.getRelatedToType()) {
                    case MAID:
                        throw new RuntimeException("Please fill the proper MAID in 'Related to' search field");
                    case TEAM:
                        throw new RuntimeException("Please fill the proper TEAM in 'Related to' search field");
                    case OFFICE_STAFF:
                        throw new RuntimeException("Please fill the proper OFFICE STAFF in 'Related to' search field");
                    case APPLICANT:
                        throw new RuntimeException("Please fill the proper APPLICANT in 'Related to' search field");
                }
            }
            query.filterBy("relatedToId", "=", search.getRelatedToId());
        }

        if (search.getExpenseDate1() != null && search.getExpenseDateOperator() != null && search.getExpenseDate2() == null) {
            if (search.getExpenseDateOperator().equals("=")) {
                query.filterBy("creationDate", ">=", new DateTime(search.getExpenseDate1()).withTimeAtStartOfDay().toDate());
                query.filterBy("creationDate", "<", new DateTime(search.getExpenseDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else query.filterBy("creationDate", search.getExpenseDateOperator(), search.getExpenseDate1());

        } else if (search.getExpenseDate1() != null && search.getExpenseDate2() != null) {
            query.filterBy("creationDate", ">=", search.getExpenseDate1());
            query.filterBy("creationDate", "<=", search.getExpenseDate2());
        }

        if (search.getRefundDate1() != null && search.getRefundDateOperator() != null && search.getRefundDate2() == null) {
            if (search.getRefundDateOperator().equals("=")) {
                query.filterBy("refundDate", ">=", new DateTime(search.getRefundDate1()).withTimeAtStartOfDay().toDate());
                query.filterBy("refundDate", "<", new DateTime(search.getRefundDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else query.filterBy("refundDate", search.getRefundDateOperator(), search.getRefundDate1());

        } else if (search.getRefundDate1() != null && search.getRefundDate2() != null) {
            query.filterBy("refundDate", ">=", search.getRefundDate1());
            query.filterBy("refundDate", "<=", search.getRefundDate2());
        }

        return new ResponseEntity(query.execute(pageable).map(todo -> projectionFactory.createProjection(
                ExpenseRequestRefundProjection.class, todo)), HttpStatus.OK);
    }

    @PostMapping("/exportRefundsAsCsv")
    @Transactional
    @PreAuthorize("hasPermission('expenseRequestTodo','exportRefundsAsCsv')")
    public void exportRefundsAsCsv(
            @RequestBody ExpenseRequestRefundSearchDto search,
            HttpServletResponse response) {

        SelectQuery<ExpenseRequestTodo> query = new SelectQuery(ExpenseRequestTodo.class);
        query.leftJoin("expense");

        //since this API will only be used for Refunds Records then
        query.filterBy("isRefunded", "=", true);

        if (search.getRefundId() != null) {
            query.filterBy("id", "=", search.getRefundId());
        }

        if (search.getExpenseId() != null) {
            query.filterBy("expense.id", "=", search.getExpenseId());
        }

        if (search.getRefundConfirmed() != null) {
            SelectFilter selectFilter = new SelectFilter("refundConfirmed", "=", search.getRefundConfirmed());
            if (!BooleanUtils.toBoolean(search.getRefundConfirmed())) {
                selectFilter = selectFilter.or("refundConfirmed", "IS NULL", null);
            }
            query.filterBy(selectFilter);
        }

        if (search.getExpenseAmount() != null && search.getExpenseAmountOperator() != null) {
            query.filterBy("amount", search.getExpenseAmountOperator(), search.getExpenseAmount());
        }
        if (search.getRefundAmount() != null && search.getRefundAmountOperator() != null) {
            query.filterBy("refundAmount", search.getRefundAmountOperator(), search.getRefundAmount());
        }

        if (search.getSupplierId() != null) {
            query.filterBy("beneficiaryType", "=", SUPPLIER);
            query.filterBy("beneficiaryId", "=", search.getSupplierId());
        }

        if (search.getRelatedToType() != null) {
            query.filterBy("relatedToType", "=", search.getRelatedToType());
        }
        if (search.getRelatedToId() != null) {
            query.filterBy("relatedToId", "=", search.getRelatedToId());
        }

        if (search.getExpenseDate1() != null && search.getExpenseDateOperator() != null && search.getExpenseDate2() == null) {

            if (search.getExpenseDateOperator().equals("=")) {
                query.filterBy("creationDate", ">=", new DateTime(search.getExpenseDate1()).withTimeAtStartOfDay().toDate());
                query.filterBy("creationDate", "<", new DateTime(search.getExpenseDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else query.filterBy("creationDate", search.getExpenseDateOperator(), search.getExpenseDate1());

        } else if (search.getExpenseDate1() != null && search.getExpenseDate2() != null) {
            query.filterBy("creationDate", ">=", search.getExpenseDate1());
            query.filterBy("creationDate", "<=", search.getExpenseDate2());
        }

        if (search.getRefundDate1() != null && search.getRefundDateOperator() != null && search.getRefundDate2() == null) {

            if (search.getRefundDateOperator().equals("=")) {
                query.filterBy("refundDate", ">=", new DateTime(search.getRefundDate1()).withTimeAtStartOfDay().toDate());
                query.filterBy("refundDate", "<", new DateTime(search.getRefundDate1()).plusDays(1).withTimeAtStartOfDay().toDate());
            } else query.filterBy("refundDate", search.getRefundDateOperator(), search.getRefundDate1());

        } else if (search.getRefundDate1() != null && search.getRefundDate2() != null) {
            query.filterBy("refundDate", ">=", search.getRefundDate1());
            query.filterBy("refundDate", "<=", search.getRefundDate2());
        }

        List<ExpenseRequestTodo> refundList = query.execute();

        InputStream inputStream = null;
        File excelFile;
        String fileName = "Expense Refunds.csv";
        try {
            String[] namesOrdered = {"expenseId", "expenseName", "expenseTransaction", "supplier", "relatedToInfo", "id", "amount", "refundAmount", "creationDate", "refundDate"};

            String[] headers = {"Expense Id", "Expense Name", "Expense Transaction", "Supplier", "Related To", "Refund Id", "Expense Amount", "Refund Amount", "Expense Date", "Refund Date"};

            excelFile = CsvHelper.generateCsv(refundList, ExpenseRequestRefundProjection.class,
                    headers, namesOrdered, fileName);
        } catch (IOException ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }

        try {
            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, fileName, inputStream);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    @PreAuthorize("hasPermission('expenseRequestTodo','index')")
    @RequestMapping(value = "/addrefund", method = RequestMethod.GET)
    public ResponseEntity<?> addRefund(@RequestParam(name = "id", required = false) Long id, @RequestParam(name = "amount", required = false) Double amount) {
        ExpenseRequestTodo todo = getRepository().findOne(id);
        if (!todo.getCanBeRefunded())
            return new ResponseEntity("Sorry, this Request can't be refunded!", HttpStatus.BAD_REQUEST);

        todo.setRefundAmount(amount);
        todo.setIsRefunded(true);
        todo.setRefundDate(new Date());
        getRepository().save(todo);
        return new ResponseEntity("Done!", HttpStatus.OK);

    }

    @RequestMapping(value = {"/parsetransportationexpensefile"}, method = {RequestMethod.POST})
    @ResponseBody
    @Transactional
    @PreAuthorize("hasPermission('expenseRequestTodo','parsetransportationexpensefile')")
    public ResponseEntity<?> parseTransportationExpenseFile(@RequestParam(name = "supplierName") String supplierName, @RequestBody ExpenseRequestTodo expenseRequestTodo) {

        return new ResponseEntity(parseAttachmentAndCompareToErp(supplierName, expenseRequestTodo), HttpStatus.OK);
    }

    public ParsedTransportationExpenseAttachmentDto parseAttachmentAndCompareToErp(String supplierName, ExpenseRequestTodo expenseRequestTodo) {
        if (expenseRequestTodo == null || expenseRequestTodo.getAttachments() == null || expenseRequestTodo.getAttachments().isEmpty())
            throw new RuntimeException("No audit excel sheet attached!");
        Attachment attachment = null;
        String hala = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_HALA_SUPPLIER_NAME);
        BillType billType = BillType.HALA;
        String careem = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CAREEM_SUPPLIER_NAME);
        if (supplierName.equals(careem))
            billType = BillType.CAREEM;
        else if (!supplierName.equals(hala))
            throw new RuntimeException("Supplier should be " + hala + " or " + careem);

        attachment = expenseRequestTodo.getAttachments().get(0);

        return transportationExpenseAttachmentParser.parseAttachmentAndCompareToErp(attachment, billType,
                expenseRequestTodo.getRequestFrom(), expenseRequestTodo.getRequestTo());
    }

    @RequestMapping(value = {"/TransportationExpenseGenerateCSV"}, method = {RequestMethod.POST})
    @ResponseBody
    @Transactional
    @PreAuthorize("hasPermission('expenseRequestTodo','TransportationExpenseGenerateCSV')")
    public void TransportationExpenseGenerateCSV(@RequestParam(name = "supplierName") String supplierName,
                                                 @RequestParam(name = "getERPRecords") boolean getERPRecords,
                                                 @RequestBody ExpenseRequestTodo expenseRequestTodo,
                                                 HttpServletResponse response) {

        ParsedTransportationExpenseAttachmentDto data = parseAttachmentAndCompareToErp(supplierName, expenseRequestTodo);

        InputStream inputStream = null;
        File excelFile;
        String fileName = getERPRecords ? "TaxiRidesInERP.csv" : "TaxiRidesInExcelSheet.csv" ;
        try {
            if (getERPRecords) {
                String[] namesOrdered = {"bookingId", "erpAmount", "passenger", "purpose", "pickupLocation",
                        "dropOffLocation", "date", "attachmentLink"
                };

                String[] headers =
                        {"Booking Id","Amount in ERP", "Passenger", "Purpose", "Pickup Location", "Drop Off Location",
                                "Date", "Attachment"};

                excelFile = CsvHelper.generateCsv(data.getErpTaxiOrders(), TransportationExpenseCSVProjection.class, headers,
                        namesOrdered, fileName);
            } else {
                String[] namesOrdered = {"bookingId", "billAmount", "passenger", "dateString", "pickupLocation", "dropOffLocation"};

                String[] headers = {"Booking Id", "Amount in excel sheet", "Passenger", "Date", "PickupLocation", "Drop Off Location"};

                excelFile = CsvHelper.generateCsv(data.getAttachmentTaxiOrders(), TransportationExpenseCSVProjection.class,
                        headers, namesOrdered, fileName);
            }

        } catch (IOException ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }

        try {
            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, fileName, inputStream);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    @RequestMapping(value = "/transportationexpenserequestneedsinvoice", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','transportationexpenserequestneedsinvoice')")
    public ResponseEntity<?> transportationExpenseRequestNeedsInvoice(@RequestParam(value = "from") @DateTimeFormat(pattern = "yyyy-MM-dd") Date from,
                                                                      @RequestParam(value = "to") @DateTimeFormat(pattern = "yyyy-MM-dd") Date to,
                                                                      @RequestParam(name = "supplierName") String supplierName,
                                                                      @RequestParam(name = "amount") Double amount) {
        Boolean enableSendRequest = false;
        Double taxiOrdersAmounts = 0D;
        String hala = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_HALA_SUPPLIER_NAME);
        BillType billType = BillType.HALA;
        String careem = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CAREEM_SUPPLIER_NAME);
        if (supplierName.equals(careem))
            billType = BillType.CAREEM;
        else if (!supplierName.equals(hala))
            throw new RuntimeException("Supplier should be " + hala + " or " + careem);

//        if (billType == null)
//            taxiOrdersAmounts = transportationExpenseAttachmentParser.getTaxiAmountSum(from, to);
//        else
        taxiOrdersAmounts = transportationExpenseAttachmentParser.getTaxiAmountSum(billType, from, to);

        if (Math.abs(taxiOrdersAmounts - amount) <= 100) {
            enableSendRequest = true;
        }

        return new ResponseEntity(new TransportationExpenseValidationDto(taxiOrdersAmounts - amount, enableSendRequest, true), HttpStatus.OK);
    }

    @GetMapping(value = "/expensesavailableforpage")
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','expensesavailableforpage')")
    public ResponseEntity<?> expensesAvailableForPage(
            @RequestParam(value = "page") ExpenseRequestType page,
            @RequestParam(value = "search", required = false) String search) {

        String selectStatement = "select new map(e.id as id, e.caption as label, e.code as code) " +
                "from Expense e " +
                "join e.requestedFrom r " +
                "left join e.requestors req " +
                "where r.list.code = :listCode and r.code = :code and e.deleted = false and e.disabled = false " +
                "and (req is null or req.id = :userId)";

        Map<String, Object> para = new HashMap<>();
        para.put("listCode", AccountingModule.PICKLIST_EXPENSE_REQUESTED_FROM);
        para.put("userId", CurrentRequest.getUser().getId());

        if (search != null) {
            selectStatement += " and (";
            selectStatement += " e.code like concat('%', :search, '%') ";
            selectStatement += " or e.caption like concat('%', :search, '%')";
            selectStatement += " or e.name like concat('%', :search, '%') ";
            selectStatement += ") ";
            para.put("search", search);
        }

        switch (page) {
            case MAID_PAYMENT:
                para.put("code", "show_expense_in_add_payments_to_maids_page");
                break;
            case MEDICAL:
                para.put("code", "show_expense_in_medical_expenses_page");
                break;
            case NEW_REQUEST:
                para.put("code", "show_expense_in_request_expenses_page");
                break;
            case CASHIER:
                para.put("code", "show_in_add_new_payment_in_cashier_screen");
                break;
            case MAINTENANCE:
                para.put("code", "show_in_maintenance_request_in_request_expense_page");
                break;
            case ONE_TIME:
                para.put("code", "show_in_one-time_request");
                break;
            case TICKETING:
                Picklist picklist = picklistRepository.findByCode(AccountingModule.PICKLIST_EXPENSE_REQUESTED_FROM);
                PicklistItem item = picklistItemRepository.findByListAndCodeIgnoreCase(picklist, "show_in_ticketing_expense_screen");
                List<Expense> expenses = expenseRepository.findByRequestedFromAndDeletedFalseAndDisabledFalseAndRequestorsIn(item, CurrentRequest.getUser().getId(), search);

                return ResponseEntity.ok(expenses.stream()
                        .map(e -> projectionFactory.createProjection(ExpenseProjectionWithBuckets.class, e))
                        .collect(Collectors.toList()));
            case VIP:
                para.put("code", "show_in_vip_expense_screen");
                break;
        }

        return ResponseEntity.ok(new SelectQuery<>(selectStatement, "", Map.class, para).execute());
    }

    @RequestMapping(
            value = {"/createCovidTestRequest"},
            method = {RequestMethod.POST}
    )
    @ResponseBody
    @Transactional
    @PreAuthorize("hasPermission('expenseRequestTodo','createCovidTestRequest')")
    public ResponseEntity<?> createCovidTestRequest(@RequestBody CovidTestExpenseRequestDto covidTestExpenseRequestDto) {
        if (covidTestExpenseRequestDto == null)
            return badRequestResponse();
        if (covidTestExpenseRequestDto.getAmountForOne() == null)
            throw new RuntimeException("Amount for one should not be null");
        if (covidTestExpenseRequestDto.getHousemaidsIds() == null || covidTestExpenseRequestDto.getHousemaidsIds().isEmpty())
            throw new RuntimeException("Housmaids list should not be empty");
        if (covidTestExpenseRequestDto.getExpensePaymentMethod() == null)
            throw new RuntimeException("Payment method should not be null");

        String covidExpenseCode = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_COVID_TEST_EXPENSE_CODE);
        Expense expense = expenseRepository.findOneByCode(covidExpenseCode);
        if (expense == null)
            throw new RuntimeException("Covid19 Test expense setup doesn't exist, please add it with code " + covidExpenseCode + ".");
        if (expense.getBeneficiaryType() == null || !expense.getBeneficiaryType().equals(SUPPLIER))
            throw new RuntimeException(covidExpenseCode + " expense beneficiary type should be Supplier");
        if (expense.getSuppliers() == null || expense.getSuppliers().isEmpty() || expense.getSuppliers().size() > 1)
            throw new RuntimeException(covidExpenseCode + " expense must have one supplier");
//        if (expense.getPaymentMethods() == null || expense.getPaymentMethods().isEmpty() || expense.getPaymentMethods().size() > 1)
//            throw new RuntimeException(covidExpenseCode + " expense must have one payment method");

        for (Long housemaidId : covidTestExpenseRequestDto.getHousemaidsIds()) {
            ExpenseRequestTodo expenseRequestTodo = new ExpenseRequestTodo();
            expenseRequestTodo.setExpense(expense);
            expenseRequestTodo.setBeneficiaryId(expense.getSuppliers().get(0).getId());
            expenseRequestTodo.setBeneficiaryType(SUPPLIER);
            expenseRequestTodo.setPaymentMethod(covidTestExpenseRequestDto.getExpensePaymentMethod());
            expenseRequestTodo.setRelatedToId(housemaidId);
            expenseRequestTodo.setRelatedToType(ExpenseRelatedTo.ExpenseRelatedToType.MAID);
            expenseRequestTodo.setExpenseRequestType(ExpenseRequestType.COVID_TEST);
            expenseRequestTodo.setAmount(covidTestExpenseRequestDto.getAmountForOne());
            this.createEntity(expenseRequestTodo);
        }

        return okResponse();

    }

    @RequestMapping(value = "/getAvailableDewaPhones", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getAvailableDewaPhones')")
    public ResponseEntity<?> getAvailableDewaPhones() {
        Picklist picklist = picklistRepository.findByCode(AccountingModule.PICKLIST_SERVICE_TYPE);
        PicklistItem dewa = picklistItemRepository.findByListAndCodeIgnoreCase(picklist,
                "dewa");
        List<TelecomPhone> dewaPhones = telecomPhoneRepository.findByServiceTypeAndActive(dewa, true);
        return new ResponseEntity(dewaPhones.stream().map(todo -> projectionFactory.createProjection(
                TelecomPhoneExpenseRequestProjection.class, todo)).collect(Collectors.toList()), HttpStatus.OK);
    }

    @RequestMapping(value = "/getAvailableTelecomPhonesUsages", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getAvailableTelecomPhonesUsages')")
    public ResponseEntity<?> getAvailableTelecomPhonesUsages(@RequestParam(value = "expenseId") Long expenseId) {
        Picklist picklist = picklistRepository.findByCode(AccountingModule.PICKLIST_SERVICE_TYPE);
        PicklistItem dewa = picklistItemRepository.findByListAndCodeIgnoreCase(picklist,
                "dewa");
        Expense expense = expenseRepository.findOne(expenseId);
        List<TelecomPhone> telecomPhones = telecomPhoneRepository.findByPrimaryExpenseAndServiceTypeNotAndActiveAndDeleted(expense, dewa, true, false);
        return new ResponseEntity(telecomPhones.stream().map(todo -> projectionFactory.createProjection(
                TelecomPhoneExpenseRequestProjection.class, todo)).collect(Collectors.toList()), HttpStatus.OK);
    }

    @RequestMapping(value = "/getAvailableTelecomPhonesExpenses", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getAvailableTelecomPhonesExpenses')")
    public ResponseEntity<?> getAvailableTelecomPhonesExpenses() {
        Picklist picklist = picklistRepository.findByCode(AccountingModule.PICKLIST_SERVICE_TYPE);
        PicklistItem dewa = picklistItemRepository.findByListAndCodeIgnoreCase(picklist,
                "dewa");
        List<TelecomPhone> telecomPhones = telecomPhoneRepository.findByServiceTypeNotAndActive(dewa, true);
        List<TelecomPhone> result = new ArrayList<>();
        List<Long> expenses = new ArrayList<>();
        for (TelecomPhone item : telecomPhones) {
            if (item.getPrimaryExpense() != null && !BooleanUtils.toBoolean(item.getPrimaryExpense().getDisabled()) && !expenses.contains(item.getPrimaryExpense().getId())) {
                result.add(item);
                expenses.add(item.getPrimaryExpense().getId());
            }
        }
        return new ResponseEntity(result.stream()
                .map(todo -> projectionFactory.createProjection(
                        TelecomPhoneExpenseRequestProjection.class, todo))
                .collect(Collectors.toList()), HttpStatus.OK);
    }

    @RequestMapping(value = "/getCooMaidAdditionRequests", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getCooMaidAdditionRequests')")
    public ResponseEntity<?> getCooMaidAdditionRequestsAPI(@RequestParam("questionedPage") CooQuestion.QuestionedPage questionedPage,
                                                           Pageable pageable) {
        Page<ExpenseRequestTodo> expenseRequestToDos = getCooMaidAdditionRequests(pageable);

        Page<ExpenseRequestProjection> requests = expenseRequestToDos.map(todo -> {
            todo.setCooQuestionedPage(questionedPage);
            return projectionFactory.createProjection(
                    ExpenseRequestProjection.class, todo);
        });

        Double totalAmount = requests.getContent().stream().mapToDouble(p -> p.getAmount()).sum();
        AccountingPage page = new AccountingPage(requests.getContent(), pageable, requests.getTotalElements(), totalAmount);

        return new ResponseEntity(page, HttpStatus.OK);
    }

    private Page<ExpenseRequestTodo> getCooMaidAdditionRequests(Pageable pageable) {
        List<String> requestedFromList = Arrays.asList("show_expense_in_taxi_page", "show_expense_in_medical_expenses_page", "show_expense_in_add_payments_to_maids_page");

        Page<ExpenseRequestTodo> expenseRequestToDos = expenseRequestTodoRepository.findByTaskNameAndRelatedToTypeAndExpense_RequestedFrom_CodeIn(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString(),
                ExpenseRelatedTo.ExpenseRelatedToType.MAID, requestedFromList, pageable);
        return expenseRequestToDos;
    }

    @RequestMapping(value = "/getCooExpenseRequests", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getCooExpenseRequests')")
    public ResponseEntity<?> getCooExpenseRequestsAPI(@RequestParam("questionedPage") CooQuestion.QuestionedPage questionedPage,
                                                      Pageable pageable) {
        Page<ExpenseRequestTodo> expenseRequestTodos = getCooExpenseRequests(pageable);
        List<Map> todosSecuredInfo = queryService.getExpenseRequestTodoWithSecuredInfo(expenseRequestTodos.getContent().stream()
                .map(BaseEntity::getId).collect(Collectors.toList()));

        Page<ExpenseRequestProjection> projectedPage = expenseRequestTodos.map(todo -> {
            todo.setCooQuestionedPage(questionedPage);

            Map m = todosSecuredInfo.stream()
                    .filter(t -> todo.getId().equals(t.get("todoId"))).findFirst().orElse(null);
            if (m != null) {
                if (expensePaymentService.bucketOrExpenseSecure(((Boolean) m.get("exIsSecure")), ((Boolean) m.get("fbIsSecure")),
                        ((Boolean) m.get("tbIsSecure")))) {
                    todo.setLastPaymentDetail("***##*** Description Secured ***");
                }
            }
            return projectionFactory.createProjection(
                    ExpenseRequestProjection.class, todo);
        });

        Double totalAmount = expenseRequestTodos.getContent().stream().mapToDouble(p -> p.getAmount()).sum();
        AccountingPage page = new AccountingPage(projectedPage.getContent(), pageable, projectedPage.getTotalElements(), totalAmount);

        return new ResponseEntity(page, HttpStatus.OK);
    }

    private Page<ExpenseRequestTodo> getCooExpenseRequests(Pageable pageable) {
        List<String> requestedFromList = Arrays.asList("show_expense_in_taxi_page", "show_expense_in_medical_expenses_page", "show_expense_in_add_payments_to_maids_page");

        Page<ExpenseRequestTodo> expenseRequestToDos = expenseRequestTodoRepository.findByTaskNameAndExpenseAndNotRelatedToMaidOrRelatedToMaidAnd_RequestedFrom_CodeNotIn(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString(), requestedFromList, pageable);

        return expenseRequestToDos;
    }

    @RequestMapping(value = "/getApprovalExpenseRequests", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getApprovalExpenseRequests')")
    public ResponseEntity<?> getApprovalExpenseRequests() {
        User creator = CurrentRequest.getUser();
        List<ExpenseRequestTodo> expenseRequestTodos = expenseRequestTodoRepository.findByTaskNameAndApproveHolder(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString(), creator);
        List<Map> todosSecuredInfo = queryService.getExpenseRequestTodoWithSecuredInfo(expenseRequestTodos.stream()
                .map(BaseEntity::getId).collect(Collectors.toList()));

        List<ExpenseRequestProjection> requests = expenseRequestTodos.stream().map(todo -> {
            Map m = todosSecuredInfo.stream()
                    .filter(t -> todo.getId().equals(t.get("todoId"))).findFirst().orElse(null);

            if (m != null) {
                if (expensePaymentService.bucketOrExpenseSecure(((Boolean) m.get("exIsSecure")), ((Boolean) m.get("fbIsSecure")),
                        ((Boolean) m.get("tbIsSecure")))) {
                    todo.setLastPaymentDetail("***##*** Description Secured ***");
                }
            }
            return projectionFactory.createProjection(
                    ExpenseRequestProjection.class, todo);
        }).collect(Collectors.toList());
        return new ResponseEntity(requests, HttpStatus.OK);
    }

    @RequestMapping(value = "/getBeneficiaryExpenseRequestsHistory", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getBeneficiaryExpenseRequestsHistory')")
    public ResponseEntity<?> getBeneficiaryExpenseRequestsHistory(@RequestParam(value = "beneficiaryId") Long beneficiaryId) {
        List<ExpenseRequestTodo> expenseRequestTodos =
                expenseRequestTodoRepository.findByBeneficiaryIdAndStatusIn(beneficiaryId,
                        Arrays.asList(ExpenseRequestStatus.PENDING, ExpenseRequestStatus.PENDING_PAYMENT, ExpenseRequestStatus.REJECTED, ExpenseRequestStatus.PAID));
        List<ExpenseRequestProjection> requests = expenseRequestTodos.stream().map(todo -> projectionFactory.createProjection(
                ExpenseRequestProjection.class, todo)).collect(Collectors.toList());
        return new ResponseEntity(requests, HttpStatus.OK);
    }

    @RequestMapping(value = "/getRelatedToExpenseRequestsHistory", method = RequestMethod.GET)
    @ResponseBody
    @PreAuthorize("hasPermission('expenseRequestTodo','getRelatedToExbenefeciaryInfopenseRequestsHistory')")
    public ResponseEntity<?> getRelatedToExpenseRequestsHistory(@RequestParam(value = "relatedToId") Long relatedToId) {
        List<ExpenseRequestTodo> expenseRequestTodos =
                expenseRequestTodoRepository.findByRelatedToIdAndStatusIn(relatedToId,
                        Arrays.asList(ExpenseRequestStatus.PENDING, ExpenseRequestStatus.PENDING_PAYMENT, ExpenseRequestStatus.REJECTED, ExpenseRequestStatus.PAID));
        List<ExpenseRequestProjection> requests = expenseRequestTodos.stream().map(todo -> projectionFactory.createProjection(
                ExpenseRequestProjection.class, todo)).collect(Collectors.toList());
        return new ResponseEntity(requests, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(value = {"/createFreedomOperatorExpenseRequest"},method = {RequestMethod.GET})
    public Map<String, Object> createFreedomOperatorExpenseRequest(@RequestParam Long freedomOperatorId, @RequestParam Double amount,
                                                                   @RequestParam String fopPaymentType,
                                                                   @RequestParam(name = "creatorId", required = false) Long creatorId,
                                                                   @RequestParam String notes,
                                                                   @RequestParam Long beneficiaryId) {
        Map<String, Object> resultObj = new HashMap<>();
        try {
            FreedomOperator freedomOperator = freedomOperatorRepository.findOne(freedomOperatorId);
            Expense expense = freedomOperator.getExpense();
            if (expense == null)
                throw new RuntimeException("The Expense setup doesn't exist, please add it.");
            if (freedomOperator == null)
                throw new RuntimeException("Freedom Operator doesn't exist.");
            if (expense.getSuppliers() == null || expense.getSuppliers().isEmpty())
                throw new RuntimeException(expense.getName() + " expense must have at least one supplier");

            User creator = creatorId != null ? Setup.getRepository(UserRepository.class).findOne(creatorId) : null;

            ExpenseRequestTodo expenseRequestTodo = new ExpenseRequestTodo();
            expenseRequestTodo.setRequestedBy(creator);
            expenseRequestTodo.setNotes(notes);
            expenseRequestTodo.setExpense(expense);
            expenseRequestTodo.setStatus(ExpenseRequestStatus.PENDING);
            expenseRequestTodo.setCurrency(currencyExchangeSevice.getSpecificCurrency(AccountingModule.EXPENSE_CURRENCY_AED));
            expenseRequestTodo.setBeneficiaryId(beneficiaryId);
            expenseRequestTodo.setBeneficiaryType(SUPPLIER);

            switch (fopPaymentType) {
                case "CREDIT_CARD":
                    expenseRequestTodo.setPaymentMethod(ExpensePaymentMethod.CREDIT_CARD);
                    break;
                case "CASH":
                    expenseRequestTodo.setPaymentMethod(ExpensePaymentMethod.CASH);
                    break;
                case "WIRE_TRANSFER":
                    expenseRequestTodo.setPaymentMethod(ExpensePaymentMethod.BANK_TRANSFER);
                    break;
            }

            expenseRequestTodo.setExpenseRequestType(ExpenseRequestType.NEW_REQUEST);
            expenseRequestTodo.setAmount(amount);
            expenseRequestTodo.setLinkedToFopRequest(true);

            expenseRequestTodo = (ExpenseRequestTodo) this.createEntity(expenseRequestTodo).getBody();
            resultObj.put("id", expenseRequestTodo.getId());
        } catch (Exception e) {
            resultObj.put("error", e.getMessage()) ;
        }
        return resultObj;
    }

    //ACC-8508
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT)
    @EnableSwaggerMethod
    @ApiCacheable
    @GetMapping(value = "/getMaidExpenses")
    public ResponseEntity<?> getMaidExpenses(
            @RequestParam(required = false) String mobileNumber,
            @RequestParam(required = false) String eidNumber,
            @RequestParam(required = false) String firstName,
            @RequestParam(required = false) String middleName,
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false, name = "contractID") Long contractId) {

        return ResponseEntity.ok(expensePaymentService.getMaidExpenses(
                mobileNumber, eidNumber, firstName, middleName, lastName, contractId));
    }

    // ACC-9104
    @JwtSecured
    @UsedBy(others = UsedBy.Others.New_GPT)
    @PostMapping(value = "/addExpensesByGPT")
    public ResponseEntity<?> addExpensesByGPT(
            @RequestParam("mobileNumber") String mobileNumber,
            @RequestParam("expenseType") String expenseType,
            @RequestBody ExpenseRequestForMaidByGPTDTO body) {

        Map<String, Object> result = new HashMap<>();
        try {
            expenseRequestTodoService.addExpensesForMaidByGPT(mobileNumber, expenseType, body);
            result.put("requestSuccessful", true);
        } catch (Exception e) {
            result.put("requestSuccessful", false);
            result.put("errorReason", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    // ACC-6275
    @PreAuthorize("hasPermission('expenseRequestTodo','getTaxiReimbursementBuckets')")
    @GetMapping(value = "/getTaxiReimbursementBuckets")
    public ResponseEntity<?> getTaxiReimbursementBuckets(String relatedToType) {

        Expense expense = expenseRepository.findOneByCode(relatedToType.equalsIgnoreCase("MAID") ?
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_TAXI_REIMBURSEMENT_EXPENSE_CODE) :
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_TAXI_REIMBURSEMENT_APPLICANT_EXPENSE_CODE));

        Map<String, Object> r = new HashMap<>();
        r.put("fromCashBucket", expense != null && expense.getPaidOnTheSpotCash() && expense.getFromCashBucket() != null ?
                project(expense.getFromCashBucket(), BucketsController.BucketProjectionIdName.class) : "");

        r.put("fromCreditCardBucket", expense != null && expense.getPaidOnTheSpotCreditCard() && expense.getFromCreditCardBucket() != null ?
                project(expense.getFromCreditCardBucket(), BucketsController.BucketProjectionIdName.class) : "");

        r.put("fromCashBuckets", expense != null && expense.getPaidOnTheSpotCash() && expense.getFromCashBuckets() != null ?
                project(expense.getFromCashBuckets(), BucketsController.BucketProjectionIdName.class) : "");

        r.put("fromCreditCardBuckets", expense != null && expense.getPaidOnTheSpotCreditCard() && expense.getFromCreditCardBuckets() != null ?
                project(expense.getFromCreditCardBuckets(), BucketsController.BucketProjectionIdName.class) : "");

        return ResponseEntity.ok(r);
    }

    @PreAuthorize("hasPermission('expenseRequestTodo', 'validateAndPrepareEntity')")
    @PostMapping("/validateAndPrepareEntity")
    public ResponseEntity<?> validateAndPrepareEntityAPI(@RequestBody ExpenseRequestTodo todo, Long userId) {
        HashMap<String, Object> result = new HashMap<>();
        result.put("valid", true);
        result.put("errorMessage", "");
        try {
            User u = Setup.getRepository(UserRepository.class).findOne(userId);
            if (u == null) {
                result.put("valid", false);
                result.put("errorMessage", "you are not authorized!");
            } else {
                validateAndPrepareEntity(todo, u);
            }
        } catch (BusinessException e) {
            e.printStackTrace();
            result.put("valid", false);
            result.put("errorMessage", e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("valid", false);
            result.put("errorMessage", "Something went wrong");
        }
        return ResponseEntity.ok(result);
    }

    // ACC-8171
    @UsedBy(modules = UsedBy.Modules.Recruitment)
    @PreAuthorize("hasPermission('expenseRequestTodo', 'createClosedExpenseRequestTodo')")
    @PostMapping("/createClosedExpenseRequestTodo")
    @Transactional
    public ResponseEntity<?> createClosedExpenseRequestTodo(
            @RequestParam(name = "userId") Long userId,
            @RequestParam(name = "fromBucketId") Long fromBucketId,
            @RequestBody Map<String, Object> body) {

        try {
            logger.info("requestBody for API : '/expenseRequestTodo/createClosedExpenseRequestTodo'" + getObjectMapper().writeValueAsString(body));
        } catch (Exception e) {
            e.printStackTrace();
        }

        User user = Setup.getRepository(UserRepository.class).findOne(userId);
        Bucket fromBucket = Setup.getRepository(BucketRepository.class).findOne(fromBucketId);

        // validate body info
        ExpenseRequestTodo expenseRequestTodo;
        List<Attachment> attachments;
        try {
            expenseRequestTodo = getObjectMapper().convertValue(body.get("expenseRequestTodo"), ExpenseRequestTodo.class);
            expenseRequestTodo.setAttachments(expenseRequestTodo
                    .getAttachments()
                    .stream()
                    .map(a -> Storage.cloneTemporary(a, a.getTag()))
                    .collect(Collectors.toList()));

            AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
            attachments = ((List<Object>) body.get("attachments")).stream()
                    .map(a -> {
                       Attachment att = getObjectMapper().convertValue(a, Attachment.class);
                       return attachementRepository.findOne(att.getId());
                    })
                    .collect(Collectors.toList());

            if (user == null || fromBucket == null || attachments.isEmpty()) {
                throw new RuntimeException("The operation failed due to a technical problem");
            }
        } catch (Exception e) {
            throw new RuntimeException("The operation failed due to a technical problem");
        }

        // Create ExpenseRequestTodo
        expenseRequestTodo = (ExpenseRequestTodo) createExpenseRequestTodo(expenseRequestTodo, user).getBody();

        if (expenseRequestTodo == null) {
            throw new RuntimeException("The operation failed due to a technical problem");
        }

        expenseRequestTodo = expenseRequestTodoRepository.findOne(expenseRequestTodo.getId());

        if (expenseRequestTodo.getExpensePayment() == null) {
            throw new BusinessException(
                    (expenseRequestTodo.getExpense().getCaption() != null ?
                        expenseRequestTodo.getExpense().getCaption() :
                        expenseRequestTodo.getExpense().getName()) +
                    " is not an auto-approved expense");
        }

        // Pay Expense Payment
        ExpensePayment paymentReq = new ExpensePayment();
        paymentReq.setFromBucket(fromBucket);
        paymentReq.addAttachment(Storage.cloneTemporary(attachments.get(0), AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString()));

        Setup.getApplicationContext()
                .getBean(ExpensePaymentService.class)
                .payCreditCardPayment(expenseRequestTodo.getExpensePayment(), paymentReq, user);

        Map<String, Object> response = new HashMap<>();
        response.put("id", expenseRequestTodo.getId());

        return ResponseEntity.ok(response);
    }

    @PreAuthorize("hasPermission('expenseRequestTodo', 'deleteEntityWithNotes')")
    @PostMapping(value = "/deleteEntityWithNotes/{id}")
    public ResponseEntity<?> deleteEntityWithNotes(
            @PathVariable("id") ExpenseRequestTodo todo,
            @RequestBody String notes) {

        todo.setDeleted(true);
        todo.setNotes((todo.getNotes() != null && !todo.getNotes().isEmpty() ? (todo.getNotes() + " / " ) : "") + notes);
        expenseRequestTodoRepository.silentSave(todo);

        Map<String, String> parameters = new HashMap<>();
        parameters.put("expense_name", todo.getExpense().getName());
        parameters.put("user_name", CurrentRequest.getUser() != null ? CurrentRequest.getUser().getName() : "");
        parameters.put("expense_Description", todo.getDescription());
        parameters.put("expense_amount", PaymentHelper.df_two_decimal.format(todo.getAmount()) + " " + todo.getCurrency().getName());
        parameters.put("date_Of_request", new LocalDateTime(todo.getCreationDate().getTime()).toString("yyyy-MM-dd HH:mm"));
        parameters.put("requested_by", todo.getRequestedBy() != null && todo.getRequestedBy().getFullName() != null ? todo.getRequestedBy().getFullName() : "");
        parameters.put("notes", notes);

        if (!Arrays.asList(
                ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString(),
                ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString()).contains(todo.getTaskName())) return okResponse();

        String rec = "" ;
        if (ExpenseRequestTodoFlowActions.APPROVE.equals(todo.getManagerAction()) ||
                todo.getTaskName().equals(ExpenseRequestTodoType.WAITING_MANAGER_APPROVAL.toString())) {
            if(todo.getApproveHolder() != null) {
                rec = todo.getApproveHolder().getEmail();
            } else if (todo.getApproveHolderEmail() != null) {
                rec = todo.getApproveHolderEmail();
            }
        }

        if (todo.getTaskName().equals(ExpenseRequestTodoType.WAITING_COO_APPROVAL.toString())) {
            User ceoUser = UserHelper.getCeoUser();
            if (ceoUser != null && ceoUser.getEmail() != null) {
                if (!rec.isEmpty()) {
                    rec += ";";
                }
                rec += ceoUser.getEmail();
            }
        }

        if (rec.isEmpty()) return okResponse();

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff(
                        "expense_requested_deleted_email",
                        parameters,
                        rec,
                        "The requested " + todo.getExpense().getName() + " was deleted by " + CurrentRequest.getUser().getName());

        return ResponseEntity.ok("email send successfully");
    }

    /**
     * ِMc-150
     * API to mark expense request as matched and auto-confirm for automation workflows
     * This creates a transaction directly from expense request todo and confirms the expense
     * Used by n8n automation for non-visa Qashio expenses
     */
    @Transactional
    @EnableSwaggerMethod
    @GetMapping("/createTransaction/{id}")
    public ResponseEntity<?> createTransaction(@PathVariable("id") Long expenseRequestId) {

        ExpenseRequestTodo expenseRequestTodo = expenseRequestTodoRepository.findOne(expenseRequestId);

        if (expenseRequestTodo == null) {
            throw new RuntimeException("Expense request not found");
        }

        if (expenseRequestTodo.getConfirmed()) {
            throw new RuntimeException("Expense request is already confirmed");
        }

        if (expenseRequestTodo.getExpensePayment() == null) {
            throw new RuntimeException("Expense request must have an associated payment to be marked as matched");
        }

        if (expenseRequestTodo.getPaymentMethod() == null || !expenseRequestTodo.getPaymentMethod().equals(ExpensePaymentMethod.CREDIT_CARD)) {
            throw new RuntimeException("Only expense requests with CREDIT CARD payment method are allowed");
        }

        // Create transaction directly from expense request todo and expense payment
        ExpensePayment expensePayment = expenseRequestTodo.getExpensePayment();
        Transaction transaction = createTransactionFromExpenseRequest(expenseRequestTodo, expensePayment);

        // Trigger after confirmation logic
        expenseService.afterConfirmationTrigger(expenseRequestTodo,
                CreditCardReconciliationStatementDetails.MatchType.EXISTING_EXPENSE,
                transaction);

        // Apply reconciliation logic to complete the expense payment
        expenseService.reconciliatorConfirmationStep(expensePayment);

        return ResponseEntity.ok().build();
    }

    /**
     * Helper method to create transaction directly from expense request todo and expense payment
     */
    private Transaction createTransactionFromExpenseRequest(
            ExpenseRequestTodo expenseRequestTodo,
            ExpensePayment expensePayment) {

        Transaction transaction = new Transaction();
        transaction.setDate(new java.sql.Date(new Date().getTime()));
        transaction.setPnlValueDate(new Date());
        transaction.setAmount(expensePayment.getAmount());
        transaction.setFromBucket(expensePayment.getFromBucket());
        transaction.setToBucket(expensePayment.getToBucket());
        transaction.setExpense(expenseRequestTodo.getExpenseToPost() != null ?
                expenseRequestTodo.getExpenseToPost() : expenseRequestTodo.getExpense());
        transaction.setDescription(expenseRequestTodo.getDescription());
        transaction.setPaymentType(PaymentMethod.valueOf(expenseRequestTodo.getPaymentMethod().toString()));

        // Handle VAT
        if (expensePayment.getVatAmount() != null && !expensePayment.getVatAmount().equals(0D)) {
            transaction.setVatType(VatType.IN);
            transaction.setVatAmount(expensePayment.getVatAmount());
            transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE,
                    AccountingModule.PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM));
        } else {
            transaction.setVatType(null);
            transaction.setVatAmount(null);
            transaction.setLicense(PicklistHelper.getItem(AccountingModule.PICKLIST_TRANSACTION_LICENSE, "no_vat"));
        }

        // Copy attachments
        for (Attachment attachment : expensePaymentService.getAttachments(expensePayment)) {
            Attachment cloned = Storage.cloneTemporary(attachment, attachment.getTag());
            transaction.addAttachment(cloned);
        }

        // Set missing tax invoice flag
        transaction.setMissingTaxInvoice(parsingService.isMissingTaInvoice(transaction.getAttachments(), transaction.getVatAmount()));

        // Set expense payment ID
        transaction.setExpensePaymentId(expensePayment.getId());

        // Initialize transaction related to data
        parsingService.initTransactionRelatedToData(transaction,
                expenseRequestTodo.getRelatedToType(),
                expenseRequestTodo.getRelatedToId());

        // Create the transaction
        transaction = (Transaction) Setup.getApplicationContext().getBean(TransactionsController.class).createEntity(transaction).getBody();

        return transaction;
    }
}