package com.magnamedia.controller;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Category;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.dto.PurchasingToDoInOrderHistoryDto;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.repository.PurchaseOrderRepository;
import com.magnamedia.repository.PurchasingToDoRepository;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <PERSON> (Feb 04, 2021)
 */
@RestController
@RequestMapping("/purchase-order")
public class PurchaseOrderController extends BaseRepositoryController<PurchaseOrder> {
    @Autowired
    PurchaseOrderRepository purchaseOrderRepository;
    @Autowired
    PurchasingToDoRepository purchasingToDoRepository;

    @Override
    public BaseRepository<PurchaseOrder> getRepository() {
        return purchaseOrderRepository;
    }

    @NoPermission
    @RequestMapping("/get-pending-purchase-request-list")
    public ResponseEntity<?> getPendingPurchaseRequestList() {
        List<PurchasingToDoInOrderHistoryDto> purchasingToDos = purchasingToDoRepository
                .findByCompletedFalseAndStoppedFalseAndTaskNameIgnoreCaseIn(Arrays.asList(
                        PurchasingToDoType.PM_GET_BEST_SUPPLIER.toString(),
                        PurchasingToDoType.PA_CONFIRM_BEST_SUPPLIER.toString(),
                        PurchasingToDoType.PM_GET_BETTER_SUPPLIER.toString()
                )).stream().map(PurchasingToDoInOrderHistoryDto::new).collect(Collectors.toList());
        return ResponseEntity.ok(purchasingToDos);
    }

    @NoPermission
    @RequestMapping(value = "/page-order")
    public ResponseEntity<?> pageOrder(Pageable pageable,
                                       @RequestParam(value = "supplierId", required = false) Supplier supplier,
                                       @RequestParam(value = "categoryId", required = false) Category category,
                                       @RequestParam(value = "date", required = false)
                                       @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                       @RequestParam(value = "status", required = false) String status
    ) {

        Page<PurchaseOrder> purchaseOrders = purchaseOrderRepository.getOrderHistoryWithSearch(
                supplier, category,
                date != null ? DateUtil.getDayStart(date) : null,
                date != null ? DateUtil.getDayEnd(date) : null,
                status, pageable);
        return ResponseEntity.ok(purchaseOrders);
    }

    @NoPermission
    @RequestMapping("/get-items-of-order/{purchaseOrder}")
    public ResponseEntity<?> getPendingPurchaseRequestList(@PathVariable("purchaseOrder") PurchaseOrder purchaseOrder) {
        return ResponseEntity.ok(purchaseOrder.getPurchaseItems());
    }
}
