package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.FreedomOperator;
import com.magnamedia.entity.projection.FreedomOperatorProjection;
import com.magnamedia.repository.FreedomOperatorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on 2017-06-28
 *
 */
@RequestMapping("/freedomoperator")
@RestController
public class FreedomOperatorController extends BaseRepositoryController<FreedomOperator> {

    @Autowired
    private FreedomOperatorRepository freedomOperatorRepository;

    @Autowired
    private ProjectionFactory projectionFactory;
    
    @Override
    public BaseRepository<FreedomOperator> getRepository() {
        return freedomOperatorRepository;

    }
        
    @PreAuthorize("hasPermission('freedomoperator','search')")
    @RequestMapping(value = "/search/page", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> searchStatus(
            @RequestParam(value = "search", required = false) String search,
            Pageable pageable) {

        SelectQuery<FreedomOperator> query = new SelectQuery<>(FreedomOperator.class);
        if (search != null){
            SelectFilter filter = new SelectFilter("name", "like", "%"+ search +"%");
            filter = filter.or("code", "like", "%"+ search +"%");
            query.filterBy(filter);
        }
        return new ResponseEntity<>(query.execute(pageable).map(obj
                -> projectionFactory.createProjection(
                        FreedomOperatorProjection.class, obj)), HttpStatus.OK);
    }

}
