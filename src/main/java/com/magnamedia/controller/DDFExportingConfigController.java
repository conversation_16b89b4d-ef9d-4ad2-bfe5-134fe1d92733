package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.DDFExportingConfig;
import com.magnamedia.repository.DDFExportingConfigRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 29, 2020
 *         Jirra ACC-1435
 */

@RestController
@RequestMapping("/ddfexportingconfig")
public class DDFExportingConfigController extends BaseRepositoryController<DDFExportingConfig> {
    @Autowired
    private DDFExportingConfigRepository ddfExportingConfigRepository;

    @Transactional
    @Override
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        if (objectNode.has("name"))
            throw new RuntimeException("Name is not editable");

        return super.update(objectNode);
    }

    @Override
    public BaseRepository<DDFExportingConfig> getRepository() {
        return ddfExportingConfigRepository;
    }
}
