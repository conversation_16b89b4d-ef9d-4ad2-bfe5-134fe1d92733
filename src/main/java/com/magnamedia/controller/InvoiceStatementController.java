package com.magnamedia.controller;

import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepositoryParent;
import com.magnamedia.entity.InvoiceStatement;
import com.magnamedia.entity.InvoiceStatementTransaction;
import com.magnamedia.entity.dto.InvoiceStatementCalculationsDto;
import com.magnamedia.entity.dto.InvoiceStatementTransactionPayDto;
import com.magnamedia.entity.dto.InvoiceStatementTransactionSearchDto;
import com.magnamedia.entity.projection.InvoiceStatementTransactionMatchedCSVProjection;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.InvoiceStatementStatus;
import com.magnamedia.extra.InvoiceStatementTransactionType;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.InvoiceAnnexureMailProcessor;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.repository.InvoiceStatementRepository;
import com.magnamedia.repository.InvoiceStatementTransactionRepository;
import com.magnamedia.service.InvoiceStatementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;


@RestController
@RequestMapping("/invoiceStatement")
public class InvoiceStatementController extends BaseRepositoryController<InvoiceStatement> {

    private static final Logger logger = Logger.getLogger(InvoiceStatementController.class.getName());

    @Autowired
    private InvoiceStatementRepository invoiceStatementRepository;

    @Autowired
    private InvoiceStatementTransactionRepository invoiceStatementTransactionRepository;

    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    @Autowired
    private InvoiceStatementService invoiceStatementService;

    @Autowired
    private InvoiceAnnexureMailProcessor invoiceAnnexureMailProcessor;

    @Override
    public BaseRepositoryParent<InvoiceStatement> getRepository() {return invoiceStatementRepository;}

    @PreAuthorize("hasPermission('invoiceStatement','allStatements')")
    @RequestMapping(value = "/allStatements", method = RequestMethod.GET)
    public ResponseEntity<?> allStatements(Pageable pageable) {
        SelectQuery query = new SelectQuery(InvoiceStatement.class);
        query.filterBy("status", "=", InvoiceStatementStatus.PENDING);
        query.sortBy("creationDate", false);

        return new ResponseEntity<>(query.execute(pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','searchTransactions')")
    @RequestMapping(value = "/search/{statementId}", method = RequestMethod.POST)
    public ResponseEntity<?> searchTransactions(
            @PathVariable("statementId") Long statementId,
            @RequestBody InvoiceStatementTransactionSearchDto searchDto,
            Pageable pageable) {

        return new ResponseEntity<>(invoiceStatementService.searchTransactions(statementId, searchDto, pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','performRematch')")
    @RequestMapping(value = "/rematch/{statementId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> performRematch(@PathVariable("statementId") Long statementId) {

        InvoiceStatementService.RematchResult result = invoiceStatementService.performRematch(statementId);

        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','payTransactions')")
    @RequestMapping(value = "/pay-transactions", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> payTransactions(@RequestBody InvoiceStatementTransactionPayDto payDto) {
        if (payDto.getTransactionItems() == null || payDto.getTransactionItems().isEmpty()) {
            return new ResponseEntity<>("No transaction items provided", HttpStatus.BAD_REQUEST);
        }

        logger.info("items size: " + payDto.getTransactionItems().size());

        invoiceStatementService.processInvoiceStatementTransactionPayment(payDto);

        return new ResponseEntity<>("success", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','payTransactions')")
    @RequestMapping(value = "/pay-all-transactions/{statementId}", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity<?> payAllTransactions(
            @PathVariable("statementId") Long statementId,
            @RequestBody InvoiceStatementTransactionPayDto payDto) {

        invoiceStatementService.processAllMatchedTransactionsPayment(statementId, payDto);
        return new ResponseEntity<>("success", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','getCalculations')")
    @RequestMapping(value = "/calculations/{statementId}", method = RequestMethod.GET)
    @Transactional
    public ResponseEntity<?> getCalculations(@PathVariable("statementId") Long statementId) {
        InvoiceStatementCalculationsDto calculations = invoiceStatementService.getInvoiceStatementCalculations(statementId);
        return new ResponseEntity<>(calculations, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','updateTransactionAmount')")
    @PostMapping("/updateTransactionAmount/{transactionId}")
    @Transactional
    public ResponseEntity<?> updateTransactionAmount(
            @PathVariable("transactionId") Long transactionId,
            @RequestParam("amount") Double amount,
            @RequestParam(value = "expenseRequestTodoId") Long expenseRequestTodoId) {

        if (expenseRequestTodoId != null) {
            ExpenseRequestTodo expenseRequestTodo = expenseRequestTodoRepository.findOne(expenseRequestTodoId);
            if (expenseRequestTodo != null) {
                expenseRequestTodo.setAmount(amount);
                expenseRequestTodo.setAmountToPay(amount);
                expenseRequestTodoRepository.save(expenseRequestTodo);
                logger.info("Updated ExpenseRequestTodo amount for ID: " + expenseRequestTodoId + " to amount: " + amount);
            } else {
                logger.warning("ExpenseRequestTodo not found with ID: " + expenseRequestTodoId);
            }
        }

        InvoiceStatementTransaction transaction = invoiceStatementTransactionRepository.findOne(transactionId);
        if (transaction == null) {
            return new ResponseEntity<>("Transaction not found with ID: " + transactionId, HttpStatus.NOT_FOUND);
        }
        transaction.setAmount(amount);

        InvoiceStatementTransaction updatedTransaction = invoiceStatementTransactionRepository.save(transaction);
        logger.info("Updated transaction amount for ID: " + transactionId + " to amount: " + amount);

        return new ResponseEntity<>(updatedTransaction, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','updateTransactionAmount')")
    @PostMapping("/updatePendingMatchedTransactionAmount")
    @Transactional
    public ResponseEntity<?> updatePendingMatchedTransactionAmount(
            @RequestParam("amount") Double amount,
            @RequestParam(value = "expenseRequestTodoId") Long expenseRequestTodoId) {

        if (expenseRequestTodoId != null) {
            ExpenseRequestTodo expenseRequestTodo = expenseRequestTodoRepository.findOne(expenseRequestTodoId);
            if (expenseRequestTodo != null) {
                expenseRequestTodo.setAmount(amount);
                expenseRequestTodo.setAmountToPay(amount);
                expenseRequestTodoRepository.save(expenseRequestTodo);
                logger.info("Updated ExpenseRequestTodo amount for ID: " + expenseRequestTodoId + " to amount: " + amount);
            } else {
                logger.warning("ExpenseRequestTodo not found with ID: " + expenseRequestTodoId);
            }
        }

        return new ResponseEntity<>("Transaction amount updated successfully", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','deleteTransaction')")
    @PostMapping("/deleteTransaction/{transactionId}")
    @Transactional
    public ResponseEntity<?> deleteTransaction(@PathVariable("transactionId") Long transactionId) {

        InvoiceStatementTransaction transaction = invoiceStatementTransactionRepository.findOne(transactionId);
        if (transaction == null) {
            return new ResponseEntity<>("Transaction not found with ID: " + transactionId, HttpStatus.NOT_FOUND);
        }

        transaction.setType(InvoiceStatementTransactionType.DELETED);
        InvoiceStatementTransaction updatedTransaction = invoiceStatementTransactionRepository.save(transaction);
        logger.info("Deleted InvoiceStatementTransaction with ID: " + transactionId + " by changing type to DELETED");

        return new ResponseEntity<>("Transaction deleted successfully", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('invoiceStatement','testPdfProcessor')")
    @RequestMapping(value = "/test-pdf-processor", method = RequestMethod.POST)
    public ResponseEntity<?> testPdfProcessor(
            @RequestParam("annexureFile") MultipartFile annexureFile,
            @RequestParam("invoiceFile") MultipartFile invoiceFile) throws IOException {

        if (annexureFile.isEmpty()) {
            return new ResponseEntity<>("No annexure file provided", HttpStatus.BAD_REQUEST);
        }

        if (!annexureFile.getContentType().equals("application/pdf")) {
            return new ResponseEntity<>("Annexure file must be a PDF", HttpStatus.BAD_REQUEST);
        }

        if (invoiceFile != null && !invoiceFile.isEmpty() && !invoiceFile.getContentType().equals("application/pdf")) {
            return new ResponseEntity<>("Invoice file must be a PDF", HttpStatus.BAD_REQUEST);
        }

        logger.info("Processing PDF files - Annexure: " + annexureFile.getOriginalFilename() +
                   (invoiceFile != null && !invoiceFile.isEmpty() ? ", Invoice: " + invoiceFile.getOriginalFilename() : ""));

        File tempAnnexureFile = convertMultipartFileToFile(annexureFile, "annexure_test_", ".pdf");
        File tempInvoiceFile = null;

        if (invoiceFile != null && !invoiceFile.isEmpty()) {
            tempInvoiceFile = convertMultipartFileToFile(invoiceFile, "invoice_test_", ".pdf");
        }

        try {
            List<File> attachments = new ArrayList<>();

            File renamedAnnexureFile = new File(tempAnnexureFile.getParent(), "Copy of Annexure_test.pdf");
            if (tempAnnexureFile.renameTo(renamedAnnexureFile)) {
                attachments.add(renamedAnnexureFile);
            } else {
                attachments.add(tempAnnexureFile);
            }

            if (tempInvoiceFile != null) {
                File renamedInvoiceFile = new File(tempInvoiceFile.getParent(), "AD Medical Invoice_test.pdf");
                if (tempInvoiceFile.renameTo(renamedInvoiceFile)) {
                    attachments.add(renamedInvoiceFile);
                } else {
                    attachments.add(tempInvoiceFile);
                }
            }

            invoiceAnnexureMailProcessor.process("<EMAIL>",
                    "CHSC Al Jazira Clinic - MAIDS CC DOMESTIC WORKERSS SERVICES - Invoice", "", attachments);

            logger.info("Successfully processed PDF files");

            return new ResponseEntity<>("success", HttpStatus.OK);

        } finally {
            // Clean up temporary files
            cleanupTempFile(tempAnnexureFile);
            cleanupTempFile(tempInvoiceFile);
        }
    }

    private File convertMultipartFileToFile(MultipartFile multipartFile, String prefix, String suffix) throws IOException {
        File tempFile = File.createTempFile(prefix, suffix);
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(multipartFile.getBytes());
        }
        return tempFile;
    }

    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            boolean deleted = tempFile.delete();
            if (!deleted) {
                logger.warning("Failed to delete temporary file: " + tempFile.getAbsolutePath());
            }
        }
    }

    @PreAuthorize("hasPermission('invoiceStatement','exportToCSV')")
    @RequestMapping(value = "/exportToCSV/{statementId}", method = RequestMethod.POST)
    public void exportToCSV(
            @PathVariable("statementId") Long statementId,
            @RequestBody InvoiceStatementTransactionSearchDto searchDto,
            HttpServletResponse response) {

        InputStream inputStream = null;

        try {
            Page<InvoiceStatementTransaction> matchedTransactionsPage =
                invoiceStatementTransactionRepository.searchTransactions(
                    statementId,
                    InvoiceStatementTransactionType.Matched,
                    searchDto.getMaidName(),
                    searchDto.getPassportNumber(),
                    searchDto.getFromDate(),
                    searchDto.getToDate(),
                    Pageable.unpaged()
                );

            List<InvoiceStatementTransaction> matchedTransactions = matchedTransactionsPage.getContent();

            if (matchedTransactions.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NO_CONTENT);
                return;
            }

            String[] headers = {"Date", "Expense Name", "Employee type", "Maid Passport Number" , "Amount"};
            String[] columns = {"date", "expenseName", "relatedTo", "maidPassportNumber", "amount"};

            String fileName = "Capital_Medical_Centre_for_Health" + "_Invoice_" + DateUtil.formatDateDashedV2(new Date()) + ".csv";

            File csvFile = CsvHelper.generateCsv(
                matchedTransactions,
                InvoiceStatementTransactionMatchedCSVProjection.class,
                headers,
                columns,
                fileName
            );

            inputStream = new FileInputStream(csvFile);
            createDownloadResponse(response, fileName, inputStream);

        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error exporting MATCHED transactions to CSV", e);
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

}
