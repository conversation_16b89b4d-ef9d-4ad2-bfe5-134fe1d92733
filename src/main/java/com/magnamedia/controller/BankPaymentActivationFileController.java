package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.BankPaymentActivationFile;
import com.magnamedia.entity.BankPaymentActivationRecord;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.projection.BankPaymentActivationFileProjection;
import com.magnamedia.entity.projection.NotMatchedPaymentProjection;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.PaymentService;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>> Created on Dec 27, 2018 Jirra
 *         ACC-332
 */
@RequestMapping("/bankpaymentactivationfiles")
@RestController
public class BankPaymentActivationFileController extends BaseRepositoryController<BankPaymentActivationFile> {

    @Autowired
    private BankPaymentActivationFileRepository bankPaymentActivationFileRepository;
    @Autowired
    private BankPaymentActivationRecordRepository bankPaymentActivationRecordRepository;
    @Autowired
    private RevenueRepository revenueRepository;
    @Autowired
    private BucketRepository bucketRepositor;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private TransactionsController transactionsController;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private PaymentService paymentService;

    @Override
    public BaseRepository<BankPaymentActivationFile> getRepository() {
        return bankPaymentActivationFileRepository;
    }

    // Jirra ACC-332
    @PreAuthorize("hasPermission('bankpaymentactivationfiles','list')")
    @RequestMapping(value = "/projectedlist", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> projectedList() {

        SelectQuery<BankPaymentActivationFile> query = new SelectQuery<>(BankPaymentActivationFile.class);
        query.filterBy("hidden", "=", false);
        //Jirra ACC-404
        query.sortBy("creationDate", true, false);

        return new ResponseEntity<>(query.execute().stream()
                .map(obj -> projectionFactory.createProjection(BankPaymentActivationFileProjection.class, obj))
                .collect(Collectors.toList()), HttpStatus.OK);
    }

    // Jirra ACC-612
    @PreAuthorize("hasPermission('bankpaymentactivationfiles','get')")
    @RequestMapping(value = "/getrecords/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> getRecords(
            @PathVariable("id") BankPaymentActivationFile file,
            @RequestParam(required = false, value = "matched") boolean matched,
            Pageable pageable) {

        SelectQuery<BankPaymentActivationRecord> query =
                new SelectQuery<>(BankPaymentActivationRecord.class);
        query.filterBy("bankPaymentActivationFile", "=", file);
        if (matched)
            query.filterBy("payment", "IS NOT NULL", null);
        else
            query.filterBy("payment", "IS NULL", null);
        return new ResponseEntity<>(
                query.execute(pageable),
                HttpStatus.OK);
    }

    // Jirra ACC-332
    @PreAuthorize("hasPermission('bankpaymentactivationfiles','get')")
    @RequestMapping(value = "/notmatchedpayments/{id}", method = RequestMethod.GET)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> notMatchedPayments(
            @PathVariable("id") BankPaymentActivationFile file,
            @RequestParam(required = false, value = "date")
            @DateTimeFormat(pattern = "yyyy-MM-dd") java.util.Date date,
            Pageable pageable) {

        List<Long> ids = new ArrayList<>();
        for (BankPaymentActivationRecord r : file.getRecords())
            if (r.getPayment() != null)
                ids.add(r.getPayment().getId());

        Page<Payment> notMatchedPayments;

        if (ids.isEmpty())
            notMatchedPayments = paymentRepository.findByDateOfPaymentBetweenAndStatus(
                    new Date((new LocalDate(date.getTime())).dayOfMonth().withMinimumValue().toDate().getTime()),
                    new Date(date.getTime()), PaymentStatus.PDC, pageable);
        else
            notMatchedPayments = paymentRepository.findByDateOfPaymentBetweenAndStatusAndIdNotIn(
                    new Date((new LocalDate(date.getTime())).dayOfMonth().withMinimumValue().toDate().getTime()),
                    new Date(date.getTime()), PaymentStatus.PDC, ids, pageable);

        return new ResponseEntity<>(
                notMatchedPayments
                        .map(obj -> projectionFactory.createProjection(NotMatchedPaymentProjection.class, obj)),
                HttpStatus.OK);
    }

    // Jirra ACC-333
    @PreAuthorize("hasPermission('bankpaymentactivationfiles','confirmpayment')")
    @RequestMapping(value = "/confirmpayment", method = RequestMethod.POST)
    @ResponseBody
    @Transactional
    @JsonView(ViewScope.Normal.class)
    public ResponseEntity<?> confirmpayment(@RequestBody List<Long> ids) throws Exception {

        List<BankPaymentActivationRecord> result = new ArrayList<>();
        List<BankPaymentActivationRecord> records = bankPaymentActivationRecordRepository.findAll(ids);
        Double amount = 0D;
        for (BankPaymentActivationRecord r : records) {
            if (!r.isConfirmed() && r.getPayment() != null) {
                // fetch payment from database to get contract information in later adjusted end
                // date handle
                Payment p = paymentRepository.findOne(r.getPayment().getId());
                p.setStatus(r.getStatus());
                paymentService.forceUpdatePayment(p);
                if (r.getStatus().equals(PaymentStatus.RECEIVED))
                    amount += r.getAmount();
                r.setConfirmed(true);
                result.add(r);
            }
        }

        bankPaymentActivationRecordRepository.save(result);
        String bucketCode = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET);
        if (amount > 0) {
            Transaction t = new Transaction();
            t.setAmount(amount);
            t.setRevenue(revenueRepository.findByCode("FTR 02"));
            t.setToBucket(bucketRepositor.findByCode(bucketCode));
            t.setDate(new Date(new java.util.Date().getTime()));
            transactionsController.createEntity(t);
        }

        return new ResponseEntity<>(result.stream().map(x -> x.getId()).collect(Collectors.toList()), HttpStatus.OK);
    }
}
