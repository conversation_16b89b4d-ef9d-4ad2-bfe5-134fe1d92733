package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.MaintenanceRequest;
import com.magnamedia.entity.PurchaseOrder;
import com.magnamedia.entity.StockKeeperToDo;
import com.magnamedia.entity.dto.PurchaseBillInfoDto;
import com.magnamedia.entity.dto.StockKeeperTaskListDto;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.SalesBinderApiClient;
import com.magnamedia.helper.AttachmentHelper;
import com.magnamedia.module.type.MaintenanceRequestStatus;
import com.magnamedia.module.type.PurchaseOrderStatus;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.repository.MaintenanceRequestRepository;
import com.magnamedia.repository.PurchaseOrderRepository;
import com.magnamedia.repository.StockKeeperToDoRepository;
import com.magnamedia.service.PurchasingService;
import com.magnamedia.workflow.type.AttachmentTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mohammad Nosairat (Feb 10, 2021)
 */
@Transactional
@RestController
@RequestMapping("/stock-keeper")
public class StockKeeperToDoController extends BaseRepositoryController<StockKeeperToDo> {

    @Autowired
    StockKeeperToDoRepository stockKeeperToDoRepository;
    @Autowired
    AttachementRepository attachementRepository;
    @Autowired
    MaintenanceRequestRepository maintenanceRequestRepository;
    @Autowired
    PurchaseOrderRepository purchaseOrderRepository;
    @Autowired
    SalesBinderApiClient salesBinderApiClient;

    @NoPermission
    @RequestMapping("/get-stock-keeper-tasks")
    public ResponseEntity<?> getStockKeeperTasks() {

        List<StockKeeperTaskListDto> stockKeeperToDos = stockKeeperToDoRepository.findByClosedFalse().stream()
                .map(StockKeeperTaskListDto::new)
                .sorted(Comparator.comparing(StockKeeperTaskListDto::getCreationDate).reversed())
                .collect(Collectors.toList());

        return ResponseEntity.ok(stockKeeperToDos);
    }

    @PreAuthorize("hasPermission('stock-keeper','get-order-object')")
    @RequestMapping("/get-order-object/{stockKeeperToDoId}")
    public ResponseEntity<?> getOrderObject(@PathVariable("stockKeeperToDoId") StockKeeperToDo stockKeeperToDo) {
        return ResponseEntity.ok(stockKeeperToDo.getPurchaseOrder().getPurchaseItems());
    }

    @PreAuthorize("hasPermission('stock-keeper','confirm-receive-order')")
    @RequestMapping("/confirm-receive-order/{stockKeeperToDoId}")
    public ResponseEntity<?> confirmReceiveOrder(@PathVariable("stockKeeperToDoId") StockKeeperToDo stockKeeperToDo) {
        stockKeeperToDo.setClosed(true);
        stockKeeperToDoRepository.save(stockKeeperToDo);

        PurchaseOrder order = stockKeeperToDo.getPurchaseOrder();
        order.setStatus(PurchaseOrderStatus.RECEIVED);
        purchaseOrderRepository.save(order);
        purchasingService.updateLastAndCheapestSupplierInItems(order);
        salesBinderApiClient.createDocumentForPurchaseOrder(Collections.singletonList(order));

        return ResponseEntity.ok().build();
    }

    @Autowired
    PurchasingService purchasingService;

    //call when done stock keeper tood of the Maintenance todo
    @PreAuthorize("hasPermission('stock-keeper','done-maintenance-request')")
    @RequestMapping("/done-maintenance-request/{stockKeeperToDoId}")
    public ResponseEntity<?> doneMaintenanceRequest(@PathVariable("stockKeeperToDoId") StockKeeperToDo stockKeeperToDo) {
        if (stockKeeperToDo.getStatus().equals(StockKeeperToDo.StockKeeperToDoStatus.DONE))
            throw new RuntimeException("request is already done");
        if (stockKeeperToDo.getStatus().equals(StockKeeperToDo.StockKeeperToDoStatus.PAID))
            stockKeeperToDo.setClosed(true);
        stockKeeperToDo.setStatus(StockKeeperToDo.StockKeeperToDoStatus.DONE);
        stockKeeperToDoRepository.save(stockKeeperToDo);

        return ResponseEntity.ok().build();
    }

    @PreAuthorize("hasPermission('stock-keeper','get-bill-info-for-maintenance-request')")
    @RequestMapping("/get-bill-info-for-maintenance-request/{stockKeeperToDoId}")
    public ResponseEntity<?> purchaseInOneBill(@PathVariable("stockKeeperToDoId") StockKeeperToDo stockKeeperToDo) {
        PurchaseBillInfoDto dto = new PurchaseBillInfoDto();
        dto.setId(stockKeeperToDo.getId());
        dto.setPaymentMethods(stockKeeperToDo.getMaintenanceRequest().getPaymentMethod() != null ?
                Collections.singletonList(stockKeeperToDo.getMaintenanceRequest().getPaymentMethod()) :
                Collections.singletonList(stockKeeperToDo.getMaintenanceRequest().getExpenseRequestTodo().getPaymentMethod()));
        Boolean vatRegistered = stockKeeperToDo.getMaintenanceRequest().getSupplier().getVatRegistered();
        if (vatRegistered == null) {
            dto.setTaxable(stockKeeperToDo.getMaintenanceRequest().getTaxable());
        } else {
            dto.setTaxable(stockKeeperToDo.getMaintenanceRequest().getSupplier().getVatRegistered());
        }
        dto.setTotalBillAmount(stockKeeperToDo.getMaintenanceRequest().getCost());
        return ResponseEntity.ok(dto);
    }

    //call when pay stock keeper tood of the Maintenance todo
    @PreAuthorize("hasPermission('stock-keeper','purchase-in-one-bill')")
    @PostMapping("/purchase-in-one-bill/{stockKeeperToDoId}")
    public ResponseEntity<?> purchaseInOneBill(@PathVariable("stockKeeperToDoId") StockKeeperToDo stockKeeperToDo,
                                               @RequestBody PurchaseBillInfoDto billInfoDto) {
        if (stockKeeperToDo.getStatus().equals(StockKeeperToDo.StockKeeperToDoStatus.PAID))
            throw new RuntimeException("request is already PAID");
        if (stockKeeperToDo.getStatus().equals(StockKeeperToDo.StockKeeperToDoStatus.DONE))
            stockKeeperToDo.setClosed(true);

        stockKeeperToDo.setStatus(StockKeeperToDo.StockKeeperToDoStatus.PAID);
        stockKeeperToDoRepository.save(stockKeeperToDo);

        updateMaintenanceRequest(stockKeeperToDo, billInfoDto);

        updateExpenseRequest(stockKeeperToDo);

        return ResponseEntity.ok().build();
    }

    private void updateExpenseRequest(StockKeeperToDo stockKeeperToDo) {
        ExpenseRequestTodo request = stockKeeperToDo.getMaintenanceRequest().getExpenseRequestTodo();

        Attachment paymentInvoice = AttachmentHelper.getRequestAttachment(
                stockKeeperToDo.getMaintenanceRequest(), AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
        if (paymentInvoice != null) {
            Attachment requestInvoice = Storage.cloneTemporary(
                    paymentInvoice, AttachmentTag.EXPENSE_REQUEST_INVOICE.toString());
            request.addAttachment(requestInvoice);
        }
        Attachment paymentVatInvoice = AttachmentHelper.getRequestAttachment(
                stockKeeperToDo.getMaintenanceRequest(), AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
        if (paymentVatInvoice != null) {
            Attachment requestVatInvoice = Storage.cloneTemporary(
                    paymentVatInvoice, AttachmentTag.EXPENSE_REQUEST_VAT_INVOICE.toString());
            request.addAttachment(requestVatInvoice);
        }

        Setup.getRepository(ExpenseRequestTodoRepository.class).save(request);
        request.createPayment();
    }

    private void updateMaintenanceRequest(StockKeeperToDo stockKeeperToDo, PurchaseBillInfoDto billInfoDto) {
        MaintenanceRequest main = stockKeeperToDo.getMaintenanceRequest();
        main.setCost(billInfoDto.getTotalBillAmount());
        main.setVatAmount(billInfoDto.getVatAmount());
        main.setTaxable(billInfoDto.getTaxable());
        if (stockKeeperToDo.getStatus().equals(StockKeeperToDo.StockKeeperToDoStatus.PAID))
            main.setStatus(MaintenanceRequestStatus.PAID);
        if (stockKeeperToDo.getStatus().equals(StockKeeperToDo.StockKeeperToDoStatus.DONE))
            main.setStatus(MaintenanceRequestStatus.DONE);

        for (Attachment attachment : billInfoDto.getAttachments()) {
            main.addAttachment(attachementRepository.findOne(attachment.getId()));
        }
        maintenanceRequestRepository.save(main);
    }

    @Override
    public BaseRepository<StockKeeperToDo> getRepository() {
        return stockKeeperToDoRepository;
    }
}
