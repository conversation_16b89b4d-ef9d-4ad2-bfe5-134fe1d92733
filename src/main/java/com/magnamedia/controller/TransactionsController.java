package com.magnamedia.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Iterables;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Searchable;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.security.ViewScope;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.TransactionCsvProjection;
import com.magnamedia.entity.projection.TransactionNightReviewProjection;
import com.magnamedia.entity.projection.TransactionProjection;
import com.magnamedia.entity.projection.TransactionsFromAndToBucket;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.extra.*;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.module.type.VatType;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.module.AccountingModule.*;

/**
 * <AUTHOR>
 */
@RequestMapping("/transactions")
@RestController
public class TransactionsController extends BaseRepositoryController<Transaction> {

    // ACC-1196
    public static final Integer csvMaxLimit = 2000;
    private int getCsvMailMaxLimit() {

        return Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_TRANSACTIONS_PAGE_CSV_ROWS_LIMIT));
    }

    // ACC-2545
    public static final String TRANSACTION_MINIMUM_DATE = "2019-05-19";

    private static final Logger logger = Logger.getLogger(TransactionsController.class.getName());
    public static final String TRANSACTION_LICENSE_NO_VAT = "no_vat";

    public static final List<String> columns = Arrays.asList(
            "name", "bucketFromName", "bucketFromCode", "revenueName",
            "revenueCode", "expenseName", "expenseCode", "bucketToName",
            "bucketToCode", "description", "pnlValueDate",
            "transactionAmount", "vatAmount", "vatType", "dateOfTransaction",
            "dateOfCreation", "transactionFor", "visaExpenseName", "license", "paymentId", "paymentType");

    @Autowired
    private TransactionRepository transactionRepository;
    @Autowired
    private BucketRepository bucketRepository;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private AccountBalanceService accountBalanceService;
    @Autowired
    private ExpenseRepository expenseRepository;
    @Autowired
    private DisableExpensesLogRepository disableExpensesLogRepository;
    @Autowired
    private EmailHelper emailHelper;
    @Autowired
    private TransactionService transactionService;
    @Autowired
    private AttachementRepository attachementRepository;

    @Override
    public BaseRepository<Transaction> getRepository() {
        return transactionRepository;
    }

    @Transactional
    public ResponseEntity<?> newCreateEntity(Transaction t) {

        t.checkIntegrity();

        if (t.getHousemaids() != null && t.getHousemaids().size() == 1 && t.getHousemaids().get(0).getAmount() == null) {
            t.getHousemaids().get(0).setAmount(t.getAmount());
        }

        if (t.getTransactionType() == TransactionEntityType.HOUSEMAID && t.getHousemaids() != null) {
            double totalHousemaidAmount = t.getHousemaids().stream()
                    .mapToDouble(ht -> {
                        return ht.getAmount() != null ? ht.getAmount() : 0.0;
                    })
                    .sum();

            if (Math.abs(totalHousemaidAmount - t.getAmount()) > EPSILON) {
                throw new RuntimeException("The total of the assigned amounts must match the main transaction amount.");
            }
        }

        // ACC-2522
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getFromBucket(), t.getDate());
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getToBucket(), t.getDate());

        // ACC-2665
        // check if the expense is enabled
        if (t.getExpense() != null) {
            Expense expense = expenseRepository.findOne(t.getExpense().getId());

            if (expense.getDisabled()) throw new RuntimeException("The selected Expense is disabled");
        }

        transactionService.setMissingTaxInvoice(t);

        ResponseEntity<?> entity = super.createEntity(t);
        t = (Transaction) entity.getBody();


        if (t.getVisaStatementTransactionId() != null){
            VisaStatementTransaction visaStatementTransaction = Setup.getRepository(VisaStatementTransactionRepository.class)
                    .findOne(t.getVisaStatementTransactionId());
            if (visaStatementTransaction != null){
                visaStatementTransaction.setFinished(true);
                visaStatementTransaction.setNote("Manual transaction added");
                visaStatementTransaction.setTransaction(t);
                visaStatementTransaction = Setup.getRepository(VisaStatementTransactionRepository.class).save(visaStatementTransaction);
                VisaStatement statement = visaStatementTransaction.getStatement();
                Setup.getApplicationContext().getBean(VisaStatementService.class)
                        .refreshStatement(statement);
            }
        }

        getRepository().flush();

        // ACC-2522
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getFromBucket(), t.getDate());
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getToBucket(), t.getDate());

        return entity;
    }

    @Override
    public ResponseEntity<?> createEntity(Transaction t) {
        return newCreateEntity(t);
    }

    @PreAuthorize("hasPermission('transactions','byBucket')")
    @RequestMapping("/byBucket/{id}")
    public ResponseEntity<?> listByBuckets(@PathVariable("id") Bucket bucket) {
        if (checkPermission("list")) {
            Calendar cal = Calendar.getInstance();
            cal.set(1900, 1, 1);
            List<Transaction> transFrom = transactionRepository.findByFromBucket(bucket);
            List<Transaction> transTo = transactionRepository.findByToBucket(bucket);

            TransactionsFromAndToBucket res = new TransactionsFromAndToBucket();
            res.setFromBucket(transFrom);
            res.setToBucket(transTo);

            return new ResponseEntity<>(res, HttpStatus.OK);
        } else {
            return unauthorizedReponse();
        }
    }

    @Transactional
    public ResponseEntity<?> newUpdateEntity(Transaction t) {
        logger.log(Level.SEVERE, "Update Transaction: " + t.getId());
        logger.log(Level.SEVERE, "Update Transaction attachments count: " + t.getAttachments().size());

        Expense expense = null;
        if (t.getExpense() != null) expense = expenseRepository.findOne(t.getExpense().getId());

        // ACC-2665
        //check if the expense is disabled
        if (expense != null) {
            List<DisableExpensesLog> expensesLogs = disableExpensesLogRepository.getDisabledExpenseByExpenseAndTransactionDate(expense, t.getCreationDate());
            if (expensesLogs != null && expensesLogs.size() > 0)
                throw new RuntimeException("The selected Expense were disable by the transaction creation date!");
        }

        HistorySelectQuery historyQuery = new HistorySelectQuery(Transaction.class);
        historyQuery.filterBy("id", "=", t.getId());
        historyQuery.filterByChanged("date");
        historyQuery.sortBy("lastModificationDate", false, true);
        List<Transaction> oldTransactions = historyQuery.execute();

        Transaction old = oldTransactions.isEmpty() ? null : oldTransactions.get(0);

        java.sql.Date earlierDate = old != null && old.getDate() != null &&
                old.getDate().before(t.getDate()) ? old.getDate() : t.getDate();

        // ACC-2522
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getFromBucket(), earlierDate);
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getToBucket(), earlierDate);

        if(old != null){
            accountBalanceService.deleteBucketAccountBalanceAfter(old.getFromBucket(), earlierDate);
            accountBalanceService.deleteBucketAccountBalanceAfter(old.getToBucket(), earlierDate);
        }

        transactionService.setMissingTaxInvoice(t);

        super.updateEntity(t);
        getRepository().flush();

        // ACC-2522
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getFromBucket(), t.getDate());
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getToBucket(), t.getDate());

        if(old != null){
            accountBalanceService.deleteBucketAccountBalanceAfter(old.getFromBucket(), earlierDate);
            accountBalanceService.deleteBucketAccountBalanceAfter(old.getToBucket(), earlierDate);
        }

        return new ResponseEntity(new Response("Updated"), HttpStatus.OK);
    }

    //this API is only used from Missing Tax Invoice Tab in reconciliator page
    @PreAuthorize("hasPermission('transactions','update2Api')")
    @RequestMapping(value = "/update2Api", method = RequestMethod.POST)
    @ResponseBody
    @JsonView(ViewScope.Normal.class)
    @Transactional
    public ResponseEntity<?> update2Api(
            @RequestBody ObjectNode objectNode) throws IOException {

        if (checkPermission("update")) {

            Transaction updated = parse(objectNode);
            Transaction origin = getRepository().findOne(updated.getId());

            update(origin, updated, objectNode);

            origin.checkAttachment();

            return updateEntity(origin);
        } else {
            return unauthorizedReponse();
        }
    }

    @Override
    protected ResponseEntity<?> updateEntity(Transaction t) {
        return newUpdateEntity(t);
    }

    @Transactional
    protected ResponseEntity<?> newDeleteEntity(Transaction t) {

        // ACC-2522
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getFromBucket(), t.getDate());
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getToBucket(), t.getDate());

        ResponseEntity response = super.deleteEntity(t);

        // ACC-2522
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getFromBucket(), t.getDate());
        accountBalanceService.deleteBucketAccountBalanceAfter(t.getToBucket(), t.getDate());

        return response;
    }

    @Override
    protected ResponseEntity<?> deleteEntity(Transaction t) {
        return newDeleteEntity(t);
    }

    private double getBalance(Double initial, Double income, Double outcome) {
        return (initial == null ? 0.0 : initial) + (income == null ? 0.0 : income) - (outcome == null ? 0.0 : outcome);
    }

    @PreAuthorize("hasPermission('transactions','fix')")
    @RequestMapping("/fix/{dd}/{mm}/{yyyy}")
    public ResponseEntity<?> fix(@PathVariable("dd") Integer day, @PathVariable("mm") Integer month, @PathVariable("yyyy") Integer year) {
        Calendar cal = Calendar.getInstance();
        cal.set(year, month - 1, day);
        Fix(cal.getTime());
        return new ResponseEntity<>(cal.getTime().toString(), HttpStatus.OK);
    }

    @Transactional
    public void Fix(Date startDate) {
        List<Transaction> trans = transactionRepository.findByCreationDateGreaterThanEqualOrderByCreationDateAscIdAsc(startDate);
        List<Transaction> toSave = new ArrayList<>();
        Map<Long, Double> bucketBalance = new HashMap<>();
        for (Transaction tran : trans) {
            Double fromPreBalance = null;
            Double toPreBalance = null;
            if (tran.getFromBucket() != null) {
                Bucket fromBucket = tran.getFromBucket();
                if (bucketBalance.containsKey(fromBucket.getId())) {
                    fromPreBalance = bucketBalance.get(fromBucket.getId());
                } else {
                    fromPreBalance = getBalance(fromBucket.getInitialBalance(),
                            bucketRepository.calculateIncomeBeforeDate(fromBucket, tran.getCreationDate(), tran.getId()),
                            bucketRepository.calculateOutcomeBeforeDate(fromBucket, tran.getCreationDate(), tran.getId()));
                }
                bucketBalance.put(fromBucket.getId(), fromPreBalance - tran.getAmount());

            }
            if (tran.getToBucket() != null) {

                Bucket toBucket = tran.getToBucket();
                if (bucketBalance.containsKey(toBucket.getId())) {
                    toPreBalance = bucketBalance.get(toBucket.getId());
                } else {
                    toPreBalance = getBalance(toBucket.getInitialBalance(),
                            bucketRepository.calculateIncomeBeforeDate(toBucket, tran.getCreationDate(), tran.getId()),
                            bucketRepository.calculateOutcomeBeforeDate(toBucket, tran.getCreationDate(), tran.getId()));
                }
                bucketBalance.put(toBucket.getId(), toPreBalance + tran.getAmount());

            }
            if (tran.getFromBucket() != null || tran.getToBucket() != null) {
                toSave.add(tran);
            }
        }
        transactionRepository.save(toSave);
        //transactionRepository.flush();
    }

    /*private List<Transaction> adjustTransactions(
            List<Transaction> affectedTransactions,
            Bucket fromBucket,
            Bucket toBucket,
            Transaction t,
            Boolean isDelete) {
        DateTime todayDate = new DateTime();
        DateTime transactionCreationDate = new DateTime(t.getCreationDate());
        DateTime beforeOneWeek = todayDate.minusDays(6);
        if (transactionCreationDate.isBefore(beforeOneWeek)) {
            throw new RuntimeException("Transaction Too Old");
        }

        for (Transaction trans : affectedTransactions) {
            Bucket transFromBucket = trans.getFromBucket();
            Bucket transToBucket = trans.getToBucket();

            Double fromBucketIncome = 0.0;
            Double fromBucketOutcome = 0.0;
            Double fromInitialBalance = 0.0;
            if (isDelete) {
                fromBucketIncome = bucketRepository.calculateIncomeBeforeDate(transFromBucket, trans.getCreationDate(), t.getId());
                fromBucketOutcome = bucketRepository.calculateOutcomeBeforeDate(transFromBucket, trans.getCreationDate(), t.getId());
            } else {
                fromBucketIncome = bucketRepository.calculateIncomeBeforeDate(transFromBucket, trans.getCreationDate());
                fromBucketOutcome = bucketRepository.calculateOutcomeBeforeDate(transFromBucket, trans.getCreationDate());
            }
            if (fromBucket != null) {
                fromInitialBalance = fromBucket.getInitialBalance();
            }

            fromBucketIncome = fromBucketIncome == null ? 0 : fromBucketIncome;
            fromBucketOutcome = fromBucketOutcome == null ? 0 : fromBucketOutcome;
            fromInitialBalance = fromInitialBalance == null ? 0 : fromInitialBalance;
            Double fromBucketPreBalance = fromBucketIncome - fromBucketOutcome + fromInitialBalance;

            Double toBucketIncome = 0.0;
            Double toBucketOutcome = 0.0;
            Double toInitialBalance = 0.0;

            if (isDelete) {
                toBucketIncome = bucketRepository.calculateIncomeBeforeDate(transToBucket, trans.getCreationDate(), t.getId());
                toBucketOutcome = bucketRepository.calculateOutcomeBeforeDate(transToBucket, trans.getCreationDate(), t.getId());
            } else {
                toBucketIncome = bucketRepository.calculateIncomeBeforeDate(transToBucket, trans.getCreationDate());
                toBucketOutcome = bucketRepository.calculateOutcomeBeforeDate(transToBucket, trans.getCreationDate());
            }

            toInitialBalance = 0.0;
            if (transToBucket != null) {
                toInitialBalance = transToBucket.getInitialBalance();
            }

            toBucketIncome = toBucketIncome == null ? 0 : toBucketIncome;
            toBucketOutcome = toBucketOutcome == null ? 0 : toBucketOutcome;
            toInitialBalance = toInitialBalance == null ? 0 : toInitialBalance;
            Double toBucketPreBalance = toBucketIncome - toBucketOutcome + toInitialBalance;

            trans.setBucketFromPreBalance(fromBucketPreBalance);
            trans.setBucketToPreBalance(toBucketPreBalance);

        }

        return affectedTransactions;

    }*/

    @PreAuthorize("hasPermission('transactions','csvsearch')")
    @RequestMapping(path = "/csv/search",
            method = RequestMethod.POST)
    public void downloadAttachment(Sort sort,
                                   @RequestParam(name = "search",
                                           required = false) String queryString,
                                   @RequestParam(name = "limit",
                                           required = false) Integer limit,
                                   @RequestBody List<String> csvColumns,
                                   HttpServletResponse response) {
//        throw new RuntimeException("Max limit exceeded");
        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");
        //Filtering
        if (queryString != null && !queryString.isEmpty()) {
            if (StringUtils.isNumeric(queryString)) {
                query.filterBy(
                        new SelectFilter("id", "=", Long.valueOf(queryString)));
            } else {
                query.filterBy(
                        new SelectFilter("fromBucket.name", "like", "%" + queryString + "%")
                                .or("fromBucket.code", "like", "%" + queryString + "%")
                                .or("toBucket.name", "like", "%" + queryString + "%")
                                .or("toBucket.code", "like", "%" + queryString + "%")
                                .or("expense.name", "like", "%" + queryString + "%")
                                .or("expense.code", "like", "%" + queryString + "%")
                                .or("revenue.name", "like", "%" + queryString + "%")
                                .or("revenue.code", "like", "%" + queryString + "%"));
            }
        }

        //Jirra ACC-2545
        query.filterBy(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        //Sorting
        if (sort != null && !Iterables.isEmpty(sort)) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", false, true);
        }
        if (limit == null || limit > csvMaxLimit) {
            limit = csvMaxLimit;
        }
        
        InputStream is = null;
        try {
            List<String> targetCsvColumns = new ArrayList<>();
            for (String column : csvColumns) {
                if (columns.contains(column))
                    targetCsvColumns.add(column);
            }
            String[] namesOrdared = targetCsvColumns.toArray(new String[0]);

            is = generateCsv(query, TransactionCsvProjection.class, namesOrdared, limit);
            createDownloadResponse(response,
                    "Transactions.csv",
                    is);
        } catch (Exception ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    //Jirra ACC-1144
    @PreAuthorize("hasPermission('transactions','csvsearch')")
    @RequestMapping(path = "/csv/search/mail",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> advanceSearchCsv(Sort sort,
                                              @RequestParam(name = "pageName", required = false) String pageName,
                                              @RequestParam(name = "search",
                                                      required = false) String queryString,
                                              @RequestParam(name = "limit",
                                                      required = false) Integer limit,
                                              @RequestBody List<String> csvColumns) {

        //Jirra ACC-1099
        int csvMailMaxLimit = getCsvMailMaxLimit();
        if (limit == null || limit > csvMailMaxLimit) {
            limit = csvMailMaxLimit;
        }

        List<String> targetCsvColumns = new ArrayList<>();
        for (String column : csvColumns) {
            if (columns.contains(column))
                targetCsvColumns.add(column);
        }

        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");

        //Jirra ACC-2545
        query.filterBy(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        if (queryString != null && !queryString.isEmpty()) {
            if (StringUtils.isNumeric(queryString)) {
                query.filterBy(
                        new SelectFilter("id", "=", Long.valueOf(queryString)));
            } else {
                query.filterBy(
                        new SelectFilter("fromBucket.name", "like", "%" + queryString + "%")
                                .or("fromBucket.code", "like", "%" + queryString + "%")
                                .or("toBucket.name", "like", "%" + queryString + "%")
                                .or("toBucket.code", "like", "%" + queryString + "%")
                                .or("expense.name", "like", "%" + queryString + "%")
                                .or("expense.code", "like", "%" + queryString + "%")
                                .or("revenue.name", "like", "%" + queryString + "%")
                                .or("revenue.code", "like", "%" + queryString + "%"));
            }
        }

        //Sorting
        if (sort != null && !Iterables.isEmpty(sort)) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", false, true);
        }

        if (limit != null) {
            query.setLimit(limit);
        }

        String[] namesOrdared = targetCsvColumns.toArray(new String[0]);

        String emails = CurrentRequest.getUser().getEmail();

        try {
            emailHelper.generateCSVFileAndSendViaMail(emails, pageName,
                    namesOrdared, namesOrdared,
                    TransactionCsvProjection.class,
                    "Manage Transactions".equals(pageName) ? "Manage Transactions" : "", query, CurrentRequest.getUser());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
        }

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('transactions','search')")
    @RequestMapping("/page/search")
    public ResponseEntity<?> searchTransactions(
            Pageable pageable,
            @RequestParam(name = "search",
                    required = false) String queryString) {

        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Jirra ACC-961
        SelectQuery<Transaction> inQuery = new SelectQuery<>(Transaction.class);
        SelectQuery<Transaction> outQuery = new SelectQuery<>(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");
        inQuery.leftJoinFetch("fromBucket");
        inQuery.leftJoinFetch("toBucket");
        inQuery.leftJoinFetch("expense");
        inQuery.leftJoinFetch("revenue");
        outQuery.leftJoinFetch("fromBucket");
        outQuery.leftJoinFetch("toBucket");
        outQuery.leftJoinFetch("expense");
        outQuery.leftJoinFetch("revenue");
        //Filtering
        SelectFilter filter = new SelectFilter();
        if (queryString != null && !queryString.isEmpty()) {
            if (StringUtils.isNumeric(queryString)) {
                filter.or("id", "=", Long.valueOf(queryString));
            }
            filter.or(
                    new SelectFilter("fromBucket.name", "like", "%" + queryString + "%")
                            .or("fromBucket.code", "like", "%" + queryString + "%")
                            .or("toBucket.name", "like", "%" + queryString + "%")
                            .or("toBucket.code", "like", "%" + queryString + "%")
                            .or("expense.name", "like", "%" + queryString + "%")
                            .or("expense.code", "like", "%" + queryString + "%")
                            .or("revenue.name", "like", "%" + queryString + "%")
                            .or("revenue.code", "like", "%" + queryString + "%"));
        }

        //Jirra ACC-2545
        filter = filter.and(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        SelectFilter inFilter = new SelectFilter(filter);
        SelectFilter outFilter = new SelectFilter(filter);
        query.filterBy(filter);
        inQuery.filterBy(inFilter);
        outQuery.filterBy(outFilter);

        inQuery.filterBy("vatType", "=", VatType.IN);
        outQuery.filterBy("vatType", "=", VatType.OUT);
        AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "amount");
        AggregateQuery inAggQuery = new AggregateQuery(inQuery, Aggregate.Sum, "vatAmount");
        AggregateQuery outAggQuery = new AggregateQuery(outQuery, Aggregate.Sum, "vatAmount");
        Double balanceSum = aggQuery.execute().doubleValue();
        Double inBalanceSum = inAggQuery.execute().doubleValue();
        Double outBalanceSum = outAggQuery.execute().doubleValue();

        //Jirra ACC-1147
        //Jirra ACC-7479
        balanceSum = (double) Math.round(balanceSum * 100) / 100;
        inBalanceSum = Math.floor(inBalanceSum * 100) / 100;
        outBalanceSum = Math.floor(outBalanceSum * 100) / 100;

        //Sorting
        if (pageable.getSort() == null || Iterables.isEmpty(pageable.getSort())) {
            query.sortBy("creationDate", false, true);
        }
        PageImpl s = (PageImpl) query.execute(pageable).map(obj
                -> projectionFactory.createProjection(
                TransactionProjection.class, obj));

        AccountingPage accountingPageResult =
                new TransactionPage(
                        s.getContent(), pageable, s.getTotalElements(),
                        balanceSum, inBalanceSum, outBalanceSum);
        return new ResponseEntity<>(accountingPageResult,
                HttpStatus.OK);
    }

    //this API is only used from Missing Tax Invoice Tab in reconciliator page
    @PreAuthorize("hasPermission('transactions','advancesearch3')")
    @RequestMapping(value = "/page/advancesearch3",
            method = RequestMethod.POST)
    public ResponseEntity<?> advanceSearchForMissingTaxInvoicesTab(Pageable pageable, @RequestBody List<FilterItem> filters) {

        //this is to search only on required conditions in Missing Tax Invoice Tab in reconciliator page
        FilterItem filterItem = new FilterItem();
        filterItem.setOperation("=");
        filterItem.setProperty("missingTaxInvoice");
        filterItem.setValue(true);
        filters.add(filterItem);

        filterItem = new FilterItem();
        filterItem.setOperation("IN");
        filterItem.setProperty("license.id");
        filterItem.setValue(Arrays.asList(PicklistHelper.getItem(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM).getId(),
                PicklistHelper.getItem(PICKLIST_TRANSACTION_LICENSE, PICKLIST_TRANSACTION_LICENSE_STORAGE_ITEM).getId()));
        filters.add(filterItem);

        filterItem = new FilterItem();
        filterItem.setOperation("IS NULL");
        filterItem.setProperty("cancelRequestExpense");
        filterItem.setValue(null);
        filters.add(filterItem);

        filterItem = new FilterItem();
        filterItem.setOperation("IS NULL");
        filterItem.setProperty("newRequestExpense");
        filterItem.setValue(null);
        filters.add(filterItem);

        filterItem = new FilterItem();
        filterItem.setOperation("IS NULL");
        filterItem.setProperty("renewRequestExpense");
        filterItem.setValue(null);
        filters.add(filterItem);

        filterItem = new FilterItem();
        filterItem.setOperation("IS NULL");
        filterItem.setProperty("repeatEIDRequestExpense");
        filterItem.setValue(null);
        filters.add(filterItem);

        filterItem = new FilterItem();
        filterItem.setOperation(">=");
        filterItem.setProperty("creationDate");
        filterItem.setValue(java.sql.Date.valueOf(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_MISSING_TAX_INVOICE_TAB_DEPLOYMENT_DATE).trim()));
        filters.add(filterItem);

        Map queriesResult = advanceSearch(pageable, null, true, filters, true);

        Page page = (PageImpl) queriesResult.get("page");
        Double transactionsSum = (Double) queriesResult.get("transactionsSum");
        Double inBalanceSum = (Double) queriesResult.get("inBalanceSum");
        Double outBalanceSum = (Double) queriesResult.get("outBalanceSum");

        PageImpl s = (PageImpl) page.map(obj
                -> projectionFactory.createProjection(
                TransactionProjection.class, obj));

        AccountingPage accountingPageResult =
                new TransactionPage(s.getContent(), pageable, s.getTotalElements(),
                        transactionsSum, inBalanceSum, outBalanceSum);

        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('transactions','advancesearch')")
    @RequestMapping(value = "/page/advancesearch",
            method = RequestMethod.POST)
    public ResponseEntity<?> advanceSearch(Pageable pageable, @RequestBody List<FilterItem> filters) {
        Map queriesResult = advanceSearch(pageable, null, true, filters, true);

        Page page = (PageImpl) queriesResult.get("page");
        Double transactionsSum = (Double) queriesResult.get("transactionsSum");
        Double inBalanceSum = (Double) queriesResult.get("inBalanceSum");
        Double outBalanceSum = (Double) queriesResult.get("outBalanceSum");

        PageImpl s = (PageImpl) page.map(obj
                -> projectionFactory.createProjection(
                TransactionProjection.class, obj));

        AccountingPage accountingPageResult =
                new TransactionPage(s.getContent(), pageable, s.getTotalElements(),
                        transactionsSum, inBalanceSum, outBalanceSum);

        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
    }

    public Map advanceSearch(Pageable pageable, Bucket bucket, boolean withVat, List<FilterItem> filters, boolean includeMinDate) {
        return advanceSearch(pageable, pageable != null ? pageable.getSort() : null, bucket, withVat, filters, includeMinDate);
    }

    public Map advanceSearch(Pageable pageable, Sort sort, Bucket bucket, boolean withVat, List<FilterItem> filters, boolean includeMinDate) {
        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");

        // ACC-961
        SelectQuery<Transaction> inQuery = new SelectQuery<>(Transaction.class);
        SelectQuery<Transaction> outQuery = new SelectQuery<>(Transaction.class);

        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(Transaction.class));
        }

        // ACC-2545
        if (includeMinDate)
            selectFilter = selectFilter.and(new SelectFilter(
                    "date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        query.filterBy(selectFilter);

        SelectFilter inFilter = new SelectFilter(selectFilter);
        inQuery.filterBy(inFilter);
        inQuery.filterBy("vatType", "=", VatType.IN);

        SelectFilter outFilter = new SelectFilter(selectFilter);
        outQuery.filterBy(outFilter);
        outQuery.filterBy("vatType", "=", VatType.OUT);

        AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "amount");
        AggregateQuery inAggQuery = new AggregateQuery(inQuery, Aggregate.Sum, "vatAmount");
        AggregateQuery outAggQuery = new AggregateQuery(outQuery, Aggregate.Sum, "vatAmount");

        Double transactionsSum = aggQuery.execute().doubleValue();
        Double inBalanceSum = withVat ? inAggQuery.execute().doubleValue() : 0.0;
        Double outBalanceSum = withVat ? outAggQuery.execute().doubleValue() : 0.0;

        // ACC-1147
        //Jirra ACC-7479
        transactionsSum = (double) Math.round(transactionsSum * 100) / 100;
        inBalanceSum = Math.floor(inBalanceSum * 100) / 100;
        outBalanceSum = Math.floor(outBalanceSum * 100) / 100;

        //Sorting
        if (sort == null || Iterables.isEmpty(sort)) {
            query.sortBy("creationDate", false, true);
        } else {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        }


        Map queriesResult = new HashMap();
        if (pageable == null) {
            List<Transaction> transactions = query.execute();
            queriesResult.put("transactions", transactions);

            if(bucket != null) {
                Double balanceSum = transactions.stream()
                        .map(t -> {
                            if(t.getFromBucket() != null && t.getFromBucket().getId()
                                    .equals(bucket.getId())) {

                                return -t.getAmount();
                            } else if (t.getToBucket() != null && t.getToBucket().getId()
                                    .equals(bucket.getId())) {

                                return t.getAmount();
                            } else {
                                return 0.0;
                            }
                        }).collect(Collectors.summingDouble(Double::doubleValue));
                queriesResult.put("balanceSum", balanceSum);
            }
        } else {
            Page<Transaction> page = query.execute(pageable);
            queriesResult.put("page", page);
        }

        queriesResult.put("transactionsSum", transactionsSum);
        queriesResult.put("inBalanceSum", inBalanceSum);
        queriesResult.put("outBalanceSum", outBalanceSum);

        return queriesResult;
    }

    @PreAuthorize("hasPermission('transactions','csvadvancesearch')")
    @RequestMapping(value = "/csv/advancesearch",
            method = RequestMethod.POST)
    public void downloadAttachmentAdvanceSearch(
            Sort sort,
            @RequestBody advanceSearchEntity advanceSearchEntity,
            @RequestParam(name = "limit", required = false) Integer limit,
            HttpServletResponse response) {

        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");
        //Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : advanceSearchEntity.filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(Transaction.class, false));
        }

        //Jirra ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        query.filterBy(selectFilter);
        //Sorting
        if (sort != null && !Iterables.isEmpty(sort)) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", false, true);
        }

        if (limit == null || limit > csvMaxLimit) {
            limit = csvMaxLimit;
        }
        
        InputStream is = null;
        try {
            List<String> targetCsvColumns = new ArrayList<>();
            for (String column : advanceSearchEntity.csvColumns) {
                if (columns.contains(column))
                    targetCsvColumns.add(column);
            }
            String[] namesOrdared = targetCsvColumns.toArray(new String[0]);
            is = generateCsv(query, TransactionCsvProjection.class, namesOrdared, limit);
            createDownloadResponse(response,
                    "Transactions.csv",
                    is);
        } catch (Exception ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    //Jirra ACC-1144
    @PreAuthorize("hasPermission('transactions','csvadvancesearch')")
    @RequestMapping(path = "/csv/advancesearch/mail",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> advanceSearchCsv2(
            Sort sort,
            @RequestBody advanceSearchEntity advanceSearchEntity,
            @RequestParam(name = "pageName", required = false) String pageName,
            @RequestParam(name = "limit", required = false) Integer limit) {

        //Jirra ACC-1099
        int csvMailMaxLimit = getCsvMailMaxLimit();
        if (limit == null || limit > csvMailMaxLimit) {
            limit = csvMailMaxLimit;
        }

        SelectQuery<Transaction> query = new SelectQuery(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");


        //Process Filters
        SelectFilter selectFilter = new SelectFilter();

        if (advanceSearchEntity != null && advanceSearchEntity.filters != null) {
            for (FilterItem filter : advanceSearchEntity.filters) {
                selectFilter = selectFilter.and(filter.getSelectFilter(Transaction.class));
            }

        }

        //Jirra ACC-2545
        selectFilter = selectFilter.and(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        query.filterBy(selectFilter);

        //Sorting
        if (sort != null && !Iterables.isEmpty(sort)) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", false, true);
        }

        if (limit != null) {
            query.setLimit(limit);
        }

        List<String> targetCsvColumns = new ArrayList<>();
        for (String column : advanceSearchEntity.csvColumns) {
            if (columns.contains(column))
                targetCsvColumns.add(column);
        }

        String[] namesOrdared = targetCsvColumns.toArray(new String[0]);

        String emails = CurrentRequest.getUser().getEmail();
        try {
            emailHelper.generateCSVFileAndSendViaMail(emails, pageName,
                    namesOrdared, namesOrdared,
                    TransactionCsvProjection.class,
                    "Unknown Wire Transfer".equals(pageName) ? "Unknown Wire Transfer" : "", query, CurrentRequest.getUser());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
        }

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    //Jirra ACC-558
    @PreAuthorize("hasPermission('transactions','getallvattypes')")
    @RequestMapping(value = "/getallvattypes")
    public ResponseEntity<?> getAllVatTypes(
            @RequestParam(name = "search", required = false) String search) {
        List<SearchableEnumProjection> result =
                Arrays.asList(VatType.class.getEnumConstants())
                        .stream()
                        .map(x -> new SearchableEnumProjection(x.toString()))
                        .filter(x -> search == null
                                || search.isEmpty()
                                || x.getLabel().contains(search))
                        .collect(Collectors.toList());
        return new ResponseEntity<>(
                result,
                HttpStatus.OK);
    }

    //Jirra ACC-565 ACC-751
    @PreAuthorize("hasPermission('transactions','advancesearch')")
    @RequestMapping(value = "/page/advancesearch2",
            method = RequestMethod.GET)
    @Searchable(fieldName = "fromBucket.name",
            label = "Bucket From Name",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "toBucket.name",
            label = "Bucket To Name",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "revenue.name",
            label = "Revenue Name",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "expense.name",
            label = "Expense Name",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "description",
            label = "Description",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "date",
            label = "Date of Transaction",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "pnlValueDate",
            label = "PnL Date",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "creationDate",
            label = "Date of Creation",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "amount",
            label = "Amount",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "fromBucket",
            label = "Bucket From",
            valuesApi = "/accounting/buckets/page/searchBuckets",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "toBucket",
            label = "Bucket To",
            valuesApi = "/accounting/buckets/page/searchBuckets",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "expense",
            label = "Expense",
            valuesApi = "/accounting/expenses/page/searchExpenses2",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "revenue",
            label = "Revenue",
            valuesApi = "/accounting/revenues/page/searchRevenues",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "chequesNotClearedAmount",
            label = "Cheques Not Cleared Amount",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "vatAmount",
            label = "Vat Amount",
            entity = Transaction.class,
            apiKey = "transaction_management")
    @Searchable(fieldName = "vatType",
            label = "Vat Type",
            valuesApi = "/accounting/transactions/getallvattypes",
            entity = Transaction.class,
            apiKey = "transaction_management")
    //Jirra ACC-960
    @Searchable(fieldName = "license",
            label = "License",
            valuesApi = "/public/picklist/items/transaction_license",
            entity = Transaction.class,
            apiKey = "transaction_management")
    // acc-2466
    @Searchable(fieldName = "missingTaxInvoice",
            label = "Missing Tax Invoice",
            entity = Transaction.class,
            apiKey = "transaction_management")
    public ResponseEntity<?> advanceSearch2(
            Pageable pageable) {

        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Jirra ACC-961
        SelectQuery<Transaction> inQuery = new SelectQuery<>(Transaction.class);
        SelectQuery<Transaction> outQuery = new SelectQuery<>(Transaction.class);

        SelectFilter selectFilter = CurrentRequest.getSearchFilter();

        if (selectFilter != null) {
            SelectFilter inFilter = new SelectFilter(selectFilter);
            SelectFilter outFilter = new SelectFilter(selectFilter);
            //amount Sum
            query.filterBy(selectFilter);
            inQuery.filterBy(inFilter);
            outQuery.filterBy(outFilter);
        }

        //ACC-2545
        SelectFilter dateFilter = new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE));
        query.filterBy(dateFilter);
        inQuery.filterBy(new SelectFilter(dateFilter));
        outQuery.filterBy(new SelectFilter(dateFilter));

        inQuery.filterBy("vatType", "=", VatType.IN);
        outQuery.filterBy("vatType", "=", VatType.OUT);
        AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "amount");
        AggregateQuery inAggQuery = new AggregateQuery(inQuery, Aggregate.Sum, "vatAmount");
        AggregateQuery outAggQuery = new AggregateQuery(outQuery, Aggregate.Sum, "vatAmount");
        Double balanceSum = aggQuery.execute().doubleValue();
        Double inBalanceSum = inAggQuery.execute().doubleValue();
        Double outBalanceSum = outAggQuery.execute().doubleValue();

        //Jirra ACC-1147
        //Jirra ACC-7479
        balanceSum = (double) Math.round(balanceSum * 100) / 100;
        inBalanceSum = Math.floor(inBalanceSum * 100) / 100;
        outBalanceSum = Math.floor(outBalanceSum * 100) / 100;
        //Sorting
        query.sortBy("creationDate", false, true);

        PageImpl s = (PageImpl) query.execute(pageable).map(obj
                -> projectionFactory.createProjection(
                TransactionProjection.class, obj));

        AccountingPage accountingPageResult =
                new TransactionPage(s.getContent(), pageable, s.getTotalElements(),
                        balanceSum, inBalanceSum, outBalanceSum);

        return new ResponseEntity<>(accountingPageResult, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('transactions','csvadvancesearch')")
    @RequestMapping(value = "/csv/advancesearch2",
            method = RequestMethod.POST)
    public void downloadAttachmentAdvanceSearch2(
            Sort sort,
            @RequestParam(name = "limit", required = false) Integer limit,
            @RequestBody List<String> csvColumns,
            HttpServletResponse response) {

        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");
        //Process Filters
        query.filterBy(CurrentRequest.getSearchFilter());

        //Jirra ACC-2545
        query.filterBy(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        //Sorting
        if (sort != null && !Iterables.isEmpty(sort)) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", false, true);
        }

        if (limit == null || limit > csvMaxLimit) {
            limit = csvMaxLimit;
        }
        
        InputStream is = null;
        try {
            List<String> targetCsvColumns = new ArrayList<>();
            for (String column : csvColumns) {
                if (columns.contains(column))
                    targetCsvColumns.add(column);
            }
            String[] namesOrdared = targetCsvColumns.toArray(new String[0]);
            is = generateCsv(query, TransactionCsvProjection.class, namesOrdared, limit);
            createDownloadResponse(response,
                    "Transactions.csv",
                    is);
        } catch (Exception ex) {
            logger.log(Level.SEVERE,
                    ex.getMessage(),
                    ex);
            throw new RuntimeException(ex.getMessage());
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    //Jirra ACC-1144
    @PreAuthorize("hasPermission('transactions','csvadvancesearch')")
    @RequestMapping(path = "/csv/advancesearch2/mail",
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> advanceSearchCsv22(
            Sort sort,
            @RequestParam(name = "pageName", required = false) String pageName,
            @RequestParam(name = "limit", required = false) Integer limit,
            @RequestBody List<String> csvColumns) {

        //Jirra ACC-1099
        int csvMailMaxLimit = getCsvMailMaxLimit();
        if (limit == null || limit > csvMailMaxLimit) {
            limit = csvMailMaxLimit;
        }

        SelectQuery<Transaction> query = new SelectQuery<>(Transaction.class);
        //Joins
        query.leftJoinFetch("fromBucket");
        query.leftJoinFetch("toBucket");
        query.leftJoinFetch("expense");
        query.leftJoinFetch("revenue");

        //Jirra ACC-2545
        query.filterBy(new SelectFilter("date", ">=", java.sql.Date.valueOf(TRANSACTION_MINIMUM_DATE)));

        query.filterBy(CurrentRequest.getSearchFilter());

        //Sorting
        if (sort != null && !Iterables.isEmpty(sort)) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("creationDate", false, true);
        }

        if (limit != null) {
            query.setLimit(limit);
        }

        List<String> targetCsvColumns = new ArrayList<>();
        for (String column : csvColumns) {
            if (columns.contains(column))
                targetCsvColumns.add(column);
        }

        String[] namesOrdared = targetCsvColumns.toArray(new String[0]);

        String emails = CurrentRequest.getUser().getEmail();
        try {
            emailHelper.generateCSVFileAndSendViaMail(emails, pageName,
                    namesOrdared, namesOrdared,
                    TransactionCsvProjection.class,
                    "Manage Transactions".equals(pageName) ? "Manage Transactions" : "", query, CurrentRequest.getUser());
        } catch (Exception e) {
            logger.log(Level.SEVERE, ExceptionUtils.getStackTrace(e));
        }

        return new ResponseEntity("The file is too big and needs time to generate. We will send it to your email once done.!", HttpStatus.OK);
    }

    // Jirra ACC-3315
    @PreAuthorize("hasPermission('transactions','get-manual-transaction-by-date/questioned-expenses')")
    @RequestMapping(path = "/get-manual-transaction-by-date/questioned-expenses", method = RequestMethod.GET)
    public ResponseEntity getQuestionedCreatedManualTransactionsByDateAPI(Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(new Date(), Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(14).toDate();

        Page<TransactionNightReviewProjection> result = transactionRepository.getQuestionedCreatedManualTransactionsByDate(fromDate, toDate, pageable)
                .map(transactionRev -> {
                    Transaction transaction = getRepository().getOne(transactionRev.getId());
                    transaction.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);
                    return project(transaction, TransactionNightReviewProjection.class);
                });

        return ResponseEntity.ok(result);
    }

    // Jirra ACC-3315
    @PreAuthorize("hasPermission('transactions','get-manual-transaction-by-date')")
    @RequestMapping(path = "/get-manual-transaction-by-date", method = RequestMethod.GET)
    public ResponseEntity getCreatedManualTransactionsByDateAPI(@RequestParam(value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                                @RequestParam boolean archive,
                                                                Pageable pageable) {
        Page result = getCreatedManualTransactionsByDate(date, archive, pageable);

        return ResponseEntity.ok(result);
    }

    // Jirra ACC-3315
    public Page<TransactionNightReviewProjection> getCreatedManualTransactionsByDate(Date date, boolean archive, Pageable pageable) {
        Date toDate = DateUtil.getDateAtHour(date, Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR)));
        Date fromDate = new DateTime(toDate).minusDays(1).toDate();
        
        if (archive)
            return transactionRepository.getCreatedManualTransactionsByDateWithAchive(fromDate, toDate, pageable)
                    .map(transactionRev -> {
                        Transaction transaction = getRepository().getOne(transactionRev.longValue());
                        transaction.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);
                        return project(transaction, TransactionNightReviewProjection.class);
                    });
        else
            return transactionRepository.getCreatedManualTransactionsByDateWithNoAchive(fromDate, toDate, pageable)
                    .map(transactionRev -> {
                        Transaction transaction = getRepository().getOne(transactionRev.longValue());
                        transaction.setCooQuestionedPage(CooQuestion.QuestionedPage.NIGHT_REVIEW);
                        return project(transaction, TransactionNightReviewProjection.class);
                    });
    }

    @PreAuthorize("hasPermission('transactions','mark-as-done-by-coo')")
    @PostMapping(value = "/mark-as-done-by-coo")
    public ResponseEntity markAsDoneByCooAPI(@RequestBody Long id) {
        markAsDoneByCoo(id);
        return okResponse();
    }

    @PreAuthorize("hasPermission('transactions','mark-all-as-done-by-coo')")
    @PostMapping("/mark-all-as-done-by-coo")
    public ResponseEntity markAllAsDoneByCooAPI(
            @RequestParam(value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam boolean archive) {

        List<Long> ids = getCreatedManualTransactionsByDate(
                date, archive, PageRequest.of(0, Integer.MAX_VALUE))
                .map(element -> element.getId()).getContent();

        markAsDoneByCoo(ids);
        return okResponse();
    }

    @PreAuthorize("hasPermission('transactions','mark-all-as-done-by-coo')")
    @PostMapping("/mark-list-as-done-by-coo")
    public ResponseEntity markListAsDoneByCooAPI(@RequestBody List<Long> ids) {
        markAsDoneByCoo(ids);
        return okResponse();
    }

    private void markAsDoneByCoo(List<Long> ids) {
        if (ids == null) return;

        for (Long id : ids) {
            markAsDoneByCoo(id);
        }
    }

    @Transactional
    public void markAsDoneByCoo(Long id) {
        if (id == null) return;

        Transaction transaction = getRepository().findOne(id);
        transaction.setDoneByCoo(true);

        getRepository().save(transaction);
    }

    // ACC-2492
    @PreAuthorize("hasPermission('transactions','data-correction')")
    @GetMapping("/data-correction/acc-2492")
    public ResponseEntity dataCorrection_copy_Tax_Invoice_ToTransactions(
            @RequestParam(value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate) {

        int index = 0;
        Page<Transaction> transactionPage;
        do {
            transactionPage = transactionRepository.getDataForACC2492(fromDate, PageRequest.of(index, 100));

            for (Transaction transaction : transactionPage.getContent()) {
                Attachment paymentAttachment = null;
                for (Attachment attachment : transaction.getPayment().getAttachments()) {
                    if (attachment.getTag().startsWith(Payment.FILE_TAG_PAYMENTS_TAX_INVOICE))
                        paymentAttachment = attachment;
                }

                if (paymentAttachment == null) continue;
                Attachment newAttachment = Storage.storeTemporary(paymentAttachment.getName(), Storage.getStream(paymentAttachment),
                        "tr_" + paymentAttachment.getTag(), Boolean.FALSE);
                transaction.addAttachment(newAttachment);

                getRepository().save(transaction);
            }
        } while (transactionPage.hasNext());

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    // ACC-2492
    @PreAuthorize("hasPermission('transactions','data-correction')")
    @GetMapping(path = "/data-correction/acc-2492_2")
    public ResponseEntity dataCorrection_copy_Tax_Invoice_ToTransactions_2(
            @RequestParam(value = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate) {

        int index = 0;
        Page<Transaction> transactionPage;
        do {
            transactionPage = transactionRepository.getDataForACC2492_2(fromDate, PageRequest.of(index, 100));

            for (Transaction transaction : transactionPage.getContent()) {
                Attachment paymentAttachment = null;
                for (Attachment attachment : transaction.getPayment().getAttachments()) {
                    if (attachment.getTag().startsWith(Payment.FILE_TAG_PAYMENTS_TAX_CREDIT_NOTE))
                        paymentAttachment = attachment;
                }

                if (paymentAttachment == null) continue;
                Attachment newAttachment = Storage.storeTemporary(paymentAttachment.getName(), Storage.getStream(paymentAttachment),
                        "tr_" + paymentAttachment.getTag(), Boolean.FALSE);
                transaction.addAttachment(newAttachment);

                getRepository().save(transaction);
            }
        } while (transactionPage.hasNext());

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    //ACC-3945
    @PreAuthorize("hasPermission('transactions','data-correction')")
    @GetMapping(path = "/getDDBankInfoAttachments/{id}")
    public ResponseEntity getDDBankInfoAttachments(
            @PathVariable(value = "id") Transaction transaction) {

        List<Attachment> attachments = new ArrayList<>();
        DirectDebit dd = transaction.getPayment() != null ?
                transaction.getPayment().getDirectDebit() : null;

        if(dd != null){
            Attachment att = dd.getAttachment("bank_info_eid");
            if (att != null)
                attachments.add(att);

            att = dd.getAttachment("bank_info_account_name");
            if (att != null)
                attachments.add(att);

            att = dd.getAttachment("bank_info_iban");
            if (att != null)
                attachments.add(att);
        }

        return ResponseEntity.ok(attachments);
    }

    @PreAuthorize("hasPermission('transactions','searchNew')")
    @RequestMapping("/page/searchNew")
    public ResponseEntity<?> searchTransactionsAcc7274(
            Pageable pageable,
            @RequestParam(name = "search",
                    required = false) String queryString) {
        return new ResponseEntity<>(Setup.getApplicationContext().getBean(QueryService.class)
                .manageTransactionsMainSearch(queryString, pageable),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('transactions','advancesearchNew')")
    @PostMapping(value = "/page/advancesearchNew")
    public ResponseEntity<?> advanceSearchAcc7274(Pageable pageable, @RequestBody List<FilterItem> filters) {
        return new ResponseEntity<>(Setup.getApplicationContext().getBean(QueryService.class)
                .manageTransactionsAdvanceSearch(filters, pageable),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('transactions','advancesearch2New')")
    @GetMapping(value = "/page/advancesearch2New")
    @Searchable(fieldName = "fromBucket.name",
            label = "Bucket From Name",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "toBucket.name",
            label = "Bucket To Name",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "revenue.name",
            label = "Revenue Name",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "expense.name",
            label = "Expense Name",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "description",
            label = "Description",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "date",
            label = "Date of Transaction",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "pnlValueDate",
            label = "PnL Date",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "creationDate",
            label = "Date of Creation",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "amount",
            label = "Amount",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "fromBucket",
            label = "Bucket From",
            valuesApi = "/accounting/buckets/page/searchBuckets",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "toBucket",
            label = "Bucket To",
            valuesApi = "/accounting/buckets/page/searchBuckets",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "expense",
            label = "Expense",
            valuesApi = "/accounting/expenses/page/searchExpenses",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "revenue",
            label = "Revenue",
            valuesApi = "/accounting/revenues/page/searchRevenues",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "chequesNotClearedAmount",
            label = "Cheques Not Cleared Amount",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "vatAmount",
            label = "Vat Amount",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    @Searchable(fieldName = "vatType",
            label = "Vat Type",
            valuesApi = "/accounting/transactions/getallvattypes",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    //Jirra ACC-960
    @Searchable(fieldName = "license",
            label = "License",
            valuesApi = "/public/picklist/items/transaction_license",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    // acc-2466
    @Searchable(fieldName = "missingTaxInvoice",
            label = "Missing Tax Invoice",
            entity = Transaction.class,
            apiKey = "transaction_management_New")
    // MC-117
    @Searchable(fieldName = "paymentType",
            label = "Payment Type",
            entity = Transaction.class,
            valuesApi = "/accounting/payments/getallpaymentmethods",
            apiKey = "transaction_management_New")
    public ResponseEntity<?> advanceSearch2Acc7274(
            Pageable pageable) {
        return new ResponseEntity<>(Setup.getApplicationContext().getBean(QueryService.class)
                .manageTransactionsAdvanceSearch2(pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('transactions','addTransactionForPayment')")
    @PostMapping(value = "/addTransactionForPayment")
    public ResponseEntity<?> addTransactionForPayment(@RequestBody List<Long> ids) {
        PaymentService paymentService = Setup.getApplicationContext()
                .getBean(PaymentService.class);
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);

        for (Long id : ids) {
            try {
                paymentService.createTransactionForPayment(paymentRepository.findOne(id));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return ResponseEntity.ok("done");
    }

    @PreAuthorize("hasPermission('transactions','addAttachmentToTransactionsFromPayment')")
    @PostMapping(value = "/addAttachmentToTransactionsFromPayment")
    public ResponseEntity<?> addAttachmentToTransactionsFromPayments(@RequestBody List<Long> paymentsIds) {
        paymentsIds.forEach(paymentId -> {
            try {
                List<Attachment> attachments = attachementRepository.findByOwnerIdAndOwnerType(paymentId, "Payment");
                attachments.forEach(attachment -> transactionService.addAttachmentFromPayment(attachment));
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return ResponseEntity.ok("Done");
    }
}