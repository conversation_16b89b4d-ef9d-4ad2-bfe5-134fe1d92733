package com.magnamedia.controller;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.projection.IdLabel;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.NonClientPDC;
import com.magnamedia.entity.Supplier;
import com.magnamedia.entity.TenancyContract;
import com.magnamedia.entity.dto.TenancyContractSearchDto;
import com.magnamedia.entity.projection.TenancyContractProjection;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.repository.NonClientPDCRepository;
import com.magnamedia.repository.TenancyContractRepository;
import com.magnamedia.scheduledjobs.TenancyContractsEmailScheduledJob;

import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;

import org.apache.commons.lang3.BooleanUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.servlet.http.HttpServletResponse;

/**
 *
 * <AUTHOR> Esrawi <<EMAIL>>
 * Created at May 9, 2018
 */
@RequestMapping("/tenancyContracts")
@RestController
public class TenancyContractController extends BaseRepositoryController<TenancyContract> {

    private TenancyContractsEmailScheduledJob job;
    
    @Autowired
    TenancyContractRepository tenancyContractRepository;

    @Autowired
    private NonClientPDCRepository nonClientPDCRepository;

    @Override
    public BaseRepository<TenancyContract> getRepository() {
        return tenancyContractRepository;
    }

    @PreAuthorize("hasPermission('tenancyContracts','addTenancyForOldPDCs')")
    @RequestMapping(value = "/addTenancyForOldPDCs", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> addTenancyForOldPDCs() {
        SelectQuery<NonClientPDC> query = new SelectQuery(NonClientPDC.class);
        query.filterBy("tenancyContract", "is null", null);

        List<NonClientPDC> pdcs = query.execute();
        for (NonClientPDC pdc : pdcs) {
            TenancyContract contract = new TenancyContract();
            contract.setName(pdc.getName());
            contract.setEndDate(pdc.getContractEndDate());

            contract = tenancyContractRepository.save(contract);
            pdc.setTenancyContract(contract);
            nonClientPDCRepository.save(pdc);

        }

        return new ResponseEntity<>(project(pdcs, IdLabel.class), HttpStatus.OK);
    }
    Date after;
    Date before;

    @PreAuthorize("hasPermission('tenancyContracts','testRepo')")
    @RequestMapping(value = "/testRepo", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> testRepo() {
        DateTime after = this.after == null ? DateTime.now().withMillisOfDay(0) : new DateTime(this.after);
        DateTime before = this.before == null ? after.plusDays(30) : new DateTime(this.before);
        this.after = after.toDate();
            this.before = before.toDate();

        return new ResponseEntity<>(tenancyContractRepository.findByEndDateBetween(this.after, this.before), HttpStatus.OK);
    }
    
    @PreAuthorize("hasPermission('tenancyContracts','sendMail')")
    @RequestMapping(value = "/sendMail", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> sendMail() {
       try{
           job = new TenancyContractsEmailScheduledJob();
           job.SendEmail();
           return okResponse();
       }catch(Exception e){
           return new ResponseEntity<>(e.getMessage(),HttpStatus.OK);
       }  
    }
        
    @PreAuthorize("hasPermission('tenancyContracts','search')")
    @RequestMapping(value = "/search/page", method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<?> searchStatus(
            @RequestParam(value = "active", required = false) Boolean active,
            Pageable pageable) {

        SelectQuery<TenancyContract> query = new SelectQuery<>(TenancyContract.class);
        if (active != null)
            query.filterBy("active", "=", active);
        return new ResponseEntity<>(query.execute(pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('tenancyContracts','advancedSearch')")
    @RequestMapping(value = "/advancedSearch/page", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<?> advancedSearch(@RequestBody TenancyContractSearchDto search, Pageable pageable) {

        SelectQuery<TenancyContract> query = new SelectQuery<>(TenancyContract.class);
        if (search.getActive() != null)
            query.filterBy("active", "=", search.getActive());

        if(search.getSecondPartyName() != null) {
            if (search.getSecondPartyNameOperation() == null)
                search.setSecondPartyNameOperation("=");

            if (search.getSecondPartyNameOperation().equalsIgnoreCase("like"))
                query.filterBy("ownerName", search.getSecondPartyNameOperation(), "%" + search.getSecondPartyName() + "%");
            else
                query.filterBy("ownerName", search.getSecondPartyNameOperation(), search.getSecondPartyName());
        }

        if(search.getFirstPartyName() != null) {
            if (search.getFirstPartyNameOperation() == null)
                search.setFirstPartyNameOperation("=");

            if (search.getFirstPartyNameOperation().equalsIgnoreCase("like"))
                query.filterBy("tenantName", search.getFirstPartyNameOperation(), "%" + search.getFirstPartyName() + "%");
            else
                query.filterBy("tenantName", search.getFirstPartyNameOperation(), search.getFirstPartyName());
        }

        if (search.getStartDate1() != null && search.getStartDateOperator() != null && search.getStartDate2() == null) {
            query.filterBy("startDate", search.getStartDateOperator(), search.getStartDate1());
        } else if (search.getStartDate1() != null && search.getStartDate2() != null) {
            query.filterBy("startDate", ">=", search.getStartDate1());
            query.filterBy("startDate", "<=", search.getStartDate2());
        }

        if (search.getExpiryDate1() != null && search.getExpiryDateOperator() != null && search.getExpiryDate2() == null) {
            query.filterBy("endDate", search.getExpiryDateOperator(), search.getExpiryDate1());
        } else if (search.getExpiryDate1() != null && search.getExpiryDate2() != null) {
            query.filterBy("endDate", ">=", search.getExpiryDate1());
            query.filterBy("endDate", "<=", search.getExpiryDate2());
        }

        if (search.getTypeOfDocument() != null) {
            if (search.getTypeOfDocumentOperation() == null)
                search.setTypeOfDocumentOperation("=");
            query.filterBy("typeOfDocument", search.getTypeOfDocumentOperation(), search.getTypeOfDocument());
        }
        return new ResponseEntity<>(query.execute(pageable), HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('tenancyContracts','exportToCsv')")
    @RequestMapping(value = "/exportToCsv", method = RequestMethod.POST)
    @ResponseBody
    public void exportToCsv(@RequestBody TenancyContractSearchDto search,
                            HttpServletResponse response) {

        SelectQuery<TenancyContract> query = new SelectQuery<>(TenancyContract.class);
        if (search.getActive() != null)
            query.filterBy("active", "=", search.getActive());

        if(search.getSecondPartyName() != null) {
            if (search.getSecondPartyNameOperation() == null)
                search.setSecondPartyNameOperation("=");

            if (search.getSecondPartyNameOperation().equalsIgnoreCase("like"))
                query.filterBy("ownerName", search.getSecondPartyNameOperation(), "%" + search.getSecondPartyName() + "%");
            else
                query.filterBy("ownerName", search.getSecondPartyNameOperation(), search.getSecondPartyName());
        }

        if(search.getFirstPartyName() != null) {
            if (search.getFirstPartyNameOperation() == null)
                search.setFirstPartyNameOperation("=");

            if (search.getFirstPartyNameOperation().equalsIgnoreCase("like"))
                query.filterBy("tenantName", search.getFirstPartyNameOperation(), "%" + search.getFirstPartyName() + "%");
            else
                query.filterBy("tenantName", search.getFirstPartyNameOperation(), search.getFirstPartyName());
        }

        if (search.getStartDate1() != null && search.getStartDateOperator() != null && search.getStartDate2() == null) {
            query.filterBy("startDate", search.getStartDateOperator(), search.getStartDate1());
        } else if (search.getStartDate1() != null && search.getStartDate2() != null) {
            query.filterBy("startDate", ">=", search.getStartDate1());
            query.filterBy("startDate", "<=", search.getStartDate2());
        }

        if (search.getExpiryDate1() != null && search.getExpiryDateOperator() != null && search.getExpiryDate2() == null) {
            query.filterBy("endDate", search.getExpiryDateOperator(), search.getExpiryDate1());
        } else if (search.getExpiryDate1() != null && search.getExpiryDate2() != null) {
            query.filterBy("endDate", ">=", search.getExpiryDate1());
            query.filterBy("endDate", "<=", search.getExpiryDate2());
        }

        if (search.getTypeOfDocument() != null) {
            if (search.getTypeOfDocumentOperation() == null)
                search.setTypeOfDocumentOperation("=");
            query.filterBy("typeOfDocument", search.getTypeOfDocumentOperation(), search.getTypeOfDocument());
        }
        List<TenancyContract> tenancyContracts = query.execute();

        InputStream inputStream = null;
        File excelFile;
        String fileName = "Tenancy Contracts.csv";
        try {
            String[] namesOrdered = {"name", "startDate", "endDate", "description", "ownerName", "tenantName", "active", "typeOfDocument"};

            String[] headers = {"Name", "Start Date", "End Date", "Description", "Second Party Name", "First Party Name", "Active", "Type Of Document"};

            excelFile = CsvHelper.generateCsv(tenancyContracts, TenancyContractProjection.class,
                    headers, namesOrdered, fileName);
        } catch (IOException ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }

        try {
            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, fileName, inputStream);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    @PreAuthorize("hasPermission('tenancyContracts','getTypesOfNotifyDate')")
    @GetMapping(value = "/getTypesOfNotifyDate")
    public ResponseEntity<?> getTypesOfNotifyDate() {

        return ResponseEntity.ok(TenancyContract.NotifyType.values());
    }
}
