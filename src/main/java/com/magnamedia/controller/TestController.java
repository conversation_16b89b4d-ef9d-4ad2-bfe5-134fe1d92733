package com.magnamedia.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.configuration.ObjectMapperConfiguration;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.PushNotification;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.chatai.ChatAIRequestBuilder;
import com.magnamedia.core.helper.ocr.GenericOCRService;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.helper.image.removebackground.CutoutRemoveBackgroundProvider;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.services.chatai.ChatAIService;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.entity.*;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DDMessagingToDoRepository;
import com.magnamedia.service.CreditCardOfferService;
import com.magnamedia.service.DirectDebitSignatureService;
import org.joda.time.LocalDate;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.service.adcb.AccessTokenManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

@RestController
@RequestMapping("/test")
public class TestController extends BaseController {

    private static final Logger logger = Logger.getLogger(TestController.class.getName());

    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private ChatAIService chatAIService;
    @Autowired
    private Utils utils;
    @Autowired
    private CutoutRemoveBackgroundProvider cutoutRemoveBackgroundProvider;
    @Autowired
    private GenericOCRService genericOCRService;
    @Autowired
    private AccessTokenManager accessTokenManager;

    //@Value("${system.property.adcb.token.url}")
    private String tokenUrl;

    @PostMapping("/callGptPrompt/{name}")
    public ResponseEntity<?> callGptPrompt(
            @PathVariable("name") String templateName,
            @RequestParam("photo") MultipartFile photo) throws Exception {

        if (photo == null) return badRequestResponse();
        Template template = Setup.getRepository(TemplateRepository.class).findByNameIgnoreCase(templateName);
        if (template == null) return badRequestResponse();

        Attachment a = utils.getAttachmentFromObject(photo, "temp_callGptPrompt");

        String result = chatAIService.sendToChatGPT(new ChatAIRequestBuilder()
                .template(template)
                .templateParam(new HashMap<String, String>() {{
                    put("img","img");
                }})
                .attachments(new HashMap<String, Attachment>() {{
                    put("img", a);
                }}));

        return ResponseEntity.ok(result);
    }

    @PostMapping("/callCutout")
    public ResponseEntity<?> callCutout(@RequestParam("photo") MultipartFile photo) throws Exception {

        return ResponseEntity.ok(cutoutRemoveBackgroundProvider.removeBackgroundFromPhoto(photo.getInputStream()));
    }

    @NoPermission
    @GetMapping("/executeSelectQuery")
    public ResponseEntity executeSelectQuery(
            @RequestBody Map map,
            @RequestParam String className) throws ClassNotFoundException {

        logger.info("Body: " + map.toString());
        return ResponseEntity.ok(new SelectQuery((String) map.get("query"), "",
                Class.forName(className),(Map<String, Object>) map.get("parameters")).execute());
    }

    @NoPermission
    @PostMapping(value = "/testUploadOcr")
    public ResponseEntity<?> signDDByClientApi(@RequestPart(name = "signatures", required = false) List<MultipartFile> signatures) throws IOException {
        List<Attachment> signatureOcr = new ArrayList<>();
        for (MultipartFile signature : signatures) {
            signatureOcr.add(Setup.getApplicationContext()
                    .getBean(DirectDebitSignatureService.class)
                    .extractSignature(signature.getInputStream(), "Temp Signature.png", "temp_signature"));
        }

        return ResponseEntity.ok(signatureOcr);
    }

    @NoPermission
    @PostMapping(value = "/extractAttachmentInfo")
    public ResponseEntity<?> extractAttachmentInfo(@RequestPart(name = "attachment", required = false) MultipartFile attachment) throws IOException {
        try {

            Attachment attachmentEntity = utils.getAttachmentFromObject(attachment, "temp_ocr_test");
            if (attachmentEntity == null) {
                return ResponseEntity.badRequest().body("Failed to process attachment");
            }
            Object ocrResult = genericOCRService.extract(attachmentEntity);
            logger.info("OCR processing completed for attachment: " + attachment.getOriginalFilename());
            return ResponseEntity.ok(ocrResult);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error processing attachment with OCR: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error processing attachment: " + e.getMessage());
        }
    }

    @NoPermission
    @PostMapping(value = "/addReplacementPayment/{paymentId}//{bankTransactionId}")
    public ResponseEntity<?> addReplacementPayment
            (@PathVariable("paymentId") Payment payment,
             @PathVariable("bankTransactionId") BankStatementTransaction bankTransaction) throws Exception {


        createPaymentsFromClientModule(buildNewPaymentMapFromOldPayment(payment, bankTransaction), true);

        return ResponseEntity.ok("done");
    }

    @Transactional
    public Map createPaymentsFromClientModule(Map payment, boolean forceCreate) throws Exception {
        ObjectMapper objectMapper = Setup.getApplicationContext()
                .getBean(ObjectMapperConfiguration.class).objectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        logger.log(Level.INFO, "payment map keys: " + payment.keySet());
        logger.log(Level.INFO, "payment map values: " + payment.values());


        Map response = Setup.getApplicationContext().getBean(InterModuleConnector.class)
                .call("clientmgmt", "paymentController",
                        "createFromJson", Map.class, new Class[]{Map.class}, payment);
        if (response != null) {
            logger.log(Level.INFO, "response:\n{0}", response.toString());
            return response;
        }
        return new HashMap();
    }

    public Map<String, Object> buildNewPaymentMapFromOldPayment(
            Payment payment,
            BankStatementTransaction bankTransaction) {

        Map<String, Object> paymentContract = new HashMap<>();
        paymentContract.put("id", payment.getContract().getId());

        // ACC-2568
        Map<String, Object> paymentType = null;
        if (payment.getTypeOfPayment() != null) {
            paymentType = new HashMap<>();
            paymentType.put("id", payment.getTypeOfPayment().getId());
            paymentType.put("code", payment.getTypeOfPayment().getCode());
            paymentType.put("name", payment.getTypeOfPayment().getName());
        }

        Map<String, Object> paymentBankName = null;
        if (payment.getBankName() != null) {
            paymentBankName = new HashMap<>();
            paymentBankName.put("id", payment.getBankName().getId());
            paymentBankName.put("code", payment.getBankName().getCode());
            paymentBankName.put("name", payment.getBankName().getName());
        }

        Map<String, Object> paymentMap = new HashMap<>();
        paymentMap.put("amountOfPayment", payment.getAmountOfPayment());
        paymentMap.put("bankName", paymentBankName);
        paymentMap.put("dateOfPayment", payment.getDateOfPayment());
        paymentMap.put("contract", paymentContract);
        paymentMap.put("includeWorkerSalary", payment.getIncludeWorkerSalary());
        paymentMap.put("typeOfPayment", paymentType);
        paymentMap.put("methodOfPayment", payment.getMethodOfPayment().toString());
        paymentMap.put("status", PaymentStatus.RECEIVED.toString());
        paymentMap.put("vatPaidByClient", payment.getVatPaidByClient());

        // ACC-2354
        if (bankTransaction != null && bankTransaction.getId() != null) {
            paymentMap.put("bankStatmentTransactionId", bankTransaction.getId());
            paymentMap.put("updatedFromBankStatement", Boolean.TRUE);
        }

        // ACC-1811 #10 ACC-5048
        if (payment.getDirectDebitFile() != null && payment.getDirectDebitFile().getId() != null) {
            paymentMap.put("directDebitFile", new HashMap<String, Long>() {{
                put("id", payment.getDirectDebitFile().getId());
            }});
        }

        return paymentMap;
    }

    @GetMapping(path = "/testGetMatchedContractPayment")
    @NoPermission
    public ResponseEntity<?> getMatchedContractPayment(@RequestParam("id") ContractPaymentTerm cpt, @RequestParam("source") String source) {

        List<ContractPayment> l = Setup.getApplicationContext()
                .getBean(CreditCardOfferService.class)
                .getMatchedContractPayment(cpt,  "DirectDebit", null, ContractPaymentConfirmationToDo.Source.valueOf(source));

        return ResponseEntity.ok(l);
    }

    @GetMapping(path = "/findByDdMessagingToDosToSend")
    @NoPermission
    public ResponseEntity<?> findByDdMessagingToDosToSend() {

        Date start = new LocalDate().minusDays(1).toDate();
        Long lastId = -1L;
        Page<DDMessagingToDo> page;
        page = Setup.getRepository(DDMessagingToDoRepository.class)
                .findByDdMessagingToDosToSend(lastId, start, PageRequest.of(0, 100));

        return ResponseEntity.ok(page.getContent().size());
    }

    @NoPermission
    @GetMapping("/testAcc8243/{id}")
    public ResponseEntity<?> testAcc8243(@PathVariable("id") Payment p)  {

        try {
            Map<String, Object> paymentMap = new HashMap<>();
            paymentMap.put("id", p.getId());
            paymentMap.put("status", PaymentStatus.RECEIVED.toString());

            LinkedHashMap<String, Object> r = (LinkedHashMap<String, Object>) updatePaymentFromCmDirectCall(paymentMap);
            logger.info("response for updatePaymentFromCmDirectCall: " +  new ObjectMapper().writeValueAsString(r));

            if (HttpStatus.OK.equals(HttpStatus.valueOf((Integer) r.get("statusCodeValue"))) &&
                    !p.getStatus().equals(PaymentStatus.RECEIVED)) {
                logger.info("id: " + p.getId() + "; status: " + p.getStatus());
                p.setStatus(PaymentStatus.RECEIVED);
            }

            return ResponseEntity.ok(p);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return ResponseEntity.ok(p.getStatus());
    }

    @Transactional
    public Object updatePaymentFromCmDirectCall(Map payment) throws Exception {
        logger.info("update Payment");

        payment.put("overridePermissions", true);

        logger.info("payment map keys: " + payment.keySet());
        logger.info("payment map values: " + payment.values());

        Object response = moduleConnector.call("clientmgmt", "paymentController", "update", Object.class, new Class[]{Map.class}, payment);
        logger.info("completed");

        return response;
    }

    @NoPermission
    @GetMapping("/testAcc8243/{id}/{paymentId}")
    public ResponseEntity<?> testAcc8243(@PathVariable("id") Payment p, @PathVariable("paymentId") Payment p1)  {

        try {
            Map<String, Object> paymentMap = new HashMap<>();
            paymentMap.put("id", p.getId());
            paymentMap.put("status", PaymentStatus.RECEIVED.toString());
            paymentMap.put("isReplacement", true);
            paymentMap.put("replacementFor", new HashMap<String, Long>() {{ put("id", p1.getId()); }});
            LinkedHashMap<String, Object> r = (LinkedHashMap<String, Object>) updatePaymentFromCmDirectCall(paymentMap);
            logger.info("response for updatePaymentFromCmDirectCall: " +  new ObjectMapper().writeValueAsString(r));



            return ResponseEntity.ok(r);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return ResponseEntity.ok(p.getStatus());
    }

    @NoPermission
    @GetMapping("/disableOldNotification")
    public ResponseEntity<?> disableOldNotification(int size)  {
        PushNotificationRepository pushNotificationRepository = Setup.getRepository(PushNotificationRepository.class);
        SelectQuery<PushNotification> q =  new SelectQuery<>(PushNotification.class);
        q.filterBy("creatorModule", "=", Setup.getCurrentModule().getId());
        q.filterBy("disabled", "=", false);
        q.setLimit(size);
        int index = 0;

        for (PushNotification p : q.execute()) {
            try {
                p.setDisabled(true);
                p.setLocation(NotificationLocation.INBOX);
                pushNotificationRepository.saveAndFlush(p);
                index++;
            } catch (Exception e){
                e.printStackTrace();
            }

        }
        q.execute()
                .forEach(p -> {

                });

        return ResponseEntity.ok(index);
    }

    @NoPermission
    @PostMapping("/testADCBAccessToken")
    public ResponseEntity<?> testADCBAccessToken(@RequestBody HashMap<String, Object> body) {
        return ResponseEntity.ok(new HashMap<String, String>() {{
            put("token", accessTokenManager.getAccessToken(body));
            put("tokenUrl", tokenUrl);
        }});
    }
}
