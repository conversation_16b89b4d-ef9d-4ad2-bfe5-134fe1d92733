package com.magnamedia.controller;

import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.PurchasingToDo;
import com.magnamedia.repository.PurchaseItemRepository;
import com.magnamedia.repository.PurchasingToDoRepository;
import com.magnamedia.repository.SupplierRepository;
import com.magnamedia.workflow.service.PurchasingFlow;
import com.magnamedia.workflow.service.purchasingsteps.GetBestSupplierStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;

/**
 * Mohammad <PERSON> (Feb 02, 2021)
 */
@RequestMapping("/purchasing")
@RestController
public class PurchasingToDoController extends WorkflowController<PurchasingToDo, PurchasingFlow> {
    @Override
    protected SelectFilter filter(SelectFilter selectFilter, String s, List<String> list, Map<String, String> map) {
        return selectFilter;
    }

    @Autowired
    PurchasingToDoRepository purchasingToDoRepository;
    @Autowired
    PurchaseItemRepository purchaseItemRepository;
    @Autowired
    SupplierRepository supplierRepository;
    @Autowired
    GetBestSupplierStep getBestSupplierStep;

    @Override
    public BaseRepository<PurchasingToDo> getRepository() {
        return purchasingToDoRepository;
    }
}