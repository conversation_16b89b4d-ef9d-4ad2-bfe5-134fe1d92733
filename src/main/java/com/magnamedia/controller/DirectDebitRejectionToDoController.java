package com.magnamedia.controller;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.helper.ConcurrentModificationHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.workflow.entity.projection.DirectDebitCancelationToDoProjection;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoFlow;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitARejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBBouncedRejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBCaseDRejectionWaitingClientReSignStep;
import com.magnamedia.workflow.service.directdebitrejectiontodosteps.DirectDebitBRejectionWaitingReScheduleStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 4-4-2020
 *         Jirra ACC-1595
 */
@RestController
@RequestMapping("/directdebitrejectiontodos")
public class DirectDebitRejectionToDoController
        extends WorkflowController<DirectDebitRejectionToDo, DirectDebitRejectionToDoFlow> {

    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;
    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;
    
    @Override
    public ResponseEntity<?> createEntity(DirectDebitRejectionToDo directDebitRejectionToDo) {
        return super.createEntity(directDebitRejectionToDo);
    }

    @Deprecated
    @Transactional
    public void proccessReminderAndLastReSignTrial(Long directDebitRejectionToDoID) {
        DirectDebitRejectionToDo directDebitRejectionToDo = getRepository().findOne(directDebitRejectionToDoID);
        logger.info("directDebitRejectionToDo id: " + directDebitRejectionToDo.getId());
        directDebitRejectionFlowService.processReminderAndLastReSignTrial(directDebitRejectionToDo);
    }

    @Override
    public BaseRepository<DirectDebitRejectionToDo> getRepository() {
        return directDebitRejectionToDoRepository;
    }

    @Override
    protected SelectFilter filter(SelectFilter filter, String search, List<String> joins, Map<String, String> joinType) {
        return filter;
    }

    @Override
    protected Class<?> getProjectionClass() {
        return DirectDebitCancelationToDoProjection.class;
    }

    @Transactional
    public void processWaitingReScheduleStepForDDB(Long directDebitRejectionToDoID) {
        DirectDebitRejectionToDo directDebitRejectionToDo = getRepository().findOne(directDebitRejectionToDoID);
        logger.log(Level.INFO, "directDebitRejectionToDo id: " + directDebitRejectionToDo.getId());

        if (directDebitRejectionToDo.isCompleted() || directDebitRejectionToDo.isStopped()) {
            logger.log(Level.INFO, "directDebitRejectionToDo is stopped or completed");
            return;
        }

        List<String> currentTasks = directDebitRejectionToDo.getCurrentTasks();
        if (!currentTasks.isEmpty()) {
            DirectDebitRejectionToDoType step = DirectDebitRejectionToDoType.valueOf(currentTasks.get(currentTasks.size() - 1));

            switch (step) {
                case WAITING_RE_SCHEDULE_B: {
                    Setup.getApplicationContext().getBean(DirectDebitBRejectionWaitingReScheduleStep.class)
                            .onDone(directDebitRejectionToDo);
                    break;
                }
            }
        }
    }
}