package com.magnamedia.controller.adcb;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.service.adcb.AdcbApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Controller for ADCB API operations using OAuth authentication
 * 
 * <AUTHOR>
 * Created on Dec 2024
 */
@RestController
@RequestMapping("/adcbApi")
public class AdcbApiController {
    
    private static final Logger logger = Logger.getLogger(AdcbApiController.class.getName());
    
    @Autowired
    private AdcbApiService adcbApiService;

    /**
     * Request account statement using OAuth authentication
     * 
     * @param body The account statement request
     * @return Account statement response
     */
    @NoPermission
    @PostMapping("/generic-api")
    public ResponseEntity<?> requestAccountStatement(
            @RequestBody HashMap<String, Object> body) {
        try {
            logger.log(Level.INFO, "Requesting account statement via REST endpoint");
            
            Map response = adcbApiService.postWithOAuth(
                    (String) body.get("uri"),
                body);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error requesting account statement", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @NoPermission
    @PostMapping("/generic-api/v1")
    public ResponseEntity<?> requestAccountStatementV1(
            @RequestBody HashMap<String, Object> body) {
        try {
            logger.log(Level.INFO, "Requesting account statement via REST endpoint");

            Map response = adcbApiService.postWithCustomOAuth(
                    (String) body.get("uri"),
                    body,
                    (String) body.get("clientId"),
                    (String) body.get("clientSecret"));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error requesting account statement", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint for ADCB API service
     * 
     * @return Health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("ADCB API service is running");
    }
} 