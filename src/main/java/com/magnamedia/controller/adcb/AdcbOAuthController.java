package com.magnamedia.controller.adcb;

import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.service.adcb.AdcbOAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Controller for ADCB OAuth token operations
 * 
 * <AUTHOR>
 * Created on Dec 2024
 */
@RestController
@RequestMapping("/adcbOAuth")
public class AdcbOAuthController {
    
    private static final Logger logger = Logger.getLogger(AdcbOAuthController.class.getName());
    
    @Autowired
    private AdcbOAuthService adcbOAuthService;
    

    /**
     * Get OAuth access token using custom credentials
     * 
     * @param clientId The client ID
     * @param clientSecret The client secret
     * @return OAuth token response
     */
    @NoPermission
    @PostMapping("/token/custom")
    public ResponseEntity<?> getAccessTokenWithCustomCredentials(
            @RequestParam String clientId,
            @RequestParam String clientSecret) {
        try {
            logger.log(Level.INFO, "Requesting OAuth token with custom credentials via REST endpoint");
            return ResponseEntity.ok(adcbOAuthService.getAccessToken(clientId, clientSecret));
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error getting OAuth token with custom credentials", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Health check endpoint for ADCB OAuth service
     * 
     * @return Health status
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("ADCB OAuth service is running");
    }
} 