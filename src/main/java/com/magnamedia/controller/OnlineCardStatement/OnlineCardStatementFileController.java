package com.magnamedia.controller.OnlineCardStatement;

import com.magnamedia.core.Setup;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BackgroundTaskStatus;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.epayment.EPaymentProvider;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.OnlineCardStatement.OnlineCardStatementFile;
import com.magnamedia.helper.BackgroundTaskHelper;
import com.magnamedia.extra.OnlineCardStatementFileProjection;
import com.magnamedia.extra.UploadStatementEntityType;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementFileRepository;
import com.magnamedia.repository.OnlineCardStatement.OnlineCardStatementRecordRepository;
import com.magnamedia.service.OnlineCardStatementService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR> Mahfoud
 **/

// ACC-5587
@RestController
@RequestMapping("/onlineCardStatementFile")
public class OnlineCardStatementFileController extends BaseRepositoryController<OnlineCardStatementFile> {

    @Autowired
    private OnlineCardStatementService onlineCardStatementService;
    @Autowired
    private OnlineCardStatementRecordRepository onlineCardStatementRecordRepository;

    @Override
    public BaseRepository<OnlineCardStatementFile> getRepository() {
        return Setup.getRepository(OnlineCardStatementFileRepository.class);
    }

    @Override
    public ResponseEntity<?> get(@PathVariable("id") Long id) {
        OnlineCardStatementFile onlineCardStatementFile = getRepository().findOne(id);

        if (!onlineCardStatementFile.isResolved() &&
                onlineCardStatementService.isFileFinishedParsing(onlineCardStatementFile)) {
            onlineCardStatementFile.setResolved(true);
            getRepository().save(onlineCardStatementFile);
        }

        return ResponseEntity.ok(onlineCardStatementFile);
    }

    @Override
    @PostMapping("/create")
    @PreAuthorize("hasPermission('onlineCardStatementFile','create')")
    @Transactional
    public ResponseEntity<?> create(
            @RequestBody OnlineCardStatementFile entity) {

        if (entity.getAttachments() == null ||
                entity.getAttachments().isEmpty())
            throw new BusinessException("There is no attachment");

        Attachment att = entity.getAttachments().get(0);
        att = Storage.getAttchment(att.getId());
        entity.setAttachments(Collections.singletonList(att));

        if (!att.getTag().equals("online_card_statement"))
            throw new BusinessException("Attachment tag is incorrect");

        if (!att.getExtension().equals("csv"))
            throw new BusinessException("The file type must be csv");

        ResponseEntity responseEntity = super.create(entity);

        if (responseEntity.getStatusCode().equals(HttpStatus.OK) && responseEntity.getBody() instanceof OnlineCardStatementFile) {
            OnlineCardStatementFile file = (OnlineCardStatementFile) responseEntity.getBody();

            Map<String, Object> payload = new HashMap<>();
            payload.put("entityId", file.getId());
            BackgroundTaskHelper.createBGTParsingStatementUploaded(UploadStatementEntityType.OnlineCardStatementFile,
                            "create_all_online_card_statement_record_from_file_" + entity.getAttachments().get(0).getId(),
                             payload);

            return new ResponseEntity(file.getId(), HttpStatus.OK);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('onlineCardStatementFile','onlineCardStatementFileSearch')")
    @GetMapping(value = "/onlineCardStatementFileSearch")
    public ResponseEntity<?> onlineCardStatementFileSearch(
                @RequestParam(name = "fromDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @RequestParam(name = "toDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @RequestParam(name = "showDeletedFiles", required = false, defaultValue = "false") boolean showDeletedFiles,
            Sort sort,
            Pageable pageable) {

        SelectQuery<OnlineCardStatementFile> query = new SelectQuery<>(OnlineCardStatementFile.class);
        if (fromDate != null)
            query.filterBy("uploadedDate", ">=", fromDate);
        if (toDate != null)
            query.filterBy("uploadedDate", "<",
                    new DateTime(toDate).plusDays(1).withTimeAtStartOfDay().toDate());

        if (!showDeletedFiles)
            query.filterBy("deleted", "=", false);

        if (sort != null) {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        }

        return new ResponseEntity(project(query.execute(pageable),
                OnlineCardStatementFileProjection.class), HttpStatus.OK);

    }

    @PreAuthorize("hasPermission('onlineCardStatementFile','deleteOnlineCardStatementFile')")
    @GetMapping(value = "/deleteOnlineCardStatementFile/{id}")
    public ResponseEntity deleteOnlineCardStatementFile(
            @PathVariable("id") OnlineCardStatementFile onlineCardStatementFile) {

        onlineCardStatementFile.setDeleted(true);
        getRepository().save(onlineCardStatementFile);

        return new ResponseEntity("deleted", HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('onlineCardStatementFile','createPosTransaction')")
    @GetMapping(value = "/createPosTransaction/{fileId}")
    public ResponseEntity<?> createPosTransaction( @PathVariable("fileId") OnlineCardStatementFile f) {

        if (f.getProvider().equals(EPaymentProvider.PAYTABS)) {
            onlineCardStatementService.createPaytabsPosTransaction(f);
        } else {
            onlineCardStatementService.createCheckOutPosTransaction(f);
        }

        return okResponse();
    }
}
