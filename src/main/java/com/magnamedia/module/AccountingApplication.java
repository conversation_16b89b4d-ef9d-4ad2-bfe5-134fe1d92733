package com.magnamedia.module;

import com.magnamedia.core.MagnamediaServer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Nov 11, 2017
 */
@SpringBootApplication
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class AccountingApplication {

    public static void main(String[] args) {
        MagnamediaServer.run("/accounting",
                8080,
                args);
        
            
        
//        StatementCand newLine= new StatementCand("6 Nov 1799999",
//                "POS-PURCHASE CARD NO.443913******1483 F14204156 04-11-2017 1617.10,MYR TRIPSTA BANGKOK:TH",
//                "AED -648.03", "AED 117,652.92");
//        
//        StatementCand newLine2=StatementsCSVParsingLibrary.parsLine(newLine);
//        System.out.println("Transaction Date "+newLine2.getTransactionDate());
//        System.out.println("Credit "+newLine2.getCredit());
//        System.out.println("Debit "+newLine2.getDebit());
//        System.out.println("Balance "+newLine2.getBalance());
//        System.out.println("Operation Type "+newLine2.getOpertaionType());
//        System.out.println("Operation Type "+newLine2.getOperationDate());
//        System.out.println("Company "+newLine2.getCompany());
//        System.out.println("Booking code "+newLine2.getBookingConfirmationCode());
//        
//        CreditCardStatementController t=new CreditCardStatementController();
//        Ticket ticket= t.createTicket();
//        CreditCardStatement statement=t.createStatement(newLine);
//        ticket.setPurchaseDate(new java.sql.Date(statement.getOperationDate().getTime()));
//        ticket.setRefundRequestDate(new java.sql.Date(Date.valueOf("2017-11-30").getTime()));
//        ticket.setFareInRefCurrency(648.0);
////        System.out.println(ticket.getBookingConfirmationNumber().substring(0, 9));
////        System.out.println(StringLibrary.lcs("AGGTAB".toCharArray(), "GXTXAYB".toCharArray(), "AGGTAB".length(), "GXTXAYB".length()));
//        System.out.println(TicketMatchingLibrary.Compare(statement, ticket));
//        System.out.println(ticket.getCardUsed());
//        
//        Ticket ticket2=ticket;
//        CreditCardStatement statement2=statement;
//        List<Ticket> tickets=new ArrayList<Ticket>();
//        tickets.add(ticket);
////        tickets.add(ticket2);
//        List<CreditCardStatement> statements=new ArrayList<CreditCardStatement>();
//        statements.add(statement);
//        statements.add(statement2);
//        
//        System.out.println(TicketMatchingLibrary.MatchTickets(tickets , statements));
//        System.out.println(TicketMatchingLibrary.MatchTickets(tickets , statements).size());
        
    }

}
