package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;


public enum DirectDebitSignatureStatus implements LabelValueEnum{
    APPROVED("APPROVED"),
    UNUSED("UNUSED"),
    UNDER_PROCESS("UNDER_PROCESS"),
    REJECTED("REJECTED");


    private final String label;

    DirectDebitSignatureStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}