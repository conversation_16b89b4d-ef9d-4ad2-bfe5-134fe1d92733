package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR>
 * Created on Apr 13, 2020
 * ACC-1611
 */

public enum DDMessagingType implements LabelValueEnum {

    DirectDebitRejected("Direct Debit Rejected", false, true),
    ClientPaidCashAndNoSignatureProvided("Client paid cash and no signature provided", false, true),
    IncompleteDDRejectedByDataEntry("Incomplete flow / Data entry rejection", false),
    BouncedPayment("Bounced Payment", false),
    IncompleteDDClientHasNoApprovedSignature("Incomplete flow / Missing bank info", false),
    ExpiryPayment("Expiry Payment", false),
    OnlineCreditCardPaymentReminders("Online Credit Card Payment Reminders", false),
    ClientsPayingViaCreditCard("Clients Paying Via Credit Card", false, true),
    Termination("Termination", false), // ACC-6795
    ExtensionFlow("Extension Flow", false, true), // ACC-8954

    // DEPRECATED
    OneMonthAgreement("One Month Agreement", true), // ACC-6647
    SendSmsNextDay("send Sms Next Day", true);

    private final String label;
    private final boolean deprecated;
    private boolean sendPayTabsWithTermination = false;

    DDMessagingType(String label, boolean deprecated) {
        this.label = label; this.deprecated = deprecated;
    }

    DDMessagingType(String label, boolean deprecated, boolean sendPayTabsWithTermination) {
        this.label = label; this.deprecated = deprecated;
        this.sendPayTabsWithTermination = sendPayTabsWithTermination;
    }

    public String getLabel() {
        return label;
    }

    public boolean isDeprecated() {
        return deprecated;
    }

    public boolean isSendPayTabsWithTermination() { return sendPayTabsWithTermination; }
}