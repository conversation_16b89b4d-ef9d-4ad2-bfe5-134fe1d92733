package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jan 23, 2019
 * ACC-373
 */
public enum ExpectedWireTransferStatus implements LabelValueEnum {
    
    Matched("Matched"), 
    Not_Matched("Not Matched");

    private final String label;

    private ExpectedWireTransferStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
