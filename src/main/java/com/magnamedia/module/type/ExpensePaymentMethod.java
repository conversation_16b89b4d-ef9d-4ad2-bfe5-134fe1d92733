package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR>
 */
public enum ExpensePaymentMethod implements LabelValueEnum{
    
    CASH("Cash"),
    CHEQUE("Cheque"),
    SALARY("Salary"),
    CREDIT_CARD("Credit Card"),
    BANK_TRANSFER("Bank Transfer"),
    INVOICED("Invoiced"),
    MONEY_TRANSFER("Money Transfer");

    private final String label;

    ExpensePaymentMethod(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
