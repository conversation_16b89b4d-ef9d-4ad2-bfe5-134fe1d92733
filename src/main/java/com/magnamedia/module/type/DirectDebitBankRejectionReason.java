package com.magnamedia.module.type;


import com.magnamedia.extra.LabelValueEnum;

public enum DirectDebitBankRejectionReason implements LabelValueEnum {
    rr01_signatureincorrect("rr01-signatureincorrect", DirectDebitRejectCategory.Signature),
    rr02_signaturemissing_additionalsignature_s_required("rr02-signaturemissing/additionalsignature(s)required", DirectDebitRejectCategory.Signature),
    rr12_dormantaccount("rr12-dormantaccount", DirectDebitRejectCategory.Account),
    rr05_titlemismatch("rr05-titlemismatch", DirectDebitRejectCategory.Account),
    invalidcustomeridforgivencustomertype("invalidcustomeridforgivencustomertype", DirectDebitRejectCategory.EID),
    rr92_detailsmismatchwithscanneddda("rr92-detailsmismatchwithscanneddda", DirectDebitRejectCategory.Compliance),
    rr91_scannedddaunusable("rr91-scannedddaunusable", DirectDebitRejectCategory.Compliance),
    rr90_complianceissues("rr90-complianceissues", DirectDebitRejectCategory.Compliance),
    rr04_invalidaccount("rr04-invalidaccount", DirectDebitRejectCategory.Invalid_Account),
    rr18_ddanotacceptedbypayingbank_contactyourbank("rr18-ddanotacceptedbypayingbank-contactyourbank", DirectDebitRejectCategory.Authorization),
    rr54_rejectedbyoriginator("rr54-rejectedbyoriginator", DirectDebitRejectCategory.Authorization),
    rr55_unabletoobtainoriginatorconfirmation("rr55-unabletoobtainoriginatorconfirmation", DirectDebitRejectCategory.Authorization);

    private final String label;
    private final DirectDebitRejectCategory category;

    DirectDebitBankRejectionReason(String label, DirectDebitRejectCategory category) {
        this.label = label;
        this.category = category;
    }

    @Override
    public String getLabel() {
        return label;
    }

    public DirectDebitRejectCategory getCategory() {
        return category;
    }

    public static DirectDebitRejectCategory fromString(String value) {
        if (value == null) {
            return DirectDebitRejectCategory.Other;
        }
        for (DirectDebitBankRejectionReason reason : values()) {
            if (reason.getLabel().equals(value)) {
                return reason.getCategory();
            }
        }
        return DirectDebitRejectCategory.Other;
    }
}