/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR>
 */
// ACC-1274
public enum PaymentStatus implements LabelValueEnum{
    PDC("PDP"),
    PRE_PDP("PRE_PDP"),
    ADCB_PDC("ADCB_PDC"),
    DEPOSIT("DEPOSIT"),
    RECEIVED("RECEIVED"),
    BOUNCED("BOUNCED"),
    TEARED_UP("TEARED_UP"),
    RETURNED_TO_CLIENT("RETURNED_TO_CLIENT"),
    DELETED("DELETED"),
    FROZEN("FROZEN"),
    REQUESTED("REQUESTED"),
    UNCOLLECTED("UNCOLLECTED"),
    CANCELLED("CANCELLED"),
    CANCELLED_WAITING_CLIENT_PICKUP("CANCELLED_WAITING_CLIENT_PICKUP");

    private final String label;

    PaymentStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
