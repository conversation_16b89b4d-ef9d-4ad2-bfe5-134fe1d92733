package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jun 23, 2020
 *         Jirra ACC-1689
 */

public enum DirectDebitCancellationToDoReason implements LabelValueEnum {

    SWITCHING_NATIONALITY("Switching Nationality"),
    SWITCHING_TO_VAT("Switching To Vat"),
    SWITCHING_BANK_ACCOUNT("Switching Bank Account"),
    CANCEL_OTHER_AUTO_APPROVED_DDS("Cancel Other Auto Approved DDs"),
    CANCEL_OTHER_MANUAL_APPROVED_DDS("Cancel Other Manual Approved DDs"),
    BOUNCED_PAYMENT_REPLACEMENT("Bounced Payment Replacement"),
    PAYMENT_RECEIVAL_NO_NEED_MONEY("Payment Reveival and no more Need for Money"),
    CONTRACT_CANCELLATION("Contract Cancellation"),
    COLLECTION_FLOW_EOM_JOB("Collection Flow EOM Job"),
    IN_COMPLETE_DDS_DELETING("InComplete DDs Deleting"),
    EXPIRED_DDS_CANCELLATION("Expired DDs Cancellation"),
    OEC_Flow("OEC Flow"),
    MANUALLY_FROM_ERP("Manually From ERP"),
    CLIENT_PAID_CASH_EXTENDING_AFTER_CASH_FLOW("Client Paid Cash Extending After Cash Flow"),
    CONTRACT_ADJUSTED_END_DATE_UPDATED("contract_adjusted_end_date_updated"),
    CLIENT_PAYING_VIA_Credit_Card_FLOW("Client Paying Via Credit Card Flow"),
    ONE_MONTH_AGREEMENT_FLOW("One Month Agreement Flow"),
    PAYMENT_TERM_CONFIG_CHANGED_MANUALLY("Payment Term Config Changed Manually"),
    WORKER_SALARY_CHANGED("Worker Salary Changed"),
    CONTRACT_FREEZING("Contract Freezing"),
    WAIVED_PAYMENT("Waived Payment"),
    SWITCH_LIVE_OUT_STATUS("Switch Live-out Status"),
    HOUSEMAID_FAILED_MEDICAL_CHECK("Housemaid Failed Medical Check");

    private final String label;

    DirectDebitCancellationToDoReason(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
