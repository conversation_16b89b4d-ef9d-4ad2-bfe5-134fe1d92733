package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Sep 22, 2020
 *         ACC-2570
 */

public enum WireTransferTempPaymentRelatesTo implements LabelValueEnum {
    UNKNOWN_WIRE_TRANSFER("Unknown Wire Transfer"),
    EXPECTED_WIRE_TRANSFER("Expected Wire Transfer");

    private final String label;

    private WireTransferTempPaymentRelatesTo(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}
