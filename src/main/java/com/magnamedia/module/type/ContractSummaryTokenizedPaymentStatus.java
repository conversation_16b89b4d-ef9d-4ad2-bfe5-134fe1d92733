package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 * MC-93
 * */
public enum ContractSummaryTokenizedPaymentStatus implements LabelValueEnum {
    RECEIVED("Received"),
    PENDING_COLLECTION("Pending Collection"),
    INSUFFICIENT_FUNDS("Insufficient Funds"),
    EXCEEDS_CARD_LIMIT( "Exceeds Card Limit"),
    ACCOUNT_ISSUE( "Account Issue"),
    EXPIRY_CREDIT_CARD("Expiry Credit Card"),
    OTHERS("Others");

    private final String label;

    ContractSummaryTokenizedPaymentStatus(String label) { this.label = label; }

    @Override
    public String getLabel() {
        return label;
    }
}