package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Feb 9, 2019
 */

public enum PaymentTermConfigType implements LabelValueEnum{
    OUTSIDE_COUNTRY("Outside Country"),
    INSIDE_COUNTRY("Inside Country"),
    RESIDENCY_VISA_RENEWAL("Residency Visa Renewal"),
    LONG_TERM("Long Term"),
    SHORT_TERM("Short Term"),
    SWITCH_FROM_CC_TO_MV("Switch From CC To Mv"),
    SWITCH_FROM_MV_TO_MV("Switch From MV To MV"),
    LIVE_OUT("Live Out");

    private final String label;

    PaymentTermConfigType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}

