package com.magnamedia.module.type;

import com.magnamedia.extra.LabelValueEnum;

public enum TaxiWorkOrderStatus implements LabelValueEnum {
    PENDING("Pending"),
    DONE("Done"),
    CANCELLED("Cancelled"),
    CLOSED("Closed"),
    ONGOING("Ongoing"),
    PENDING_AND_ONGOING("Pending and Ongoing");
    private final String label;

    TaxiWorkOrderStatus(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }

}
