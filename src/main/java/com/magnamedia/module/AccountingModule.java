
package com.magnamedia.module;

import com.magnamedia.controller.CollectionFlowLogController;
import com.magnamedia.core.RunModule;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.MagnamediaModule;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.mail.GmailInbox;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.PositionRepository;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.core.type.ModuleType;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.InvoiceAnnexureMailProcessor;
import com.magnamedia.module.type.ItemMeasureOfConsumption;
import com.magnamedia.module.type.OrderCycle;
import com.magnamedia.scheduledjobs.*;
import com.magnamedia.service.CcAppSmsTemplate;
import com.magnamedia.service.EmailTemplateService;
import com.magnamedia.service.MessageTemplateService;
import com.magnamedia.service.MvAppSmsTemplate;
import com.magnamedia.workflow.service.ExpensePaymentFlow;
import com.magnamedia.workflow.service.MaintenanceRequestFlow;
import com.magnamedia.workflow.service.PurchasingFlow;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR> Alalwani <<EMAIL>>
 *         Created on Nov 11, 2017
 */
@MagnamediaModule
public class AccountingModule implements RunModule {

    public static final String ITEM_MEASURE_OF_CONSUMPTION = "item_measure_of_consumption";
    public static final String STOCK_KEEPER = "Stock keeper";
    public static final String STOCK_KEEPER_MANAGER = "Stock keeper manager";
    public static final String PURCHASE_CONTROLLER_MAIL = "purchase_controller_mail";

    @Autowired
    private PicklistRepository pickListRep;
    @Autowired
    private PositionRepository positionRepository;
    @Autowired
    private MessageTemplateService messageTemplateService;
    @Autowired
    private EmailTemplateService emailTemplateService;
    @Autowired
    private SetupDynamicApis setupDynamicApis;
    @Autowired
    private SetupCcAppCmsTemplates setupCcAppCmsTemplates;
    @Autowired
    private CcAppSmsTemplate ccAppSmsTemplate;
    @Autowired
    private MvAppSmsTemplate mvAppSmsTemplate;

    // Module Codes
    public static final String STAFF_MODULE_CODE = "staffmgmt";
    public static final String CM_MODULE_CODE = "clientmgmt";

    // Module Codes
    public static final String SALES_MODULE_CODE = "sales";

    public static final String RECRUITMENT_MODULE_URL = "/recruitment";
    public static final String PARAMETER_FRONT_END_URL = "FRONT_END_URL";

    // Google Analytics
    public static final String GOOGLE_ANALYTICS_ACTION = "erp";
    public static final String GOOGLE_ANALYTICS_CATEGORY = "ACC";
    public static final String GOOGLE_ANALYTICS_SENDING_IPAM_PAYMENT_REMINDER = "Send IPAM Payment Reminder";

    // CC APP
    public static final String CCAPP_CHANGE_BANK_DETAILS_THRESHOLD = "CCAPP_CHANGE_BANK_DETAILS_THRESHOLD";
    public static final String CCAPP_PAY_BY_CARD_THRESHOLD = "CCAPP_PAY_BY_CARD_THRESHOLD";
    public static final String CCAPP_VIEW_PAYMENT_HISTORY_THRESHOLD = "CCAPP_VIEW_PAYMENT_HISTORY_THRESHOLD";
    public static final String CCAPP_CHANGE_BANK_DETAILS_EMAIL_RECIPIENTS = "CCAPP_CHANGE_BANK_DETAILS_EMAIL_RECIPIENTS";
    public static final String CCAPP_PAY_BY_CARD_EMAIL_RECIPIENTS = "CCAPP_PAY_BY_CARD_EMAIL_RECIPIENTS";
    public static final String CCAPP_VIEW_PAYMENT_HISTORY_EMAIL_RECIPIENTS = "CCAPP_VIEW_PAYMENT_HISTORY_EMAIL_RECIPIENTS";

    // Yayabot Server related
    public static final String YAYA_CHAT_BOT_API = "YAYA_CHAT_BOT_API";
    public static final String YAYA_CHAT_BOT_SENDER_API = "YAYA_CHAT_BOT_SENDER_API";
    public static final String YAYA_CHAT_BOT_PaySlipsSender_Sender_API = "YAYA_CHAT_BOT_PaySlipsSender_Sender_API";
    public static final String YAYA_CHAT_BOT_PaySlips_Photoes_Server = "YAYA_CHAT_BOT_PaySlips_Fhotoes_Server";
    public static final String YAYA_CHAT_BOT_NOT_CONNECTED_MAIDS_API = "YAYA_CHAT_BOT_NOT_CONNECTED_MAIDS_API";
    public static final String YAYA_CHAT_BOT_SALARY_DEDUCTIONS_API = "YAYA_CHAT_BOT_SALARY_DEDUCTIONS_API";
    public static final String YAYA_USER = "YAYA_USER";
    public static final String YAYA_PASSWORD = "YAYA_PASSWORD";
    public static final String MAIDS_AE_Payslips_Sender_API = "MAIDS_AE_Payslips_Sender_API";

    // ACC-9005 ACC-9004
    public static final String RUN_BACKGROUND_TASK_IN_SEQUENTIAL = "run_background_task_in_sequential";
    public static final String SEND_REPORT_EMAIL_AFTER_BACKGROUND_TASKS_FINISHED = "send_report_email_after_background_tasks_finished";

    // ACC-9220
    public static final String RUN_BACKGROUND_TASK_AFTER_CHECK_RELATED_ENTITY_TYPE = "run_background_task_after_check_related_entity_type";
    //Jirra ACC-1087
    // Expensify Server related
    public static final String EXPENSIFY_API = "expensify_api";
    public static final String EXPENSIFY_CURRENCY = "expensify_currency";
    public static final String EXPENSIFY_APPROVED = "expensify_approved";
    public static final String EXPENSIFY_POLICY_ID = "expensify_policy_id";
    public static final String EXPENSIFY_CANCELLATION_USER_ID = "expensify_cancellation_user_id";
    public static final String EXPENSIFY_CANCELLATION_PWD = "expensify_cancellation_pwd";
    public static final String EXPENSIFY_CANCELLATION_EMAIL = "expensify_cancellation_email";
    public static final String EXPENSIFY_CANCELLATION_POLICY_ID = "expensify_cancellation_policy_id";

    public static final String SALESFORCE_APIS_BASE_URL = "SALESFORCE_APIS_BASE_URL";
    public static final String SALESFORCE_APIS_CLEANERS_SALARIES = "SALESFORCE_APIS_CLEANERS_SALARIES";
    // ACC-486 Qatar cleaners payroll API
    public static final String SALESFORCE_APIS_QATAR_CLEANERS = "SALESFORCE_APIS_QATAR_CLEANERS";

    public static final String PICKLIST_PNL_EXPENSE_TYPE = "PNLExpenseType";
    public static final String PICKLIST_NON_CLIENT_PDC_BANK_ISSUER = "NON_CLIENT_PDC_BANK_ISSUER";
    public static final String PICKLIST_SERVICE_TYPE = "SERVICE_TYPE";
    public static final String PICKLIST_PAYMENT_METHOD = "PAYMENT_METHOD";
    public static final String PICKLIST_EXPENSE_REQUESTED_FROM = "PICKLIST_EXPENSE_REQUESTED_FROM";
    public static final String PICKLIST_REFUND_PURPOSE_CATEGORY = "REFUND_PURPOSE_CATEGORY";

    public static final String PICKLIST_MANAGER_NOTE_MANGER_CODE = "FromManager";
    public static final String PICKLIST_PROSPECTTYPE = "ProspectType";
    //    public static final String PICKLIST_MANAGER_NOTE_TYPE_CODE = "NoteType";
    public static final String PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE = "AdditionReasons";
    public static final String PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE = "DeductionReasons";
    public static final String PICKLIST_MAID_MANAGER_REASON_CODE = "MAID_MANAGER_REASON";
    public static final String PICKLIST_TYPE_OF_COMPLAINT_TYPE_CODE = "TypeOfComplaintType";
    public static final String PICKLIST_ITEM_MANAGER_NOTE_REFUND_DEDUCTION_ADDITION_CODE = "refund";
    //Jirra ACC-1093
    public static final String PICKLIST_ITEM_COVER_DEDUCTION_LIMIT_ADDITION_CODE = "cover_deduction_limit";
    public static final String PICKLIST_ITEM_COVER_NEGATIVE_SALARY_ADDITION_CODE = "cover_negative_salary";
    public static final String PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE = "AnnualVacationType";
    public static final String PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE = "SalaryPaymentMethod";
    public static final String PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE = "SalaryTransferDestination";
    public static final String PICKLIST_OFFICESTAFF_TEAM_CODE = "OfficestaffTeam";
    // acc- 1595
    public static final String PICKLIST_TERMINATION_REASON_LIST = "ReasonOfTermination";
    // jira acc-1611
    public static final String PICKLIST_BOUNCED_PAYMENT_STATUS = "BouncedPaymentStatus";

    //Jirra ACC-675
    public static final String PICKLIST_SALARY_RULES_COMPONENTS = "salary_rules_components";

    public static final String PICKLIST_BANK_NAME = "BankName";
    // ACC-5818
    public static final String PICKLIST_CLIENT_REFUND_LENIENCY_TYPES_CODE = "client_refund_leniency_types";

    // Acc-6840
    public static final String PICKLIST_CREDIT_CARD_ERROR_CODES = "credit_card_error_codes";

    // ACC-7580
    public static final String PICKLIST_RISK_DOCUMENT_MANAGEMENT_TYPE = "risk_document_management_type";
    public static final String PICKLIST_RISK_DOCUMENT_MANAGEMENT_IMPORTANCE = "risk_document_management_importance";
    public static final String PICKLIST_RISK_DOCUMENT_MANAGEMENT_GOVERNMENT_ENTITY = "risk_document_management_government_entity";

    //Jirra ACC-1092
    // Jobs Exception Mails Params
    public static final String ClientModule_PARAMETER_WARNING_EMAILS = "ClientModule_PARAMETER_WARNING_EMAILS";

    //ACC-9309_ACC-9058
    public static final String PARAMETER_DATE_TO_EXCLUDE_CONTRACTS_FROM_PAYMENT_EXPIRY_FLOW = "date_to_exclude_contracts_from_payment_expiry_flow";

    //Jiira ACC-1170
    public static final String PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_TITLE = "bucket_balance_assistant_mail_title";
    public static final String PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_ADDRESS = "bucket_balance_assistant_mail_address";
    public static final String PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_OWNER = "bucket_balance_assistant_mail_owner";

    public static final String PARAMETER_ACC5587_INTERVAL_DAYS_ALLOWED = "acc5587_interval_days_allowed";

    // jira acc-1651
    public static final String PARAMETER_LOAN_HOMELESS_MAID = "loan for homeless maid";

    public static final String PARAMETER_OFFER_CC_PAYMENT_BEFORE_X_TRIALS_REMINDER = "offer_cc_payment_before_x_trials_reminder";

    public static final String PICKLIST_HOUSEMIAD_PURPOSE_FOR_MONEY_REQUEST = "HousemaidPurposes";
    //Jiira ACC-3933
    public static final String PICKLIST_HOUSEMAID_PURPOSE_FOR_BONUS_ADDITIONAL_DESCRIPTION = "HousemaidPurposesForBonusAdditionalDescription";
    public static final String PICKLIST_HOUSEMAID_PURPOSE_FOR_MAIDS_AT_OTHER_EXPENSES_ADDITIONAL_DESCRIPTION = "HousemaidPurposesForMaidsAtOtherExpensesAdditionalDescription";

    //Jiira ACC-1187
    public static final String PICKLIST_HOUSEMIAD_PURPOSE_FOR_MONEY_REQUEST_ADDITIONAL_DESCRIPTION = "HousemaidPurposesAdditionalDescription";
    public static final String PICKLIST_OFFICESTAFF_PURPOSE_FOR_MONEY_REQUEST = "OfficestaffPurposes";
    public static final String PICKLIST_CONTRACT_PURPOSE_FOR_MONEY_REQUEST = "ContractPurposes";
    public static final String PICKLIST_MAIDSCC_PURPOSE_FOR_MONEY_REQUEST = "MaidsCCPurposes";
    public static final String PICKLIST_OTHER_PURPOSE_FOR_MONEY_REQUEST = "OtherPurposes";
    public static final String MANAGER_PICK_LIST = "managers";
    public static final String PICKLIST_HOUSEMIAD_PAYROLL_TYPE = "PAYROLL_TYPE";

    public static final String PARAMETER_REQUEST_MONEY_EMAIL = "Request Money Email";
    public static final String PARAMETER_RHIZ_EMAIL = "RHIZ Email";

    public static final String PARAMETER_PNL_Summary_Report_Levels = "Pnl Summary Report Levels";
    public static final String PARAMETER_PNL_Summary_Report_Colored = "Pnl Summary Report Colored";
    public static final String PARAMETER_BACKEND_BASE_URL = "backend_base_url";

    //Jirra ACC-1144
    public static final String PARAMETER_PAYMENT_MAIL_CSV = "payment_mail_csv";
    public static final String PARAMETER_TRANSACTION_MAIL_CSV = "transaction_mail_csv";
    //ACC-5096
    // ACC-9316
    /*public static final String PARAMETER_ACC_5096_REFERENCE_DATE = "acc_5096_reference_date";
    public static final String PARAMETER_ACC_6003_REFERENCE_DATE = "acc_6003_reference_date";*/

    public static final String PARAMETER_CREDIT_CARD_REFUND_FAILED_EMAIL_RECIPIENTS = "credit_card_refund_failed_email_recipients";

    public static final String PARAMETER_UPGRADING_REPLACEMENT_FAILED_AFTER_CLIENT_PAYING_UPGRADING_FEE_EMAIL_RECIPIENTS = "upgrading_replacement_failed_after_client_paying_upgrading_fee_email_recipients";

    public static final String BUCKET_REFILL_ADMIN = "bucket_refill_admin";
    public static final String BUCKET_REFILL_CONTROLLER_NOTIFICATION_TYPE = "bucket_refill_controller";

    //NATIONALITIES picklist
    //Jirra ACC-738
    public static final String PICKLIST_ITEM_NATIONALITY_FILIPINO_1 = "philippines";
    public static final String PICKLIST_ITEM_NATIONALITY_FILIPINO_2 = "filipino";
    public static final String PARAM_ATTENDANCE_DEDUCTION_AMOUNT_FILIPINO = "attendance_deduction_amount_filipino";
    public static final String PARAM_ATTENDANCE_DEDUCTION_AMOUNT_OTHER = "attendance_deduction_amount_other";

    //Jirra ACC-960
    public static final String PICKLIST_TRANSACTION_LICENSE = "transaction_license";
    public static final String PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM = "Mustaqeem";
    public static final String PICKLIST_TRANSACTION_LICENSE_STORAGE_ITEM = "Storage";
    public static final String PICKLIST_TRANSACTION_LICENSE_NO_VAT_ITEM = "no_vat";

    // acc-1611
    public static final String PICKLIST_CALL_REASONS_SMS = "CallReasonSms";
    public static final String PICKLIST_ITEM_CALL_REASONS_SMS_DD_REJECTED = "DDRejected";
    public static final String PICKLIST_ITEM_CALL_REASONS_SMS_DD_MISSING = "DDInfoMissing";
    public static final String PICKLIST_ITEM_CALL_REASONS_SMS_DD_MISSING_WITHOUT_CASH_PAYMENT = "DDInfoMissingWithoutCashPayment";
    public static final String PICKLIST_ITEM_CALL_REASONS_SMS_BOUNCED_PAYMENT = "BouncedPayment";

    public static final String PICKLIST_IBAN_ACCOUNTNAT_REJECTION_REASONS = "iban_accountant_rejection_reason";
    public static final String PICKLIST_EID_ACCOUNTNAT_REJECTION_REASONS = "eid_accountant_rejection_reason";
    public static final String PICKLIST_ACCOUNT_NAME_ACCOUNTNAT_REJECTION_REASONS = "account_name_accountant_rejection_reason";
    public static final String PICKLIST_KNOWN_DD_REJECTION_REASONS = "known_dd_rejection_reasons";

    //Acc-2826
    public static final String PICKLIST_ACCOUNTANT_TODO_TYPES = "accountant_todo_types";
    public static final String PICKLIST_CLIENT_REFUND_ACCOUNTANT_TODO_TYPE = "Refund Client";
    public static final String PICKLIST_OFFICE_STAFF_SALARY_ACCOUNTANT_TODO_TYPE = "Office Staff Salary";
    public static final String PICKLIST_WPS_TRANSFER_ACCOUNTANT_TODO_TYPE = "WPS Transfer";
    public static final String PICKLIST_INTERNATIONAL_SALARIES_TRANSFER_ACCOUNTANT_TODO_TYPE = "International Salaries Transfer";
    public static final String PICKLIST_SALARY_LOCAL_TRANSFER_ACCOUNTANT_TODO_TYPE = "Salary Local Transfer";
    public static final String PICKLIST_PAY_PENSION_ACCOUNTANT_TODO_TYPE = "Pay Pension";
    public static final String PICKLIST_NATIONALITIES = "nationalities";

    //ACC-4047
    public static final String PICKLIST_COLLECTION_FLOW_TYPE = "PICKLIST_COLLECTION_FLOW_TYPE";

    //ACC-3861
    public static final String PICKLIST_TENANCY_TYPE_OF_DOCUMENT = "PICKLIST_TENANCY_TYPE_OF_DOCUMENT";

    //Acc-2913
    public static final String EXPENSE_LOCAL_CURRENCY = "EXPENSE_LOCAL_CURRENCY";
    public static final String EXPENSE_CURRENCY = "EXPENSE_CURRENCY";
    public static final String EXPENSE_CURRENCY_USD = "USD";
    public static final String EXPENSE_CURRENCY_EUR = "EUR";
    public static final String EXPENSE_CURRENCY_AED = "AED";
    public static final String EXPENSE_CURRENCY_QR = "QR";

    public static final String BANK_TRANSFER_PAGE_NOTIFICATION_RECIPIENTS = "bank_transfer_page_notification_recipients";
    public static final String PURCHASE_AUDITOR_PAGE_NOTIFICATION_RECIPIENTS = "purchase_auditor_page_notification_recipients";
    public static final String PURCHASE_MANAGER_PAGE_NOTIFICATION_RECIPIENTS = "purchase_manager_page_notification_recipients";
    public static final String CREDIT_CARD_HOLDER_PAGE_NOTIFICATION_RECIPIENTS = "credit_card_holder_page_notification_recipients";
    public static final String AUDIT_MANAGER_PAGE_NOTIFICATION_RECIPIENTS = "audit_manager_page_notification_recipients";

    // ACC-6831
    public static final String PARAMETER_ACCOUNT_CEILING_EXCEEDED_CC_EMAIL = "account_ceiling_exceeded_cc_email";

    //ACC-2903
    public static final String PICKLIST_PARTIAL_REFUNDS_FOR_CANCELLATION_PAYMENT_METHOD = "Partial_Refund_For_Cancellation_Payments_Method";
    public static final String PICKLIST_PARTIAL_REFUNDS_FOR_CANCELLATION_MONTHLY_PAYMENT = "Client will pay according to the Monthly payment";
    public static final String PICKLIST_PARTIAL_REFUNDS_FOR_CANCELLATION_MINISTRY_RATE = "Client will pay according to the Ministry rate";

    public static final String PICKLIST_CATEGORY_ORDER_CYCLE="category_order_cycle";

    //Jirra ACC-4326
    public static final String PICKLIST_ABSCONDER_MAID_REFUND_OPTIONS="Absconder_Maid_Refund_Options";

    //Jirra ACC-645
    public static final String PARAMETER_DEDUCTION_LIMIT = "Deduction Limit";
    public static final String PARAMETER_FILIPINOES_DEDUCTION_LIMIT = "Filipinoes Deduction Limit";
    public static final String PARAMETER_PAYROLL_JOBS_START = "Payroll Jobs Start";
    public static final String PARAMETER_PAYROLL_JOBS_END = "Payroll Jobs End";

    //Jira ACC-4662
    public static final String PARAMETER_OFFSET_FOR_VALID_EXPENSE_REQUEST = "offset_for_valid_expense_request";

    public static final String PARAMETER_CONTRACT_RETRACT_AFTER_PAYMENT_RECEIVED_REASONS = "parameter_contract_retract_after_payment_received_reasons";

    //ACC-7337
    public static final String PARAMETER_CONTRACT_RETRACT_AFTER_CLIENT_SIGN_DD_REASONS = "contract_retract_after_client_sign_dd_reasons";

    // jira ACC 1495
    public static final String PARAMETER_MAIDSAT_DEDUCTION_LIMIT = "Maids.at Deduction Limit";

    // ACC-441
    public static final String NEW_CONTRACT_PAYMENT_NOTIFICATION_TYPE = "new_contract_payment";
    //Jirra ACC-2059
    public static final String DD_DATA_ENTRY_CLERK_NOTIFICATION_TYPE = "dd_data_entry_clerk_notification_type";

    //Jirra CMA-154
    public static final String BOUNCED_PAYMENT_CLIENT_NOTIFICATION_TYPE = "bounced_payment_client_notification";

    public static final String PARAMETER_CHANGE_PAYMENT_STATUS_ADMIN = "change_payment_status";
    // ACC-505
    public static final String PARAMETER_CHANGE_TRANSACTIONS_DATES_ADMIN_POSITION = "change_transactions_dates_position";
    // ACC-8880
    public static final String CHANGE_TRANSACTIONS_DATES_ADMIN_POSITION = "change_transactions_dates";
    // ACC-9056
    public static final String STATEMENT_FILE_OVERSEES_SALARY_POSITION = "statement_file_oversees_salary";
    public static final String PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET = "statements_transactions_from_bucket";
    // ACC-1674
    public static final String PARAMETER_CHANGE_TRANSACTIONS_DAY_LIMIT = "parameter_change_transactions_day_limit";
    // ACC-600 deletePaymentMatchingFile
    public static final String PARAMETER_DELETE_PAYMENT_MATCHING_FILE_POSITION = "delete_payment_matching_file_position";
    // used to change payment status without conditions
    public static final String ACCOUNTING_ADMIN = "accounting_admin";
    public static final String PARAMETER_CHANGE_PAYMENT_STATUS_BOUNCED = "change_payment_status_bounced";
    public static final String PARAMETER_LOCK_PAYMENTS_ENABLED = "lock payments enabled";

    public static final String PARAMETER_LOCK_PAYROLL_ENABLED = "lock payroll enabled";
    public static final String PARAMETER_LOCK_PAYROLL_START = "lock payroll start day";
    public static final String PARAMETER_LOCK_PAYROLL_END = "lock payroll end day";
    //public static final String PARAMETER_NO_KIDS_DEDUCTION_AMOUNT = "NO_KIDS_DEDUCTION_AMOUNT";
    public static final String PARAMETER_NO_KIDS_DEDUCTION_RESULT_EMAILS = "NO_KIDS_DEDUCTION_RESULT_EMAILS";
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_ENABLED = "START_DATE_N_WEEKS_POLICY_ENABLED";
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_DURATION = "START_DATE_N_WEEKS_POLICY_DURATION";
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_CUTOFF_DATE = "START_DATE_N_WEEKS_POLICY_CUTOFF_DATE";
    public static final String PARAMETER_START_DATE_N_WEEKS_POLICY_EMAILS = "START_DATE_N_WEEKS_POLICY_EMAILS";
    public static final String PARAMETER_RENEWAL_PAGE_WHATSAPP_LINK = "renewal_page_whatsapp_link";

    public static final String PARAMETER_TRANSACTIONS_PAGE_CSV_ROWS_LIMIT = "transactions_page_csv_rows_limit";
    public static final String PARAMETER_PAYMENTS_PAGE_CSV_ROWS_LIMIT = "payments_page_csv_rows_limit";
    //Jirra MC-115
    public static final String PARAMETER_PAYROLL_ACCOUNTANT_TODO_CSV_ROWS_LIMIT = "payroll_accountant_todo_csv_rows_limit";

    public static final String PARAMETER_EXPENSE_REQUEST_TODO_CSV_ROWS_LIMIT = "expense_request_todo_csv_rows_limit";

    // master_ACC-8286
    public static final String PARAMETER_REGEX_TO_CHECK_FILE_NAME_IN_DDF_DOWNLOAD_BATCH_API = "regex_to_check_file_name_in_ddf_download_batch_api" ;

    //ACC-8934
    public static final String PARAMETER_BUCKET_REPLENISHMENT_ALERT_EXCLUDED_USERS_IDS = "bucket_replenishment_alert_excluded_users_ids";

    //Jirra ACC-1092
    public static final String PARAMETER_BOUNCED_PAYMENT_PENALTY = "BOUNCED_PAYMENT_PENALTY";
    public static final String PARAMETER_BOUNCED_PAYMENTS_JOB_ENABLED = "BOUNCED_PAYMENTS_JOB_ENABLED";
    //ACC-820
    public static final String PARAMETER_CLIENTS_EMAIL_CONFIG_MAID_VISA = "CLIENTS_EMAIL_CONFIG_MAID_VISA";
    public static final String PARAMETER_CLIENTS_EMAIL_CONFIG = "CLIENTS_EMAIL_CONFIG";

    //    acc-1962
    public static final String PARAMETER_CASH_ADVANCED_LIMIT = "PARAMETER Cash Advanced limit";


    // Prospect type ITems
    public static final String MAID_CC_PROSPECT_TYPE_CODE = "maids.cc_prospect";
    public static final String MAID_VISA_PROSPECT_TYPE_CODE = "maidvisa.ae_prospect";
    public static final String MAID_CC_PROSPECT_TYPE = "maids.cc prospect";
    public static final String MAID_VISA_PEOSPECT_TYPE = "maidvisa.ae prospect";

    // ACC-8721
    public static final String MV_VATTED_SALARY_KEY = "withVatOnSalary";

    // Direct Debit Attachment Tag

    //Jirra ACC-1092
//    public static final String CC_RESOLVERS_PARAMETER = "CC_resolvers";
//    public static final String CC_ENCHANTERS_PARAMETER = "CC_enchanters";
//    public static final String CC_COLLECTION_PARAMETER = "CC_collection";
//    public static final String MV_ENCHANTERS_PARAMETER = "MV_enchanters";

    //ACC-1682
    public static final String PICKLIST_ITEM_MANAGER_NOTE_PAYING_VACATION_DAYS = "Paying Vacation Days";

    // sales module parameters
    // Jirra ACC-1241
    public static final String SM_DISCOUNT_CODE_VALUE = "discount_code_value";

    // Jirra ACC-2570
    public static final String PARAMETER_MATCHING_WIRE_BOUNCED_PAYMENT = "PARAMETER_MATCHING_WIRE_BOUNCED_PAYMENT";

    //Cash Advance
    // Jirra ACC-392
    public static final String PARAMETER_SEVEN_DAYS_CASH_ADVANCED_DAYS = "PARAMETER SEVEN DAYS CASH ADVANCED DAYS";
    public static final String PARAMETER_FIRST_PERIOD_DAYS_CASH_ADVANCED_DAYS = "PARAMETER FIRST PERIOD DAYS CASH ADVANCED DAYS";
    public static final String PARAMETER_SEVEN_DAYS_CASH_ADVANCED_AMOUNT = "PARAMETER SEVEN DAYS CASH ADVANCED AMOUNT";
    public static final String PARAMETER_FIRST_PERIOD_DAYS_CASH_ADVANCED_AMOUNT = "PARAMETER FIRST PERIOD DAYS CASH ADVANCED AMOUNT";

    // Jirra CMA-154
    public static final String PARAMETER_BOUNCING_FLOW_PAUSING_DAYS = "Parameter Bouncing Flow Pausing Days";

    public static final String PARAMETER_REFUND_DAYS = "PARAMETER_REFUND_DAYS";

    public static final String PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL = "Housemaid Vacation Allowances Email";
    public static final String PARAMETER_OFFICESTAFF_VACATION_ALLOWANCE_EMAIL = "OfficeStaff Vacation Allowances Email";
    public static final String PARAMETER_OFFICESTAFF_WEEKLY_PAYROLL_REPORT_EMAIL = "OfficeStaff Weekly Payroll Report Email";
    public static final String PARAMETER_DATA_CORRECTION_AND_INTEGRITY_EMAILS = "Data Correction And Integrity Emails";
    public static final String PARAMETER_PDCS_WITHIN_WEEK_EMAILS = "PDCs Due within a Week Emails";
    public static final String PARAMETER_PDCS_WITHIN_N_DAYS = "PDCs Due within n days";
    public static final String PARAMETER_PDCS_CONTRACT_END_WITHIN_DAY_EMAILS = "pdcs_contract_end_within_day_emails";
    public static final String PARAMETER_PDCS_CONTRACT_END_WITHIN_WEEK_EMAILS = "pdcs_contract_end_within_week_emails";
    public static final String PARAMETER_PDCS_CONTRACT_END_WITHIN_MONTH_EMAILS = "pdcs_contract_end_within_month_emails";
    public static final String PARAMETER_DDS_AND_COLLECTION_COMPLIANCE_EMAILS = "dds_and_collection_compliance_emails";
    // ACC-8101
    public static final String PARAMETER_SEND_REPORT_IN_MIGRATION_API_EMAILS = "send_report_in_migration_api_emails";
    public static final String PARAMETER_FAILED_BACKGROUND_TASKS_WITHIN_DAY_EMAILS = "parameter_failed_background_tasks_within_day_emails";
    public static final String PARAMETER_CONTRACT_EXCLUDED_FROM_ACC8101_BULK_ADD_DDS_EMAILS = "contract_excluded_from_acc8101_bulk_add_dds_emails";
    public static final String PARAMETER_PAYROLL_EMAILS = "Payroll Emails";
    public static final String PARAMETER_PAYROLL_GENERATION_EMAILS = "Payroll generation Emails";
    public static final String PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS = "Payroll sending to facebook Emails";
    public static final String PARAMETER_PAYROLL_EXCEPTIONS_EMAILS = "Payroll Exceptions Emails";
    //Jirra ACC-874
    public static final String PARAMETER_CHECKLIST_EMAILS = "Checklist Emails";
    public static final String PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS = "Checklist Exceptions Emails";
    public static final String PARAMETER_ADEEB_EMAIL = "Adeeb's Email";
    public static final String PARAMETER_ERROR_EMAIL = "Errors Email";
    public static final String PARAMETER_REPAYMENT_SUMMARY_EMAIL = "REPAYMENT_SUMMARY_EMAIL";
    public static final String PARAMETER_PAYSLIPS_ARE_BEING_GENRATED = "Are paySlips being generated";
    public static final String PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING = "PAYSLIPS_MESSENGER_JOB_IS_RUNNING";
    public static final String PICKLIST_Phone_Employee_Holder = "Phone Employee Holder";
    public static final String PICKLIST_Phone_Usage = "Phone Usage";
    public static final String DEFAULT_MONTHLY_REPAYMENT = "Default Monthly Repayment";
    public static final String DEFAULT_MONTHLY_REPAYMENT_AFRICANS = "Default Monthly Repayment Africans";
    public static final String DEFAULT_MONTHLY_REPAYMENT_ETHIOPIAN = "Default Monthly Repayment ethiopian";
    public static final String DEFAULT_MONTHLY_REPAYMENT_KENYANS = "Default Monthly Repayment Kenyans";
    public static final String DEFAULT_MONTHLY_REPAYMENT_PHILIPPINES = "Default Monthly Repayment Philippines";
    public static final String PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS = "EMPLOYEES WITH NO EID EMAILS ";
    // acc-1777
    public static final String PARAMETER_ACCOUNTANT_EMAILS = "Accountant EMAILS ";
    //Jirra ACC-1939
    public static final String PARAMETER_ACCOUNTANT_EMAILS_FOR_BOUNCING_PAYMENTS = "accountant_emails_for_bouncing_payments";
    public static final String PARAMETER_ACCOUNTANT_EMAILS_CHEQUE_DAYS = "ACCOUNTANT_EMAILS_CHEQUE_DAYS";

    public static final String PARAMETER_NO_KIDS_DEDUCTION_ENABLED = "PARAMETER_NO_KIDS_DEDUCTION_ENABLED";

    public static String POSITION_READ_SECURE_EXPENSE = "read_secure_expense";
    public static String POSITION_COLLECTION_MANAGER_CONDITIONAL_REFUND = "collection_manager_conditional_refund";

    //Jirra ACC-431
    public static final String PARAMETER_lOCAL_STAFF_NATIONALITY_TAG = "LOCAL_NATIONALITY_TAG";

    public static String PARAMETER_HOUSEMAID_RULES_JOB_EMAILS = "HOUSEMAID_RULES_JOB_EMAILS";
    public static String PARAMETER_HOUSEMAID_RULES_JOB_ENABLED = "HOUSEMAID_RULES_JOB_ENABLED";

    public static String PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_MSG = "housemaid_dedcuction_notification_msg";
    public static String PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_SMS = "housemaid_dedcuction_notification_sms";

    // contract fees
    public static final String PARAMETER_CURRENT_RECRUITMENT_FEES_RENEWAL = "Current Recruiment Fees Renewal";
    public static final String PARAMETER_CURRENT_RECRUITMENT_FEES = "Current Recruiment Fees";
    public static final String PARAMETER_CURRENT_RECRUITMENT_FEES_NON_ETHIOPIAN = "Current Recruiment Fees for non ethiopian";
    public static final String PARAMETER_CURRENT_LIVE_OUT_FEES_SHORT_TERM = "Current Live Out Fees Short Term";
    public static final String PARAMETER_DEFAULT_LIVE_OUT_FEES = "Current Live Out Fees Long Term";
    public static final String PARAMETER_DEFAULT_FEES_LONG_TERM = "Current Recruiment Fees Long Term";
    public static final String PARAMETER_DEFAULT_FEES_ETHIOPIAN = "Current Ethiopian Fees ";
    public static final String PARAMETER_DEFAULT_FEES_SHORT_TERM = "Current Recruiment Fees Short Term";
    public static final String PARAMETER_DEFAULT_FEES_SHORT_TERM_NEW_CONTRACTS = "Current Recruiment Fees Short Term New Contracts";
    public static final String PARAMETER_DEFAULT_FEES_SHORT_TERM_NEW_CONTRACTS_ETHIOPIAN = "Current Recruiment Fees Short Term New Contracts Ethiopian";
    public static final String PARAMETER_DEFAULT_FEES_ETHIOPIAN_TYPE2 = "Current Ethiopian Fees Type 2";
    public static final String PARAMETER_DEFAULT_FEES_ETHIOPIAN_SHORT_TERM_TYPE2 = "Current Ethiopian Fees Short Term Type 2";
    public static final String PARAMETER_DEFAULT_FEES_KENYAN_TYPE2 = "Current Kenyan Fees Type 2";
    public static final String PARAMETER_DEFAULT_FEES_KENYAN_SHORT_TERM_TYPE2 = "Current Kenyan Fees Short Term Type 2";
    // acc-1777
    public static final String PARAMETER_CENTER_LOCATION_MESSAGES = "dubai_center_locations"; // value from clientmgmt module
    public static final String PARAMETER_ACCOMMODATION_LOCATION_MESSAGES = "accommodation_location_message";
    public static final String PARAMETER_Local_Recruitment_Whatsapp_Number = "Local_Recruitment_Whatsapp_Number";

    public static final String PARAMETER_HOUSEMAID_HOUSEING_ALLOWANCE = "Housemaid houseing allowance"; // referenced also in complaints
    public static final String PARAMETER_HOUSEMAID_FOOD_ALLOWANCE = "Housemaid food allowance";// referenced also in complaints

    //Jirra ACC-1227
    public static final String PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT = "Maximum working public holidays";// referenced also in complaints

    // ACC-3856
    public static final String PARAMETER_PRE_PDP_PAYMENT_WITHIN_WEEK_EMAILS = "PRE-PDP Payments within a Week Emails";

    public static final String PARAMETER_ELIGIBLE_REFUND_EMAIL = "parameter_eligible_refund_email";
    public static final String PARAMETER_ELIGIBLE_REFUND_EMAIL_CC = "parameter_eligible_refund_email_cc";

    public static final String PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_FROM_BUCKET = "credit_card_statement_file_pos_transaction_default_from_bucket";
    public static final String PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_EXPENSE = "credit_card_statement_file_pos_transaction_default_expense";

    // Add this constant near the other parameter constants at the top of the file
    public static final String PARAMETER_MV_EXTENSION_FLOW_ENABLED = "mv_extension_flow_enabled";

    public static final String PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE = "TypeOfPayment";

    //Picklist Items for TypeOfPayment
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_AGENCY_FEE = "Agency Fee";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DEPOSIT = "Deposit";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT = "Monthly Payment";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_BOUNCED_CHEQUE = "Bounced Cheque Fee";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_EMPLOYING_EX_DH = "Employing Ex DH";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_SERVICE_CHARGE = "Service Charge";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_UPGRADING_NATIONALITY = "Upgrading Nationality";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_ACCOMMODATION = "Monthly Accommodation Fees";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_PAID_THE_CLIENT_REFUND = "Paid the client - Refund";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MAID_PASSPORT = "Payment for Maid's Passport";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DISCOUNT = "Discount";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DALIY_ACCOMMODATION_FEES = "Daily Accommodation Fees";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DAY_TO_DAY_EXTENTSION = "Day-to-day Extension";
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_VACATION_NOT_TAKEN = "Payment for vacation not taken";

    public static final String MV_PAYMENT_PLAN_OPTIONS = "mv_payment_plan_options";
    public static final String CC_PAYMENT_PLAN_OPTIONS = "cc_payment_plan_options";

    public static final String REFUND_REJECT_REASON = "refund_reject_reason";


    //Jirra ACC-1092
    public static final String PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_PARTIAL_PAID_THE_CLIENT_REFUND = "partial_mp_refunded_to_client";
    public static final String PARAMETER_VAT_PROJECT_DATE = "VAT_PROJECT_DATE";
    public static final String TRN = "TRN";

    // ACC-1862
    public static String ELIGIBLE_VACATION_PAYING_DATE = "eligible_vacation_paying_date";

    // ******** Salary Components *************
    //Freedom Cutoff
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_NO_ARABIC = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_ETHIOPIAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_NON_ETHIOPIAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_NON_ETHIOPIAN";
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_KENYAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_KENYAN";
    //Jirra ACC-597
    public static final String SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN";

    //Freedom regardless of Cutoff
    public static final String SALARY_COMPONENTS_FREEDOM_KENYAN = "SALARY_COMPONENTS_FREEDOM_KENYAN";
    public static final String SALARY_COMPONENTS_FREEDOM_SRILANKAN_NO_ARABIC = "SALARY_COMPONENTS_FREEDOM_SRILANKAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_FREEDOM_SRILANKAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_FREEDOM_SRILANKAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_FREEDOM_INDIAN = "SALARY_COMPONENTS_FREEDOM_INDIAN";
    public static final String SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_NO_ARABIC = "SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_FREEDOM_ETHIOPIAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_FREEDOM_NON_ETHIOPIAN = "SALARY_COMPONENTS_FREEDOM_NON_ETHIOPIAN";
    //Jirra ACC-597
    public static final String SALARY_COMPONENTS_FREEDOM_CAMEROONIAN = "SALARY_COMPONENTS_FREEDOM_AFTER_CUTOFF_CAMEROONIAN";

    //Agency
    public static final String SALARY_COMPONENTS_AGENCY = "SALARY_COMPONENTS_AGENCY";

    //ACC-275
    //Visa
    public static final String SALARY_COMPONENTS_VISA = "SALARY_COMPONENTS_VISA";

    //Clean Exit
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_NO_ARABIC = "SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_CLEAN_EXIT_ETHIOPIAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_INDONESIAN = "SALARY_COMPONENTS_CLEAN_EXIT_INDONESIAN";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_INDIAN = "SALARY_COMPONENTS_CLEAN_EXIT_INDIAN";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_KENYAN = "SALARY_COMPONENTS_CLEAN_EXIT_KENYAN";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_NO_ARABIC = "SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_NO_ARABIC";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_ARABIC_SPEAKER = "SALARY_COMPONENTS_CLEAN_EXIT_SRILANKAN_ARABIC_SPEAKER";
    public static final String SALARY_COMPONENTS_CLEAN_EXIT_FILIPINO = "SALARY_COMPONENTS_CLEAN_EXIT_FILIPINO";

    // ACC-1862
    public static final String PAYMENT_REQUEST_PAY_VACATION_DAYS = "Payment_Request_Pay_Vacation_Days";
    // ACC-837 Position
    public static final String PAYMENT_REQUEST_HOUSEMAID_BUSINESS_USER_POSITION = "payments_request_housemaid_business_user_position";
    public static final String PAYMENT_REQUEST_CLIENT_BUSINESS_USER_POSITION = "payments_request_client_business_user_position";
    public static final String PAYMENT_REQUEST_BUSINESS_MANGER_USER_POSITION = "payments_request_business_manger_user_position";
    public static final String PAYMENT_REQUEST_PAYMENT_REQUEST_MANGER_USER_POSITION = "payments_request_payments_request_manger_user_position";
    public static final String PAYMENT_REQUEST_ACCOUNTANT_USER_POSITION = "payments_request_accountant_user_position";
    public static final String PAYMENT_REQUEST_TRANSFERRER_POSITION = "payments_request_transferrer_position";
    public static final String PAYMENT_REQUEST_RELEASER_USER_POSITION = "payments_request_releaser_user_position";
    public static final String PAYMENT_REQUEST_MONEY_MAANAGER_USER_POSITION = "payments_request_money_maanager_user_position";
    // ACC-1186
    public static final String PAYMENT_REQUEST_MAIDS_REFUND_USER_POSITION = "payment_request_maids_refund_user_position";
    public static final String PAYMENT_REQUEST_CLIENTS_REFUND_USER_POSITION = "payment_request_clients_refund_user_position";

    //ACC-2826
    public static final String CLIENT_REFUND_COO_USER_POSITION = "client_refund_coo_user_position";
    // ACC-5794
    public static final String ALLOW_EDIT_PAPER_MODE_POSITION = "allow_edit_paper_mode_position";

    public static final String CLIENT_REFUND_APPROVER_USER_POSITION = "client_refund_approver_user_position";
    public static final String CLIENT_REFUND_REQUESTER_USER_POSITION = "client_refund_requester_user_position";
    public static final String BANK_TRANSFER_ACCOUNTANT_USER_POSITION = "bank_transfer_accountant_user_position";
    public static final String BANK_TRANSFER_REVIEWER_USER_POSITION = "bank_transfer_reviewer_user_position";

    // ACC-8192
    public static final String COO_PAGE_TABS_FULL_CONTROL = "coo_page_tabs_full_control";
    public static final String COO_PAGE_TABS_PENDING_APPROVALS = "coo_page_tabs_pending_approvals";
    public static final String BANK_TRANSFER_COO_USER_POSITION = "bank_transfer_coo_user_position";
    public static final String NIGHT_REVIEW_COO_USER_POSITION = "night_review_coo_user_position";
    // ACC-8812
    public static final String SUPPLIER_EDITOR_POSITION = "supplier_editor_position";

    // ACC-8985
    public static final String EXPENSE_HISTORY_FULL_ACCESS = "expense_history_full_access";

    //ACC-3090
    public static final String EXPENSE_COO_USER_POSITION = "expens_coo_user_position";

    //ACC-2988
    public static final String EXPENSE_AUDIT_MANAGER_USER_POSITION = "expens_audit_manager_user_position";

    public static final String EXPENSE_RECONCILITOR_USER_POSITION = "expens_reconciliator_user_position";

    // Acc-1135
    public static final String PAYMENT_REQUEST_PURPOSE_PARTIAL_REFUND = "payment_request_purpose_partial_refund";
    public static final String PAYMENT_REQUEST_PURPOSE_FULL_REFUND = "payment_request_purpose_full_refund";
    // ACC-2180
    public static final String PAYMENT_REQUEST_PURPOSE_SWITCHING_TO_A_CHEAPER_NATIONALITY_REFUND = "payment_request_purpose_switching_to_a_cheaper_nationality_refund";
    public static final String DDS_CANCELLATION_SCHEDULED_JOB_START_DAY = "dds_cancellation_scheduled_job_start_day";
    public static final String OEC_AMEND_DDS_JOB_START_DAY = "oec_amend_dds_job_start_day";
    public static final String PARAMETER_DDS_CANCELLATION_SCHEDULED_JOB_EXCEPTIONS_EMAILS = "DD Cancellation Scheduled Job Exceptions Emails";

    //ACC-9018
    public static final String ALLOW_ADD_REFUND_TO_MAID_VISA_CONTRACT_POSITION = "allow_add_refund_to_maid_visa_contract_position";

    // Jirra Acc-1350
    public static final String PARAMETER_Full_TIME_MAIDS_AT_CONVERTED_TO_HOUSE_MAID_CASH_ADVANCE_AMOUNT = "full_time_maid_at_converted_to_house_maid_cash_advance_amount";

    public static final String PARAMETER_LAST_CALCULATE_BUCKETS_BALANCE = "last_calculate_buckets_balance";

    //Jirra ACC-1435 from here
    public static final String PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES = "NUMBER_OF_DIRECT_DEBIT_SIGNATURES";
    public static final String PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES_FOR_CLIENT = "NUMBER_OF_DIRECT_DEBIT_SIGNATURES_FOR_CLIENT";
    public static final String PARAMETER_DIRECT_DEBIT_DAY_OF_MONTH_EXPIRY_DAY = "CONTRACT_DIRECT_DEBIT_DAY_OF_MONTH_EXPIRY_DATE";
    public static final String PARAMETER_SUSPEND_DD_MSGS_JOBS = "suspend_dd_msgs_jobs";
    public static final String PARAMETER_ONE_TIME_DD_MONTH_DURATION = "PARAMETER_ONE_TIME_DD_MONTH_DURATION";
    public static final String PARAMETER_MAKE_DD_EXPIRED_CONFIG_JOB = "make_dd_expired_config_job";
    public static final String SW_EOF = "SW_EOF";
    public static final String PARAMETER_SW_ACCOUNT_DAYS = "PARAMETER_SW_ACCOUNT_DAYS";
    public static final String SUSPEND_VAT_JOBS = "suspend_vat_jobs";
    public static final String PARAMETER_VAT_BATCH_SMS_SIZE = "vat_batch_sms_size";
    public static final String PARAMETER_VAT_CONTACT_NUMBERS = "VAT_CONTACT_NUMBERS";
    public static final String SUSPEND_VAT_REMINDER_JOBS = "suspend_vat_reminder_jobs";
    public static final String PARAMETER_VAT_REMINDER_GREETINGS = "VAT_REMINDER_GREETINGS";
    public static final String PARAMETER_UPDATE_CPT_CONFIG_RECIPIENT_ADDRESS = "update_cpt_config_recipient_address";
    public static final String PARAMETER_CONTRACT_PAYMENTS_RECEIPT_HIGHER_PRICE_BOUND = "CONTRACT_PAYMENTS_RECEIPT_HIGHER_PRICE_BOUND";
    public static final String PARAMETER_IBAN_CHECK_API_KEY = "IBAN_VALIDATION_API_KEY";

    // jira acc-1542
    public static final String PARAMETER_SMS_DD_INFO = "SMS_DD_INFO";
    public static final String PARAMETER_SMS_DD_INFO_REMINDER_1 = "SMS_DD_INFO_REMINDER_1";
    public static final String PARAMETER_SMS_DD_INFO_REMINDER_2 = "SMS_DD_INFO_REMINDER_2";
    public static final String PARAMETER_SMS_DD_INFO_REMINDER_3 = "SMS_DD_INFO_REMINDER_3";
    public static final String PARAMETER_SMS_DD_INFO_REMINDER_4 = "SMS_DD_INFO_REMINDER_4";
    public static final String PARAMETER_SMS_DD_INFO_REMINDER_5 = "SMS_DD_INFO_REMINDER_5";

    public static final String PARAMETER_CONTRACT_MAX_DISCOUNT_ALLOWED = "contract_max_discount_allowed";
    public static final String PARAMETER_CONTRACT_VAT_PERCENT = "VAT_PERCENT";
    public static final String CLIENT_MGMT_MODULE_URL = "/clientmgmt";
    public static final String VISA_MODULE_URL = "/visa";
    public static final String CLIENT_MGMT_MODULE_CODE = "clientmgmt";
    public static final String CLIENT_STATUS_PROSPECT = "Prospect";
    public static final String COMPLAINTS_MODULE_CODE = "complaints";

    public static final String INTERVIEW_STATUS_IN_INTERVIEW = "in_interview";
    //Jirra ACC-1435 to here

    public static final String PARAMETER_NUMBER_OF_DAYS_TO_CALCULATE_REFUND_AMOUNT = "number_of_days_to_calculate_refund_amount";

    //Email Sms Template
    public static final String PAYMENT_EMAIL_TO_APPROVE_REQUEST_BUSINESS_MANAGER = "payment_email_to_approve_request_business_manager";
    public static final String PAYMENT_EMAIL_TO_CONFIRM_REQUEST_ACCOUNTANT = "payment_email_to_confirm_request_accountant";
    public static final String PAYMENT_EMAIL_TO_APPROVE_REQUEST_CFO = "payment_email_to_approve_request_cfo";
    public static final String PAYMENT_EMAIL_TO_APPROVE_ACCOUNTANT_REQUEST_TO_CFO = "payment_email_to_approve_accountant_request_to_cfo";
    public static final String PAYMENT_EMAIL_OF_ACCOUNTANT_NOTES_TO_BUSINESS_MANAGER = "payment_email_of_accountant_notes_to_business_manager";
    public static final String PAYMENT_EMAIL_TO_CLIENT = "payment_email_to_client";

    //Collection Sms
    public static final String COLLECTION_SMS_MAID_VISA_811 = "collection_sms_maid_visa_811";
    public static final String COLLECTION_SMS_MAID_VISA_812 = "collection_sms_maid_visa_812";
    public static final String COLLECTION_SMS_MAID_VISA_813 = "collection_sms_maid_visa_813";
    public static final String COLLECTION_SMS_MAID_VISA_814 = "collection_sms_maid_visa_814";

    //Pages
    public static final String PAYMENT_REQUEST_BUSINESS_MANAGER_TODO = "payment_request_business_manager_todo";
    public static final String PAYMENT_REQUEST_ACCOUNTANT_TODO = "payment_request_accountant_todo";
    public static final String PAYMENT_REQUEST_PROOF_PAGE = "payment_request_proof_page";

    //Files Tags
    public static final String FILE_TAG_PROOF_OF_TRANSFER = "proof_of_transfer";
    public static final String FILE_TAG_SIGNED_RECEIPT = "signed_receipt";

    //Files Tags
    public static final String CFO_USERNAME = "cfo_username";

    //Jira ACC-4047
    public static final String PARAMETER_COLLECTION_FLOW_DEBUG_MAILS = "COLLECTION_FLOW_DEBUG_MAILS";
    // jira acc-1545
    public static final String PARAMETER_DEFAULT_DPI_RESOLUTION = "DEFAULT_DPI_RESOLUTION";
    // jira ACC-1922
    public static final String ACTIVATE_QUERY_LOG_FOR_TRANSACTIONS = "ACTIVATE_QUERY_LOG_FOR_TRANSACTIONS";
    public static final String PARAMETER_DD_IMAGES_FORMAT = "PARAMETER_DD_IMAGES_FORMAT";
    public static final String PARAMETER_DD_IMAGES_LOSS_VALUE = "PARAMETER_DD_IMAGES_LOSS_VALUE";
    //Jirra ACC-2621
    public static final String PARAMETER_DEFAULT_DPI_RESOLUTION_CANCELLATION = "DEFAULT_DPI_RESOLUTION_CANCELLATION";
    public static final String PARAMETER_DD_IMAGES_FORMAT_CANCELLATION = "PARAMETER_DD_IMAGES_FORMAT_CANCELLATION";
    public static final String PARAMETER_DD_IMAGES_LOSS_VALUE_CANCELLATION = "PARAMETER_DD_IMAGES_LOSS_VALUE_CANCELLATION";
    //Jirra ACC-1604
    public static final String PARAMETER_DD_ACTIVATION_BATCH_MAX = "dd_activation_batch_max";
    public static final String PARAMETER_DD_SIGNATURE_BATCH_SIZE = "dd_signature_batch_size";

    //Jirra ACC-1590
    public static final String PARAMETER_NOT_PRORATED_CONTRACT_START_DATE = "not_prorated_contract_start_date";
    //Jirra ACC-2115
    public static final String PARAMETER_MAID_VISA_CONTRACT_START_DATE_FOR_INITIAL_PAYMENTS = "maid_visa_contract_start_date_for_initial_payments";

    // ACC-9337
    public static final String PARAMETER_SCHEDULED_DAY_FOR_TERMINATION_IN_EXTENSION_FLOW = "scheduled_day_for_termination_in_extension_flow";

    //Jirra ACC-1721
    public static final String PAYMENT_REQUEST_PURPOSE_DUPLICATED_PAYMENT = "payment_request_purpose_duplicated_payment";

    public static final String PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP = "payment_request_purpose_maid_not_finishing_medical_step";

    // ACC-8189 blocker on pre-collected salary (ACC-8796)
    public static final String PARAMETER_PCS_FAILED_MEDICAL_PAYMENT_REQUEST_PURPOSE = "pcs_failed_medical_payment_request_purpose";

    //Jirra ACC-1598
    public static final String PARAMETER_MANUAL_DD_BATCH_MAX = "manual_dd_batch_max";
    public static final String PARAMETER_MANUAL_DD_BATCH_INITIAL_INCREMENT = "manual_dd_batch_initial_increment";
    public static final String PARAMETER_MANUAL_DD_BATCH_RECORD_INDEX = "manual_dd_batch_record_index";
    public static final String PARAMETER_MANUAL_DD_BATCH_FILE_INDEX = "manual_dd_batch_file_index";
    public static final String PARAMETER_APPROVED_MANUAL_DD_EMAIL = "approved_manual_dd_email";

    // jira acc-1595

    public static final String PARAMETER_DD_MAX_TRIALS = "MaxTrials";
    public static final String PARAMETER_DD_MAX_RE_SIGN_TRIALS = "MaxReSignTrials";
    public static final String PARAMETER_DD_MAX_SAME_SIGNATURE_TRIALS = "MaxSameSignatureTrials";

    public static final String PARAMETER_REJECTION_FLOW_START_PAYING_VIA_CC_TRIAL = "rejection_flow_start_paying_via_cc_trial";

    // jira acc-4603
    public static final String PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC = "bounced_max_trials_for_cc";
    public static final String PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV = "bounced_max_trials_for_mv";

    // Parameters for contract termination days
    public static final String PARAMETER_DD_POSTPONE_SCHEDULE_TERMINATION_CC = "dd_postpone_schedule_termination_cc";
    public static final String PARAMETER_DD_POSTPONE_SCHEDULE_TERMINATION_MV = "dd_postpone_schedule_termination_mv";
    public static final String PARAMETER_DD_POSTPONE_SCHEDULE_TERMINATION_RECIPIENTS = "dd_postpone_schedule_termination_recipients";

    // ACC-9005
    public static final String PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD = "specify_background_task_queue_of_bank_dds_cancellation_record";


    public static final String PARAMETER_DD_MAX_REMINDERS = "MaxReminders";
    public static final String PARAMETER_DD_MAX_SIGN_REMINDERS = "MaxSignReminders";
    public static final String PARAMETER_DD_REMINDER_PERIOD = "ReminderPeriod";
    public static final String PARAMETER_DD_SIGN_REMINDER_PERIOD = "SignReminderPeriod";
    public static final String PARAMETER_DD_SIGN_LAST_TRIAL_PERIOD = "SignLastTrialPeriod";

    // ACC-9004
    public static final String PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_ACTIVATION_RECORD = "specify_background_task_queue_of_bank_dds_activation_record";


//    public static final String PARAMETER_DD_MAX_BANK_INFO_TRIALS = "MaxBankInfoTrials";

    public static final String PARAMETER_DD_MAX_AUTO_TRIALS = "MaxAutoDDTrials";
    public static final String PARAMETER_DD_MAX_MANUAL_TRIALS = "MaxManualDDTrials";

//    public static final String PARAMETER_DD_MAX_SAME_SIGNATURE_TRIALS_B = "MaxSameSignatureTrialsB";
//    public static final String PARAMETER_DD_MAX_SIGNATURE_TRIALS_B = "MaxSignatureTrialsB";

    // acc-1597
    public static final String PARAMETER_SCHEDULE_FOR_TERMINATION_AFTER_DAYS = "PARAMETER_SCHEDULE_FOR_TERMINATION_AFTER_DAYS";

    //Jirra ACC-2002
    public static final String PARAMETER_NON_DD_PAYMENT_EMAIL = "non_dd_payment_email";
    //Jirra ACC-2059
    public static final String PARAMETER_DD_DATA_ENTRY_CLERK_EMAIL = "dd_data_entry_clerk_email";

    //ACC 8752
    public static final String PARAMETER_TRANSACTION_POSTING_RULE_NOT_FOUND_EMAIL = "transaction_posting_rule_not_found_email";

    public static final String PARAMETER_PAYMENT_TYPES_DONT_REQUIRE_POSTING_RULE = "payment_types_dont_require_posting_rule";

    // ACC-9272
    public static final String PARAMETER_AMWAL_WALLET_AUTO_ADJUST_THRESHOLD = "amwal_wallet_auto_adjust_threshold";
    // MC-128
    public static final String PARAMETER_NOQODI_WALLET_AUTO_ADJUST_THRESHOLD = "noqodi_wallet_auto_adjust_threshold";

    //Jirra 1689
    public static final String PARAMETER_SWITCH_MAID_EOF = "SWITCH_MAID_EOF";

    //Jirra ACC-2200
    public static final String PARAMETER_DEFAULT_EXTENSION_DURATION = "default_extension_duration";
    public static final String PARAMETER_MIN_PRORATE_AMOUNT = "min_prorate_amount";
    public static final String PARAMETER_NUMBER_OF_DAYS_TO_CREATE_HIDDEN_DDS = "number_of_days_before_eom_to_create_hidden_dds";

    //Jirra ACC-2024
    public static final String PARAMETER_IN_COMPLETE_DD_MAX_REMINDER = "in_complete_dd_info_max_reminder";
    public static final String PARAMETER_IN_COMPLETE_DD_MAX_TRIALS = "in_complete_dd_info_max_trials";
    //Jirra ACC-2275
    public static final String PICKLIST_ITEM_CONTRACT_TERMINATION_REASON_FROZEN_CONTRACT_CODE = "frozen_contract";

    //Jirra ACC-2282
    public static final String PARAMETER_CLIENT_MGMT_RECEIVED_PAYMENT_LOCK_DAY = "RECEIVED_PAYMENT_LOCK_DAY";
    //Jirra CMA-1484
    public static final String PARAMETER_CLIENT_MGMT_CC_APP_DOWNLOAD_URL = "CC_APP_DOWNLOAD_URL";
    public static final String PARAMETER_CLIENT_MGMT_CC_APP_DOWNLOAD_URL_PHRASE = "CC_APP_DOWNLOAD_URL_PHRASE";

    //Jirra ACC-2463
    public static final String PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_ALL_MONTHS = "difference_period_for_received_full_payment_all_months";
    public static final String PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_FEBRUARY = "difference_period_for_received_full_payment_february";

    public static final String PARAMETER_CREDIT_CARD_DEFAULT_MANAGER = "credit_card_default_manager";

    //Jirra ACC-2563
    public static final String PARAMETER_PNL_CHANGE_CUTOFF_DAY = "pnl_change_cutoff_day";

    //Jirra ACC-2764
    public static final String PARAMETER_GEORGE_EMAIL = "george_email";
    public static final String CC_APP_RETRACT_CANCELLATION_MAIL_RECIPIENTS = "cc_app_retract_cancellation_mail_recipients";
    public static final String PARAMETER_DD_REQUESTED_AND_CLIENT_PAYING_VIA_CREDIT_CARD_RECIPIENTS = "dd_requested_and_client_paying_via_credit_card_recipients";
    public static final String PARAMETER_MARIO_MOBILE_NUMBER = "mario_mobil_number";

    //Jirra ACC-2777
    public static final String PARAMETER_GEORGE_EMAIL_NEW_DD_REJECTION_REAOSNS_REPORT = "george_email_new_dd_rejection_reasons_report";
    //public static final String PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL = "dd_auth_reject_expert_tod_reason_to_call";
    //public static final String PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES = "dd_auth_reject_expert_tod_initial_notes";

    //Jirra ACC-283811
    public static final String PARAMETER_DD_FORM_MANAGER_NAME = "dd_form_manager_name";

    //ACC-2826
    public static final String PARAMETER_CLIENT_REFUND_AGENCY_NAME = "client_refund_agency_name";
    public static final String PARAMETER_CLIENT_REFUND_AGENCY_EMAIL = "client_refund_agency_email";
    public static final String PARAMETER_CLIENT_REFUND_PENSION_AUTHORITY_NAME = "client_refund_pension_authority_name";

    //Jirra ACC-3107
    public static final String PARAMETER_CLIENT_REFUND_AGENCY_IBAN = "client_refund_agency_iban";
    public static final String PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME = "client_refund_agency_account_number";

    // ACC-8896
    public static final String PARAMETER_MIN_DATE_PAYMENTS_VALUE = "min_date_payments_value";

    //Jirra ACC-2909
    public static final String PARAMETER_DDS_ACTIVATION_RPA_MAIL = "dds_activation_rpa_mail";
    public static final String PARAMETER_DDS_CANCELLATION_RPA_MAIL = "dds_cancellation_rpa_mail";
    public static final String PARAMETER_AUTO_CONFIRM_DD_RPA_RECORDS = "auto_confirm_dd_rpa_records";

    // ACC-9005
    public static final String PARAMETER_DDS_CANCELLATION_REPORT_RPA_MAIL = "dds_cancellation_report_rpa_mail";
    //Jirra ACC-2860
    public static final String PARAMETER_SIGNING_PAPER_MODE_THRESHOLD_TRIAL = "signing_paper_mode_threshold_trial";

    public static final String PARAMETER_DISCOUNT_EFFECTIVE_AFTER_THRESHOLD = "discount_effective_after_threshold";

    // ACC-9004
    public static final String PARAMETER_DDS_ACTIVATION_REPORT_RPA_MAIL = "dds_activation_report_rpa_mail";

    //    //Jirra ACC-3447
    public static final String PARAMETER_BOUNCING_REJECTION_SIGNING_PAPER_MODE_THRESHOLD_TRIAL = "bouncing_rejection_signing_paper_mode_threshold_trial";
   public static final String PARAMETER_BOUNCING_No_MANUAL_DD_SCHEDULE_CONTRACT_FOR_TERMINATION_MAX_TRIAL = "parameter_bouncing_no_manual_dd_schedule_contract_for_termination_max_trial";
    //Jirra ACC-2933
    public static final String PARAMETER_SWITCHING_NATIONALITY_TRIAL_DAYS = "switching_nationality_trial_days";
    //Jirra ACC-3555
    public static final String PARAMETER_CC_CONTRACT_CHARGE_FREE_DAYS = "cc_contract_charge_free_days";
    public static final String MAX_SIGNATURE_COLLECTION_REMINDERS  = "max_signature_collection_reminders";
    public static final String SCHEDULED_DD_SEND_NOTIFICATION_PERIOD_BEFORE_START_DATE = "scheduled_dd_send_notification_period_before_start_date";
    public static final String MAX_SIGNATURE_COLLECTION_TRIALS = "max_signature_collection_trials";
    public static final String PERIOD_BETWEEN_SIGNATURE_COLLECTION_REMINDER = "period_between_signature_collection_reminder";

    // MC-93
    public static final String PARAMETER_BOT_USERS_LOGIN_NAMES = "bot_users_login_names";

    public static final String DD_GEN_PRE_POSTPONE_PERIOD = "dd_gen_pre_postpone_period";
    public static final String PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY = "dd_gen_postpone_start_after_x_day";
    public static final String PARAMETER_ACCEPTED_GEN_POSTPONE_GENERATION_DATE_PASSED_X_DAY = "accepted_gen_postpone_generation_date_passed_x_day";
    public static final String ADJUSTED_END_DATE_DD_GEN_PRE_POSTPONE_PERIOD = "adjusted_end_date_dd_gen_pre_postpone_period";

    public static final String FAILED_DDS_GENERATION_REPORT_RECIPIENT = "failed_dds_generation_report_recipient";

    public static final String PARAMETER_NON_MONTHLY_POSTPONED_DDS_GENERATION_REPORT_RECIPIENT = "non_monthly_postponed_dds_generation_report_recipient";

    public static final String PARAMETER_CC_CONTRACT_PRORATED_CHARGE_FREE_DAYS = "cc_contract_prorated_charge_free_days";

    public static final String NEW_DDA_CREATED_START_DATE_PASSED_REPORT_RECIPIENT = "new_dda_created_start_date_passed_report_recipient";

    public static final String PARAMETER_DD_MULTI_CONFIRMED_EMAILS = "parameter_dd_email_notification";
    // ACC-5608
    public static final String PARAMETER_EXPIRED_LINK_WHATSAPP_NUMBER = "expired_link_whatsapp_number";

    public static final String PARAMETER_ACC5044_CONTRACT_CREATION_DATE = "acc5044_contract_creation_date";
    public static final String PARAMETER_ACC5044_MONTHLY_PAYMENT_AMOUNT = "acc5044_monthly_payment_amount";

    //Jirra CMA-996
    public static final String PARAMETER_SWITCHING_NATIONALITY_DD_FORMS_AMENDING_CONFIRMATION_MAIL = "switching_nationality_dd_forms_amending_confirmation_mail";
    //ACC-2903
    public static final String PARAMETER_CLIENT_REFUND_NUMBER_OF_UNUSED_DAYS_VALIDATION = "client_refund_number_of_unused_days_validation";
    //ACC-3126
    public static final String PARAMETER_PAYMENT_REQUEST_PURPOSE_COMPENSATING_ABOVE_4000 = "payment_request_purpose_compensating_above_4000";
    public static final String PARAMETER_PAYMENT_REQUEST_PURPOSE_COMPENSATING_BELOW_4000 = "payment_request_purpose_compensating_below_4000";
    //Jirra ACC-2885
    public static final String PARAMETER_UNSUCCESSFUL_DD_CANCELLATION_REPORT_MAIL = "unsuccessful_dd_cancellation_report_mail";

    //Jirra ACC-2887
    public static final String PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_DAYS = "dd_pending_for_cancellation_sj_days";
    public static final String PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_EMAIL = "dd_pending_for_cancellation_sj_email";

    // ACC-8596
    public static final String PARAMETER_PAYMENT_REQUEST_PURPOSE_SAME_DAY_RECRUITMENT_FEE_FOR_MAID_VISA = "payment_request_purpose_same_day_recruitment_fee_for_maid_visa";
    public static final String PARAMETER_MV_MAID_FAILED_EMAIL_RECIPIENTS = "parameter_mv_maid_failed_email_recipients";
    public static final String PARAMETER_MV_MAID_FAILED_TICKET_AND_ADMINISTRATION_FEES = "mv_maid_failed_ticket_and_administration_fees";

    // ACC-8781
    public static final String PARAMETER_POPUP_WARNING_MESSAGE_DAYS_IN_IPAM_FLOW = "popup_warning_message_days_in_ipam_flow ";

    //ACC-3083
    public static final String SALES_BINDER_API_URL = "sales_binder_api_url";
    public static final String SALES_BINDER_API_KEY = "sales_binder_api_key";

    //Visa expense transaction from bucket
    public static final String EDIRHAMS_VISA_EXPENSE_TRANSACTION_FROM_BUCKET = "edirhams_visa_expense_transaction_from_bucket";
    public static final String NOQOODI_VISA_EXPENSE_TRANSACTION_FROM_BUCKET = "noqoodi_visa_expense_transaction_from_bucket";
    public static final String CBD_VISA_EXPENSE_TRANSACTION_FROM_BUCKET = "cbd_visa_expense_transaction_from_bucket";
    public static final String EWALLET_VISA_EXPENSE_TRANSACTION_FROM_BUCKET = "ewallet_visa_expense_transaction_from_bucket";
    public static final String CASH_VISA_EXPENSE_TRANSACTION_FROM_BUCKET = "cash_visa_expense_transaction_from_bucket";
    public static final String PAY_PRO_WALLET_VISA_EXPENSE_TRANSACTION_FROM_BUCKET = "pay_pro_wallet_visa_expense_transaction_from_bucket";

    //Jirra ACC-3469
    public static final String PARAMETER_LOAN_EXPENSE_CODE = "loan_expense_code";

    //Jirra ACC-3315
    public static final String PARAMETER_REMINDING_THE_COO_OF_APPROVAL_TASKS = "reminding_the_coo_of_approval_tasks";
    public static final String PARAMETER_COO_SENDING_INQUIRIES = "coo_sending_inquiries";

    public static final String PARAMETER_BANK_CONFIRMATION_AND_NIGHT_REVIEW_RECIPIENTS = "bank_confirmation_and_night_review_recipients";

    public static final String PARAMETER_NO_PRORATED_DD_FOR_ONE_MONTH_AGREEMENT_EMAIL_RECEIPTS = "no_prorated_dd_for_one_month_agreement_email_receipts";

    public static final String PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_MANUAL_SWITCHING_NATIONALITY_EMAIL_RECEIPTS = "client_paying_via_credit_card_manual_switching_nationality_email_receipts";

    public static final String PARAMETER_CREDIT_CARD_UNKNOWN_ERROR_CODES_EMAIL_RECEIPTS = "credit_card_unknown_error_codes_email_receipts";

    public static final String PARAMETER_CASHIER_REJECT_CASH_COLLECTION_RECIPIENT = "parameter_cashier_reject_cash_collection_recipient";

//    public static final String NBR_DAYS_BEFORE_START_ORDER_CYCLE = "nbr_days_before_start_order_cycle";
    public static final String PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_DOWNGRADE_RECIPIENTS_EMAIL = "client_paying_via_credit_card_downgrade_recipients_email";

    public static final String PARAMETER_CLIENT_PAYING_VIA_CC_SWITCH_NATIONALITY_RECIPIENTS_EMAIL = "client_paying_via_cc_switch_nationality_recipients_email";
    public static final String PARAMETER_PAYMENT_COLLECTED_AFTER_TERMINATION_RECEIPTS = "payment_collected_after_termination_receipts";

    //    public static final String NBR_DAYS_BEFORE_START_ORDER_CYCLE = "nbr_days_before_start_order_cycle";
    public static final String NBR_DAYS_BEFORE_START_ORDER_CYCLE_WEEKLY = "nbr_days_before_start_order_cycle_weekly";
    public static final String NBR_DAYS_BEFORE_START_ORDER_CYCLE_MONTHLY = "nbr_days_before_start_order_cycle_monthly";

//    public static final String NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE = "nbr_days_before_start_reminder_order_cycle";
    public static final String NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_WEEKLY = "nbr_days_before_start_reminder_order_cycle_weekly";
    public static final String NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_MONTHLY = "nbr_days_before_start_reminder_order_cycle_monthly";

    public static final String REMINDERS_TO_STOCK_KEEPER = "reminders_to_stock_keeper";
    public static final String STOCK_KEEPER_WEEKLY_REMINDER_INDEX = "stock_Keeper_weekly_reminder_index";
    public static final String STOCK_KEEPER_MONTHLY_REMINDER_INDEX = "stock_Keeper_monthly_reminder_index";

    public static final String PARAMETER_EXPENSE_NOTIFICATION_NUMBER_OF_REQUESTS_THRESHOLD = "expense_notification_number_of_requests_threshold";

    public static final String PARAMETER_WORSE_CASE_SCENARIO_TAG = "WORSE_CASE_SCENARIO_TAG";
    public static final String PARAMETER_WEEK_START_TAG= "WEEK_START_TAG";
    public static final String PARAMETER_TRANSPORTATION_EXPENSE_CODE = "TRANSPORTATION_EXPENSE_CODE";
    public static final String PARAMETER_TAXI_REIMBURSEMENT_EXPENSE_CODE = "TAXI_REIMBURSEMENT_EXPENSE_CODE";
    public static final String PARAMETER_TAXI_REIMBURSEMENT_APPLICANT_EXPENSE_CODE = "PARAMETER_TAXI_REIMBURSEMENT_APPLICANT_EXPENSE_CODE";
    public static final String PARAMETER_COVID_TEST_EXPENSE_CODE = "COVID_TEST_EXPENSE_CODE";
    public static final String PARAMETER_COVID_LASER_TEST_EXPENSE_CODE = "COVID_LASER_TEST_EXPENSE_CODE";
    public static final String PARAMETER_CAREEM_SUPPLIER_NAME= "CAREEM_SUPPLIER_NAME";
    public static final String PARAMETER_HALA_SUPPLIER_NAME= "HALA_SUPPLIER_NAME";
    public static final String PARAMETER_EXPENSE_VACATION_DAYS_CODE = "EXPENSE_VACATION_DAYS_CODE";
    public static final String PARAMETER_EXPENSE_SALARY_DISPUTE_CODE = "EXPENSE_SALARY_DISPUTE_CODE";
    public static final String PARAMETER_EXPENSE_BONUS_CODE = "EXPENSE_BONUS_CODE";
    public static final String PARAMETER_EXPENSE_MAIDS_AT_OTHER_EXPENSES_CODE = "EXPENSE_MAIDS_AT_OTHER_EXPENSES_CODE";
    public static final String PARAMETER_INSURANCE_EXPENSE_CODE = "INSURANCE_EXPENSE_CODE";

    public static final String PARAMETER_PAYMENT_TYPES_TO_BE_COLLECTED_BY_CREDIT_CARD = "payment_types_to_be_collected_by_credit_card";

    // ACC-8875
    public static final String PARAMETER_DAYS_BEFORE_SEND_NOTIFICATION_FOR_GENERATION_PLAN_INSURANCE = "days_before_send_notification_for_generation_plan_insurance";

    // ACC-3961
    public static final String URGENT_VISA_FEES = "URGENT_VISA_FEES";

    //Jirra ACC-3049
    public static final String PARAMETER_ANSARI_EXPENSE_CODE= "ansari_expense_code";

    //Jirra ACC-3315
    public static final String PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR = "coo_night_review_time";
    public static final String CREDIT_CARD_STATEMENT_FILE_TAG = "credit_card_statement";
    public static final String INSURANCE_AUDITING_STATEMENT_FILE_TAG = "insurance_auditing_statement";

    // Jira ACC-4505
    public static final String PARAMETER_HOURS_TO_EXPIRED_MATCHING_CREDIT_CARD_WHEN_PARSING_STATEMENT = "hours_to_expired_matching_credit_card_when_parsing_statement";

    // ACC-7894 (V24)
    public static final String PARAMETER_ALREADY_MATCHED_TRANSACTIONS_BEFORE_X_DAYS_THRESHOLD = "already_matched_transactions_before_x_days_threshold";

    //ACC-3198
    public static final String PARAMETER_DD_CANCELLATION_FAILED_DUE_RPA_PROCESS_EMAIL = "dd_cancellation_failed_due_rpa_process_email";
    //ACC-3198
    public static final String PARAMETER_EXPENSE_REQUEST_REDIRECT_AFTER_ACTION_PAGE = "ex_redirect_after_action_page";

    // ACC-7580
    public static final String PARAMETER_RISK_DOCUMENT_MGMT_PAGE = "risk_document_mgmt_page";

    public static final String PARAMETER_EXPENSE_REJECTED_FROM_EMAIL_PAGE = "ex_approval_from_email_page";

    //Jirra ACC-3742 #7
    public static final String PARAMETER_PAYTAB_PAYMENT_REDIRECT_AFTER_ACTION_PAGE = "paytab_payment_redirect_after_action_page";

    // ACC-8308
    public static final String PARAMETER_NGPT_REFUND_PURPOSES_FOR_CC_CONTRACT = "ngpt_refund_purposes_for_cc_contract";
    public static final String PARAMETER_NGPT_REFUND_PURPOSES_FOR_MV_CONTRACT = "ngpt_refund_purposes_for_mv_contract";

    public static final String PARAMETER_CONTRACT_REACTIVATED_WHILE_SWITCH_NATIONALITY_FLOW_RUNNING_EMAIL_RECIPIENTS = "contract_reactivated_while_switch_nationality_flow_running_email_recipients";

    //ACC-3211
    public static final String PARAMETER_PAYMENT_EXPIRY_FLOW_EOM_NUM_OF_DAYS = "payment_expiry_flow_eom_num_of_days";

    //ACC-3211
    public static final String PARAMETER_SWITCHING_BANK_ACCOUNT_EMAIL = "switching_bank_account_email";

    public static final String RECEIPTS_EMAIL_UPON_VIP_CLIENT_RECEIVE_MESSAGE = "receipts_email_upon_vip_client_receive_message";

    //ACC-3296
    public static final String PARAMETER_DD_CONFIGURATION_DEFAULT_NBR_OF_GENERATED_DDS = "dd_configuration_default_nbr_of_generated_dds";
    public static final String PARAMETER_DD_CONFIGURATION_DEFAULT_DDA_TIME_FRAME = "dd_configuration_default_dda_time_frame";
    public static final String PARAMETER_DD_CONFIGURATION_DEFAULT_CREATE_MANUAL_FOR_DDB = "dd_configuration_default_create_manual_for_ddb";
    public static final String PARAMETER_DD_CONFIGURATION_DEFAULT_INCLUDE_MANUAL_IN_DDB_FLOW = "dd_configuration_default_include_manual_in_ddb_flow";

    public static final String PARAMETER_MAIDS_CC_CLIENT_CALL = "maids_cc_client_call";
    // ACC-9076
    public static final String PARAMETER_MV_CLIENT_CALL = "mv_client_call";

    // ACC-9532
    public static final String PARAMETER_DEFAULT_CC_AMOUNT_FOR_FILIPINA = "default_cc_amount_for_filipina";
    public static final String PARAMETER_DEFAULT_CC_AMOUNT_FOR_NON_FILIPINA = "default_cc_amount_for_non_filipina";

    public static final String PARAMETER_MAXIMUM_BOUNCED_PAYMENT_PAUSE_TIALS = "maximum_bounced_payment_pause_trials";
    //Jirra ACC-3487
    public static final String PARAMETER_WIRE_TRANSFER_AMOUNT_DIFFERENCE_THRESHOLD = "wire_transfer_amount_difference_threshold";
    //JIRRA ACC-4465
    public static final String PARAMETER_MISSING_TAX_INVOICE_TAB_DEPLOYMENT_DATE = "PARAMETER_MISSING_TAX_INVOICE_TAB_DEPLOYMENT_DATE";

    // ACC-7525
    public static final String PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD = "payment_expiry_flow_before_x_days_paid_end_date_period";

    // ACC-6993
    public static final String PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_TWO_AMOUNT_FOR_MATCH_PTC = "accepted_difference_between_two_amount_for_match_ptc";
    public static final String PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_PRORATED_DISCOUNT_FOR_MATCH_PTC = "accepted_difference_between_prorated_discount_for_match_ptc";

    public static final String ACC_START_ALLOWING_MESSAGE_SENDING_TIME = "acc_start_allowing_message_sending_time";
    public static final String ACC_STOP_ALLOWING_MESSAGE_SENDING_TIME = "acc_stop_allowing_message_sending_time";
    public static final String ACC_SHIFT_FIRST_MESSAGE_SENDING_TIME = "acc_shift_first_message_sending_time";
    public static final String ACC_SHIFT_SECOND_MESSAGE_SENDING_TIME = "acc_shift_second_messaging_send_time";
    public static final String ACC_START_SHIFTING_SECOND_MESSAGE_TIME = "acc_start_shifting_second_message_time";
    public static final String ACC_END_SHIFTING_SECOND_MESSAGE_TIME = "acc_end_shifting_second_message_time";

    public static final String PARAMETER_DIFFERENCE_RESET_TRIAL_BOUNCED_PAYMENT = "difference_reset_trial_bounced_payment";

    public static final String PARAMETER_BANK_STATEMENT_CONFIRMED_TRANSACTION_DIRECT_CALL = "bank_statement_confirmed_transaction_direct_call";
    public static final String PARAMETER_LIMITATION_DD_DURATION = "limitation_dd_duration";

    public static final String PARAMETER_QUEUE_BGT_OF_STATEMENT_PARSING_PROCESSES = "queue_bgt_of_statement_parsing_processes";

    // ACC-9286
    public static final String PARAMETER_PAYMENT_TYPES_TO_BE_REQUIRED_WITHIN_THE_REMINDER_FLOW = "payment_types_to_be_required_within_the_reminder_flow";

    // ACC-7574_ACC-8295
    public static final String PARAMETER_SHOW_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE = "show_note_live_out_in_amend_dds_page";
    public static final String PARAMETER_TEXT_IN_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE = "text_in_note_live_out_in_amend_dds_page";

    // ACC-8776
    public static final String PARAMETER_EXCLUDE_USERS_IDS_FROM_SENDING_PURCHASE_ORDER_REMINDER_EMAIL = "exclude_users_ids_from_sending_purchase_order_reminder_email";

    public static final String PARAMETER_SEND_NOTIFICATION_FOR_FIRST_X_RECEIVED_PAYMENTS = "send_notification_for_first_x_received_payments";
    public static final String PARAMETER_SEND_NOTIFICATION_FOR_PRE_COLLECTED_FIRST_X_RECEIVED_PAYMENTS = "send_notification_for_pre_collected_first_x_received_payments";

    public static final String PARAMETER_SEND_NOTIFICATION_FOR_RECEIVED_PAYMENTS_WAITING_MINUTES = "send_notification_for_received_payments_waiting_minutes";
    public static final String PARAMETER_X_HOUR_AGO_DURING_WHICH_ALL_GENERATED_DD_HIDDEN = "x_hour_ago_during_which_all_generated_dd_hidden";

    // ACC-7321_ACC-8205
    public static final String PARAMETER_ENABLE_MATCHING_THE_DDC_WITH_DD_OR_PAYMENT = "enable_matching_the_ddc_with_dd_or_payment";

    public static final String PARAMETER_ENABLE_FAILED_CAPTURE_RECURRING_PAYMENT = "enable_failed_capture_recurring_payment";


    public static final String PARAMETER_STOP_CLOSE_MAIN_DDC_TODO = "stop_close_main_ddc_todo";
    public static final String PARAMETER_ACC_CREDIT_CARD_TOKENIZATION_ENABLED = "acc_credit_card_tokenization_enabled";

    // ACC-8747
    public static final String PARAMETER_MISSING_TRANSFER_AMOUNT_FOR_CLIENT_ALERT_RECIPIENTS = "missing_transfer_amount_for_client_alert_recipients";
    public static final String PARAMETER_LUGGAGE_COMPENSATION_EXPENSE_CODE = "luggage_Compensation_expense_code";
    public static final String PARAMETER_SPECIFY_BGT_QUEUE_OF_CONFIRM_BANK_STATEMENT_TRANSACTION = "specify_bgt_queue_of_confirm_bank_statement_transaction";

    //ACC-8121
    public static final String PARAMETER_REFUND_DELAY_HOURS = "refund_delay_hours";

    public static final double EPSILON = 0.1;

    // ACC-8912
    public static final String MANAGE_ALL_CASHIER_TODOS_POSITION = "manage_all_cashier_todos_position";

    // MC-28
    public static final String PARAM_INTERVAL_FOR_DUE_PAYMENTS = "interval_for_due_payments";

    public static final String PARAMETER_SEND_VISA_EXPENSE_MISSING_ERP_ALERT_DEPLOYMENT_DATE = "send_visa_expense_missing_erp_alert_deployment_date ";
    public static final String PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS = "visa_expense_missing_erp_alert_recipients ";
    public static final String PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS_AFTER_THREE_DAYS_UNCLOSED = "visa_expense_missing_erp_alert_recipients_after_three_days_unclosed";
    public static final String PARAMETER_VISA_EXPENSE_SAME_REFERENCE_NUMBER_BUT_DIFFERENT_AMOUNTS_ALERT_RECIPIENTS = "visa_expense_same_reference_number_but_different_amounts_alert_recipients";

    private static final Logger logger = Logger.getLogger(AccountingModule.class.getName());

    @Override
    public List<GmailInbox> getMailInboxes() {
        return Arrays.asList(new GmailInbox(
                "<EMAIL>", null,
                Setup.getApplicationContext().getBean(InvoiceAnnexureMailProcessor.class)));
    }

    @Override
    public List<DynamicApi> getDynamicApis() {
        return setupDynamicApis.setup();
    }

    @Override
    public List<JobDefinition> getCustomJobDefinitions() {
        return Arrays.asList(
                new JobDefinition("Bucket Balance Email", "BucketBalanceEmailScheduledJob", BucketBalanceEmailScheduledJob.class, null),
                new JobDefinition("Data Correction and Integrity", "DataCorrectionandIntegrityScheduledJob", DataCorrectionandIntegrityScheduledJob.class, null),
                new JobDefinition("Data Integrity Report", "DataIntegrityReportJob", DataIntegrityReportJob.class, null),
                new JobDefinition("DDs Cancellation", "DDsCancellationScheduledJob", DDsCancellationScheduledJob.class, null),
                new JobDefinition("Oec Amend DDs Job", "OecAmendDDsJob", OecAmendDDsJob.class, null),
                new JobDefinition("Flow Processor Main Job", "FlowProcessorMainJob", FlowProcessorMainJob.class, null),
                new JobDefinition("Pdc Due Date Email", "PdcDueDateEmailScheduledJob", PdcDueDateEmailScheduledJob.class, null),
                //new JobDefinition("PayTab Transaction Job", "PayTabTransactionJob", PayTabTransactionJob.class, null),
                new JobDefinition("Accounting Adjusted End Date Checker", "adjusted_end_date_checker", CheckAdjustedEndDateJobDefinition.class, null),
                new JobDefinition("Purchasing other reminder job", "PurchasingOtherReminderToShockKeeperJob", PurchasingOtherReminderToShockKeeperJob.class, null),
                new JobDefinition("Purchasing init reminder job", "PurchasingInitialReminderToStockKeeperJob", PurchasingInitialReminderToStockKeeperJob.class, null),
                new JobDefinition("Purchasing Trigger job", "PurchasingTriggerJob", PurchasingTriggerJob.class, null),
                new JobDefinition("Maids In Accommodation Statistics Job", "MaidInAccommodationStatisticsJob", MaidInAccommodationStatisticsJob.class, null),
                new JobDefinition("Referral Discount", "ReferralDiscountScheduledJob", ReferralDiscountScheduledJob.class, null),
                new JobDefinition("Tenancy Contracts Email", "TenancyContractsEmailScheduledJob", TenancyContractsEmailScheduledJob.class, null),
                new JobDefinition("DDs and Collection Compliance", "DdsAndCollectionComplianceJob", DdsAndCollectionComplianceJob.class, null),
                new JobDefinition("IPAM flow sending messages", "AfterCashFlowMessagesJob", AfterCashFlowMessagesJob.class, null),
                new JobDefinition("Housemaid Failed Medical Check Job", "MaidVisaPreCollectedSalaryMedicalCheckJob", MaidVisaPreCollectedSalaryMedicalCheckJob.class, null),
                new JobDefinition("send the scheduled messages of dd messaging", "DirectDebitMessagesSendingJob", DirectDebitMessagesSendingJob.class, null),
                new JobDefinition("checks if client provided new dd info after 24 hours", "DirectDebitWaitingClientReSignJob", DirectDebitWaitingClientReSignJob.class, null),
                new JobDefinition("Generate Bounced Payments Collection File", "GenerateBouncedPaymentsCollectionFileJob", GenerateBouncedPaymentsCollectionFileJob.class, null),
                new JobDefinition("Clean Unnecessary Data From Database", "CleanUnnecessaryDataJob", CleanUnnecessaryDataJob.class, null),
                new JobDefinition("Generate One Time Manual Payment Collection File", "GenerateOneTimeManualPaymentCollectionFileJob", GenerateOneTimeManualPaymentCollectionFileJob.class, null),
                new JobDefinition("Bounced Payments Without Approved DDF", "BouncedPaymentsWithoutApprovedDDFSC", BouncedPaymentsWithoutApprovedDDFSC.class, null),
                new JobDefinition("DirectDebitRejectionRetryJob", "DirectDebitRejectionRetryJob", DirectDebitRejectionRetryJob.class, null),
                new JobDefinition("BouncingPaymentMessageChequesJob", "BouncingPaymentChequesJob", BouncingPaymentChequesJob.class, null),
                new JobDefinition("Risk Documents Management Main Job", "RiskDocumentsManagementJob", RiskDocumentsManagementJob.class, null),
                new JobDefinition("DD Expiry Job", "DdExpiredJob", DdExpiredJob.class, null),
                new JobDefinition("Risk Documents Management Main Job", "RiskDocumentsManagementJob", RiskDocumentsManagementJob.class, null),
                new JobDefinition("Update information from salesBinder Api", "sales_binder_update_data_job", SalesBinderUpdateDataJob.class, null),
                new JobDefinition("increase DD reminder in case client didn't sign", "IncompleteDDInfoSJ", IncompleteDDInfoSJ.class, null),
                new JobDefinition("add accountant todo for pending client refund", "add_accountant_todo_for_pending_client_refund", ClientRefundAccountantTodoCreation.class, null),
                new JobDefinition("Switching Nationality to Filipino SJ", "SwitchingNationalityToFilipinoSJ", SwitchingNationalityToFilipinoSJ.class, null),
                new JobDefinition("Switching Nationality from Filipino SJ", "SwitchingNationalityFromFilipinoSJ", SwitchingNationalityFromFilipinoSJ.class, null),
                new JobDefinition("Switching Nationality to Filipino Mail SJ", "SwitchingNationalityToFilipinoEmailSJ", SwitchingNationalityToFilipinoEmailSJ.class, null),
                new JobDefinition("Switching Nationality from Filipino Mail SJ", "SwitchingNationalityFromFilipinoEmailSJ", SwitchingNationalityFromFilipinoEmailSJ.class, null),
                new JobDefinition("Notify user about pending for cancellation forms SJ", "DDPendingForCancellationSJ", DDPendingForCancellationSJ.class, null),
                new JobDefinition("Client Paying Via Credit Card Eligible Refund Email Job","ClientPayingViaCreditCardEligibleRefundEmailJob", ClientPayingViaCreditCardEligibleRefundEmailJob.class, null),
                new JobDefinition("Extension Flow Sending Messages", "ExtensionFlowJob", ExtensionFlowJob.class, null),
                new JobDefinition("Client Paying Credit Card Flow Sending Messages","ClientPayingViaCreditCardJob", ClientPayingViaCreditCardJob.class, null),
                new JobDefinition("COO Approvals Email SJ", "CooApprovalsEmailSJ", CooApprovalsEmailSJ.class, null),
                new JobDefinition("Job that send email to Coo for pending bank transfer confirmation", "BankTransferConfirmationEmailJob", BankTransferConfirmationEmailJob.class, null),
                new JobDefinition("Email Notification for Requestors", "ClientRefundRequestorsReportSJ", ClientRefundRequestorsReportSJ.class, null),
                new JobDefinition("Job that sends an email upon client refunds modified", "ReportOnClientRefundsModifiedJob", ReportOnClientRefundsModifiedJob.class, null),
                new JobDefinition("Job that create cancellation todo upon payment deleted", "CreateCancellationToDoUponPaymentDeleted", CreateCancellationToDoUponPaymentDeletedJob.class, null),
                new JobDefinition("Job that sends emails to remind the managers to approve expense todo", "SendReminderToApproveExpenseTodo", SendReminderToApproveExpenseTodo.class, null),
                new JobDefinition("Accounting module main job","AccountingModuleMainJob", AccountingModuleMainJob.class, null),
                new JobDefinition("CooExpenseNotificationDismissJob", "coo_expense_notification_dismiss_job", CooExpenseNotificationDismissJob.class, null),
                new JobDefinition("Job that checking contracts schedule termination date", "ContractScheduledTerminationJob", ContractScheduledTerminationJob.class, null),
                // ACC-3856
                new JobDefinition("Credit Card PRE_PDP payments scheduly job", "CreditCardPrePDPPaymentsEmailScheduledJob", CreditCardPrePDPPaymentsEmailScheduledJob.class, null),
                new JobDefinition("Closing Direct Debit Rejection Todos Job", "ClosingDirectDebitRejectionTodosJob", ClosingDirectDebitRejectionTodosJob.class, null),
                new JobDefinition("Ongoing Collection Flow Job", "CollectionFlowLogJob", CollectionFlowLogJob.class, null),
                new JobDefinition("The job of send one month agreement flow Messages", "OneMonthAgreementFlowMessagesJob", OneMonthAgreementFlowMessagesJob.class, null),
                // CMA-154
                new JobDefinition("Expected Wire Transfer For Replacement Job", "ExpectedWireTransferForReplacementJob", ExpectedWireTransferForReplacementJob.class, null),
                // ACC-9005
                new JobDefinition("Send UnMatched DDs Cancellation Report Job", "SendUnMatchedDDsCancellationReportJob", SendUnMatchedDDsCancellationReportJob.class, null),
                new JobDefinition("Bucket Auto Replenishment Job", "BucketAutoReplenishmentJob", BucketAutoReplenishmentJob.class, null),
                new JobDefinition("The job of send reminders for missing bank info messages", "IncompleteDirectDebitFlowJob", IncompleteDirectDebitFlowJob.class, null),
                new JobDefinition("The job of send reminders for online credit card payments Messages", "UnpaidOnlineCreditCardPaymentJob", UnpaidOnlineCreditCardPaymentJob.class, null),
                new JobDefinition("Job that generates DD from postponed plans", "DirectDebitGenerationPlanJob", DirectDebitGenerationPlanJob.class, null),
                new JobDefinition("Job that generates reports containing all the non-monthly postponed DDs that will be triggered next month", "NonMonthlyGenerationPlanMonthlyReportJob", NonMonthlyGenerationPlanMonthlyReportJob.class, null),
                new JobDefinition("Job that send report in email for payments collected after termination", "SendPaymentCollectedAfterTerminationAlertJob", SendPaymentCollectedAfterTerminationAlertJob.class, null),
                new JobDefinition("Job that sends an email upon approval of two DDFs within 24 hours", "DirectDebitFileDoubleApprove", DirectDebitFileDoubleApprove.class, null),
                new JobDefinition("Job to disable CC app accounting notifications", "DisableAccountingNotificationsJob", DisableAccountingNotificationsJob.class, null),
                new JobDefinition("Job to send email about CC App clients activity", "CcAppTrackingJob", CcAppTrackingJob.class, null),
                new JobDefinition("Job that calculate bucket amount", "CalculateBucketBalanceAmountJob", CalculateBucketBalanceAmountJob.class, null),
                new JobDefinition("Job that process all hidden DDs from x hours ago", "DDsHiddenProcessingJob", DDsHiddenProcessingJob.class, null),
                new JobDefinition("Job that informed the clients has cancelled contracts with active DDs due to overdue payments", "CancelledContractWithActiveDDJob", CancelledContractWithActiveDDJob.class, null),
                new JobDefinition("Job that calculate correct balance based on transaction", "CalculateCorrectBalanceBasedTransactionJob", CalculateCorrectBalanceBasedTransactionJob.class, null),
                new JobDefinition("The job of sending an email of payment must be collected manually", "PaymentMustCollectedManuallyReminderJob", PaymentMustCollectedManuallyReminderJob.class, null),
                new JobDefinition("A job that sends an email upon ERP creating a DDA with a start date that has passed", "NewDDACreatedStartDatePassedReportJob", NewDDACreatedStartDatePassedReportJob.class, null),
                new JobDefinition("Job that create DDs for payment expiry flow", "GenerateDDsForPaymentExpiryFlowJob", GenerateDDsForPaymentExpiryFlowJob.class, null),
                new JobDefinition("Job that check if not added upgrading replacement", "CheckReplacementFailedAfterUpgradingFeeJob", CheckReplacementFailedAfterUpgradingFeeJob.class, null),
                new JobDefinition("Job that automatically send DD to the bank", "SendDDToBankJob", SendDDToBankJob.class, null),
                //ACC-8397
                new JobDefinition("Job that send email with failed background tasks per day", "SendingEmailsForFailedBackgroundTasksJob", SendingEmailsForFailedBackgroundTasksJob.class, null),
                new JobDefinition("Job that send alerts for missing visa statement transaction per day", "SendMissingVisaStatementTransactionAlertsJob", SendMissingVisaStatementTransactionAlertsJob.class, null)
        );
    }

    @Override
    public String getName() {
        return "Accounting Module";
    }

    @Override
    public String getCode() {
        return "accounting";
    }

    @Override
    public List<ApiPermission> getCustomApiPermissions() {
        return null;
    }

    @Override
    public List<Picklist> getCustomPicklists() {

        return Arrays.asList(new Picklist(PICKLIST_NON_CLIENT_PDC_BANK_ISSUER,
                        PICKLIST_NON_CLIENT_PDC_BANK_ISSUER,
                        new String[]{"Mustaqeem ADIB", "Mustaqeem ADCB", "Magna ADIB", "QNB"}
                ),
                new Picklist(PICKLIST_PNL_EXPENSE_TYPE,
                        PICKLIST_PNL_EXPENSE_TYPE, new String[]{"New Hire", "Sales"}),
                new Picklist(PICKLIST_SERVICE_TYPE,
                        "Service Type", new String[]{"Landline", "Internet", "3G", "Mobile Number"}),
                new Picklist(PICKLIST_PAYMENT_METHOD,
                        "Payment Method", new String[]{"Etisalat B2B adibkhal", "Etisalat B2B magna.media", "DU", "DEWA", "Jad Etisalat account", "Sajeev Etisalat account"}),
                new Picklist(PICKLIST_MANAGER_NOTE_MANGER_CODE,
                        PICKLIST_MANAGER_NOTE_MANGER_CODE, new String[]{"Rizh", "Adeeb", "Jad", "Rawan", "Samer D", "Riad", "Malek"}) //        ,
                //                new Picklist(PICKLIST_MANAGER_NOTE_TYPE_CODE,
                //                PICKLIST_MANAGER_NOTE_TYPE_CODE,
                //                new String[]{"Addition", "Deduction", "Penality Deduction", "Extra Shit"}
                //        )
                ,
                new Picklist(
                        PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE,
                        new String[]{"Cash Advance", "Transportation Fare Reimbursement", "Salary Dispute", "Cover Deduction Limit",
                                "Renewal Bonus", "Luggage compensation", "Bonus", "Recommendation from client", "housemaid refund",
                                "Cover Deductions Limits", "Cover Negative Salary"}),
                new Picklist(PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE,
                        PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE,
                        new String[]{"Salary Dispute", "Transportation fare repayment", "Return Food/Housing allowance", "Food Allowance paid in cash",
                                "Absence / non working days", "Unpaid vacation", "Postponed Deductions", "Converting Maid To MaidVISA", "Repeat EID Deduction",
                                "Self Replacement", "Faulty Replacement"}
                ),
                new Picklist(PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        PICKLIST_HOUSEMAID_ANNUAL_VACATION_TYPE_CODE,
                        new String[]{"Pre-Paid Vacation", "Vacation Airfare", "Working Vacation"}
                ),
                new Picklist(PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE,
                        PICKLIST_OFFICESTAFF_SALARY_PAYMENT_METHOD_CODE,
                        new String[]{"Cash", "C3", "Bank Transfer", "Ansari Trasfer", "Ansari WPS", "QNB WPS", "Western Union",
                                "Manual/Adeeb", "Local Transer"}
                ),
                new Picklist(
                        PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                        PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                        new String[]{PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DAY_TO_DAY_EXTENTSION,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DALIY_ACCOMMODATION_FEES,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DISCOUNT,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MAID_PASSPORT,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_PAID_THE_CLIENT_REFUND,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_ACCOMMODATION,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_SERVICE_CHARGE,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_EMPLOYING_EX_DH,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_MONTHLY_PAYMENT,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_BOUNCED_CHEQUE,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_DEPOSIT,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_AGENCY_FEE,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_VACATION_NOT_TAKEN,
                                PICKLISTITEM_PAYMENT_TYPE_OF_PAYMENT_UPGRADING_NATIONALITY}
                ),
                new Picklist(PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE,
                        PICKLIST_OFFICESTAFF_SALARY_TRANSFER_DESTINATION_CODE,
                        new String[]{"Persiaran Serdang Perdana 43300 Petaling,Selangor Malaysia (near south city)",
                                "First bank, Mogaji Aare Close Ring Road, oluyole opposite sumal foods Ibadan 200212 (town planning - branch)",
                                "Malaysia - Kuala Lumpur - TML remittance center", "Malaysia, Sabah, Kota Kinabalu, Centre Point, TML Remittance Center",
                                "Germany", "Jordan", "Sharjah", "Turkey", "Philippines", "Lebanon", "Syria - Damascus - Rikn Aldeen",
                                "Syria - Damascus - Kafar Sousah - next to Cham City Center", "Syria - Damascus - Qudssaya Suburb",
                                "Syria - Damascus - Al Hariqah,Syria - Damascus - Al Zbeltani", "Syria - Damascus - Sahet Arnos",
                                "Syria - Damascus - Sahet Al Muhafaza", "Syria - Damascus - Near maysat square - Hojra w jawazat street",
                                "Syria - Damascus, Jdaydet Artooz", "Syria - Damascus - Victoria bridge - Behind Samiramis Hotel",
                                "Syria - Damuscus - Baghdad Street - beside New Horizons Institute", "Syria - Damascus - Mazzeh",
                                "Syria - Damascus - Almuhajereen", "Syria - Damascus - Al Sha'alan", "Syria - Damascus Jaramana", "Syria - Damascus - Dummar - 7th island",
                                "Syria - Damascus City", "Syria - Rif Dimashq - Deir Atiyah", "Syria - Rif Dimashq - Alkeswah", "Syria - Homs - Al Hwash",
                                "Syria - Homs - ein al ajoz", "Syria - Homs - Marmarita", "Syria - Homs city", "Syria - Tartous - Banyas", "Syria - Tartous - Mashta Al Helo", "Syria - Tartous - safita",
                                "Syria - Tartous City", "Syria - Lattakia - Jableh - Al Jubibat area - The main street", "Syria - Lattakia - Baghdad Street", "Syria - Lattakia - Al Ziraa",
                                "Syria - Lattakia City", "Syria - Aleppo - Bagdad station street", "Syria - Aleppo - Al Slemaneiye Street", "Syria - Aleppo - Al Furqan", "Syria - Hama city",
                                "Syria - Hama - Mysiaf", "Syria - Hama - Mhardeh", "Lama Labbad", "Nojoud Nofal Sneij", "Samer Fateh Dofesh",
                                "Youmna wasel salloum", "Judi Anne Angeles Torres"}
                ),
                new Picklist(PICKLIST_CLIENT_REFUND_LENIENCY_TYPES_CODE,
                        PICKLIST_CLIENT_REFUND_LENIENCY_TYPES_CODE,
                        new String[]{
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Taxi or medical reimbursement",
                                "A Partial refund to clients who cancelled Indefinite agreements / Maid asked to pull out or refused to join, Maid's quality / Bad Experience",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Exceptional case",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Bad experience/days without service",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Damage caused by the maid",
                                "A Partial refund to clients who cancelled Indefinite agreements / Avoid escalating",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Retract cancellation",
                                "A Partial refund to clients who cancelled Indefinite agreements / Exceptional case",
                                "A Partial refund to clients who cancelled Indefinite agreements / Client high expectations",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / BDDs Charges",
                                "A Partial refund to clients who cancelled Indefinite agreements / Shifted to MV client",
                                "Old Contract Adjusted End date discount: Disputes from VAT or Vacation Days or Double Payment",
                                "A Partial refund to clients who cancelled Indefinite agreements / Exceeded maximum number of replacements / Fired clients",
                                "Remove a bad Google review",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Switching to a cheaper nationality",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / G-Review , Testimonial",
                                "A Partial refund to clients who cancelled Indefinite agreements / Maid Hurt one of Family Members",
                                "A Partial refund to clients who cancelled Indefinite agreements / Other , VIP clients",
                                "Compensation for a complaint that inconvenienced an active Maids.cc client / Other , VIP clients",
                                "Waiving Tadbeer Payments in office for Existing MV clients",
                                "Compensation for a complaint that inconvenienced an active MaidVisa client / BDDs Charges",
                                "Compensation for a complaint that inconvenienced an active MaidVisa client / Exceptional case",
                                "Refund 1 month MV service upon traveling for long term vacation",
                                "A Refund to MaidVisa client",
                                "Compensation for a complaint that inconvenienced an active MaidVisa client / Bad experience",
                                "Compensation for a complaint that inconvenienced an active MaidVisa client / Taxi or medical reimbursement",
                                "Compensation for a complaint that inconvenienced an active MaidVisa client / Contract start date dispute",
                                "Uncollected HM's visa expenses from cancelled MV clients - New Model",
                                "Discount for refer a friend on MaidVisa",
                                "Compensation for a complaint that inconvenienced an active MaidVisa client / Retract cancellation"}

                ),
                new Picklist(PICKLIST_HOUSEMIAD_PURPOSE_FOR_MONEY_REQUEST,
                        PICKLIST_HOUSEMIAD_PURPOSE_FOR_MONEY_REQUEST,
                        new String[]{"Housemaid Cash Advance (7 days Available)", "Housemaid advance salary", "Housemaid incentives", "Housemaid medical care & hospital bills",
                                "Housemaid transportation", "Housemaid uniforms & shirts", "Housemaid Complaints Expenses", "Housemaid working vacation", "Housemaid salary dispute",
                                "Housemaid Hotel Expenses", "Housemaid Manager Addition", "Other"}
                ),
                //Jiira ACC-1187
                new Picklist(PICKLIST_HOUSEMIAD_PURPOSE_FOR_MONEY_REQUEST_ADDITIONAL_DESCRIPTION,
                        PICKLIST_HOUSEMIAD_PURPOSE_FOR_MONEY_REQUEST_ADDITIONAL_DESCRIPTION,
                        new String[]{"No show", "Replacement deduction", "Failed interview", "Incorrect Basic Salary"}
                ),
                //Jiira ACC-3933
                new Picklist(PICKLIST_HOUSEMAID_PURPOSE_FOR_BONUS_ADDITIONAL_DESCRIPTION,
                        PICKLIST_HOUSEMAID_PURPOSE_FOR_BONUS_ADDITIONAL_DESCRIPTION,
                        new String[]{"Referral bonus", "VIP bonus", "Signing bonus"}
                ),
                new Picklist(PICKLIST_HOUSEMAID_PURPOSE_FOR_MAIDS_AT_OTHER_EXPENSES_ADDITIONAL_DESCRIPTION,
                        PICKLIST_HOUSEMAID_PURPOSE_FOR_MAIDS_AT_OTHER_EXPENSES_ADDITIONAL_DESCRIPTION,
                        new String[]{"PCR test", "Hotel", "Pocket money & transportation", "Ticket"}
                ),
                new Picklist(PICKLIST_OFFICESTAFF_PURPOSE_FOR_MONEY_REQUEST,
                        PICKLIST_OFFICESTAFF_PURPOSE_FOR_MONEY_REQUEST,
                        new String[]{"Maids.cc Office Staff final settlment", "Maids.cc office staff transportation", "Other"}
                ),
                new Picklist(PICKLIST_CONTRACT_PURPOSE_FOR_MONEY_REQUEST,
                        PICKLIST_CONTRACT_PURPOSE_FOR_MONEY_REQUEST,
                        new String[]{"Contract Cancellation-client's refund", "Other"}
                ),
                new Picklist(PICKLIST_MAIDSCC_PURPOSE_FOR_MONEY_REQUEST,
                        PICKLIST_MAIDSCC_PURPOSE_FOR_MONEY_REQUEST,
                        new String[]{"Maids.cc ads subscriptions", "Maids.cc Recruitment (software - app)", "Maids.cc client's refund (Damage compensation)",
                                "Skype Credits", "Accommodation supplies", "Maids.cc CS & Recruiters equipment (PCs - Phones)", "Other"}
                ),
                new Picklist(PICKLIST_OTHER_PURPOSE_FOR_MONEY_REQUEST,
                        PICKLIST_OTHER_PURPOSE_FOR_MONEY_REQUEST,
                        new String[]{"Other", "Bucket Refill"}
                ),
                new Picklist(PICKLIST_HOUSEMIAD_PAYROLL_TYPE,
                        "Payroll Type",
                        new String[]{"VIP", "Normal"}
                ),
                //Jira 1611
                new Picklist(PICKLIST_BOUNCED_PAYMENT_STATUS,
                        "Bounced Payment Status",
                        new String[]{"Has E-signature and Manual DD",
                                "Has E-signature and no Manual DD",
                                "Has no E-signature",
                                "Pays through Cheque",
                                "Bounced payment received",
                                "Bounced payment received - not including worker salary"
                        }
                ),
                new Picklist(PICKLIST_Phone_Employee_Holder,
                        "Phone Employee Holder",
                        new String[]{"Madonna", "Shie", "Monique", "Jena", "Aldrin", "Delfin", "Rainer", "Arnetta", "Jod", "Phoebe",
                                "Marife", "Kjill", "Batoul", "Rhany", "Veron", "Eros", "Gael", "Ina", "Carla", "Rhiz",
                                "Adeeb", "Mara", "Marina", "Jake", "Joel", "Sajeev", "Abid", "Adil", "Rose", "Malek",
                                "GX", "Julie", "Emma"}
                ),
                new Picklist(PICKLIST_Phone_Usage,
                        "Phone Usage",
                        new String[]{"Maids.ae outbound clients", "Maids.ae outbound cleaners", "Maids.ae inbound clients", "Maids.cc outbound prospects", "Maids.cc outbound housemaids",
                                "Maids.cc inbound clients", "Storage outbound clients", "Storage inbound clients", "Data entry phone", "Office Internet",
                                "Office DEWA", "Accommondation Barsha 401 Internet", "Accommondation Barsha 401 DEWA", "Accommondation Barsha 301 Internet", "Accommondation Barsha 301 DEWA",
                                "Accommondation Barsha 505 Internet", "Accommondation Barsha 505 DEWA", "Accommondation Abu Dhabi Internet", "Accommondation Abu Dhabi DEWA", "Jad Apartment Internet",
                                "Jad Apartment DEWA", "Warehouse 84 DEWA", "Warehouse 85 DEWA", "Warehouse 90 DEWA", "Jad",
                                "Adeeb", "Rhiz", "Sajeef", "Abid", "Adil",
                                "GX", "Bank phone number for Adeeb", "Maids.cc Sales inbound", "Maids.cc Sales inbound rollver", "Maids.cc Sales inbound main",
                                "Maids.cc Sales outbound", "Inbound And Outbound", "Malek", "Accommondation Barsha 302 DEWA", "Accommondation Barsha 302 Internet",
                                "UM Sequim - new showroom UMSE"}
                ),
                new Picklist(PICKLIST_OFFICESTAFF_TEAM_CODE,
                        "OfficeStaff Team",
                        new String[]{"Data entry Dubai", "Data entry SYR", "Storage Customer Service Dubai", "Storage warehouse employee", "FTM Data entry SYR", "Consultant Dubai",
                                "Local Employee", "Maids.ae ISRs", "Maids.ae Operators", "Maids.cc resolvers", "Qatar drivers", "Data Analyst", "Baristas", "Receptionists",
                                "Online ads handler SYR", "Visa Processing team SYR", "Delighters Philippines", "Doctor SYR", "Floating", "Maid service Dubai", "Maid service non-Dubai",
                                "Maids Interviewer Philippines", "Natural Language processing", "Training Video and content", "Freedom Operator Coordinator", "Programmers", "Accountants",
                                "Non-housemaids staff recruiter", "Freedom operator recruiter"}),
                //Jirra ACC-675
                new Picklist(PICKLIST_SALARY_RULES_COMPONENTS,
                        PICKLIST_SALARY_RULES_COMPONENTS,
                        new String[]{"primarySalary", "overTime", "monthlyLoan", "holiday", "airfareFee"}),
                //Jirra ACC-675
                new Picklist(
                        PICKLIST_TRANSACTION_LICENSE,
                        "Transaction License",
                        new String[]{PICKLIST_TRANSACTION_LICENSE_MUSTAQEEM_ITEM, PICKLIST_TRANSACTION_LICENSE_STORAGE_ITEM}),
                // acc-1611
                new Picklist(
                        PICKLIST_CALL_REASONS_SMS,
                        "Call Reasons for Human Sms",
                        new String[]{PICKLIST_ITEM_CALL_REASONS_SMS_BOUNCED_PAYMENT,
                                PICKLIST_ITEM_CALL_REASONS_SMS_DD_MISSING,
                                PICKLIST_ITEM_CALL_REASONS_SMS_DD_MISSING_WITHOUT_CASH_PAYMENT,
                                PICKLIST_ITEM_CALL_REASONS_SMS_DD_REJECTED}),
                // ACC-2357
                new Picklist(
                        PICKLIST_IBAN_ACCOUNTNAT_REJECTION_REASONS,
                        "Accountant Rejection Reasons for IBAN",
                        new String[]{"The bank account holder IBAN is partially showing. (Unclear or Cropped)",
                                "The bank account holder IBAN is missing.",
                                "Credit/debit card photo has been uploaded instead of the IBAN photo."}),
                // ACC-2357
                new Picklist(
                        PICKLIST_EID_ACCOUNTNAT_REJECTION_REASONS,
                        "Accountant Rejection Reasons for EID",
                        new String[]{"Uploaded the wrong side of the bank account holder Emirates ID.",
                                "The bank account holder Emirates ID is partially showing. (Unclear or Cropped)",
                                "The Emirates ID uploaded does not belong to the bank account holder."}),
                // ACC-2357
                new Picklist(
                        PICKLIST_ACCOUNT_NAME_ACCOUNTNAT_REJECTION_REASONS,
                        "Accountant Rejection Reasons for Account Name",
                        new String[]{"The bank account holder name is partially showing. (Unclear or cropped)",
                                "The bank account holder name is missing."}),
                // ACC-2357
                new Picklist(
                        PICKLIST_KNOWN_DD_REJECTION_REASONS,
                        "DD, Known Rejectio Reasons",
                        new String[]{"DUPLICATE COMBINATION OF DARDR (07+08+11+12+17)..IT WAS ALREADY PROCESSED EARLIER.",
                                "ENTITY CANNOT BE REGISTER AS A PAYING BANK.",
                                "INVALID CUSTOMER ID FOR GIVEN CUSTOMER TYPE.",
                                "INVALID FILE FORMAT.ENSURE THAT NO EXTRA COMMA OR EXTRA CR OR LF IS USED IN FILE..CONTACT <EMAIL> .ATTACH NAK/MESSAGE FILE.",
                                "INVALID PAYING BANK ID.000001-DARDR-12-INVALID ROUTING NUMBER.",
                                "RR01-Signature Incorrect",
                                "RR02-Signature Missing / Additional Signature(s) Required",
                                "RR04-Invalid Account",
                                "RR05-Title Mismatch",
                                "RR14-Signatory(ies) not authorized for the Mandate Amount",
                                "RR18-DDA Not Accepted by Paying Bank - Contact Your Bank",
                                "RR54-Rejected by Originator",
                                "RR55-Unable to obtain Originator confirmation",
                                "RR90-Compliance Issues",
                                "RR91-Scanned DDA Unusable",
                                "RR92-Details mismatch with Scanned DDA"}),
                //ACC-2826
                new Picklist(
                        PICKLIST_ACCOUNTANT_TODO_TYPES,
                        "Bank Transfer Types",
                        new String[]{
                                PICKLIST_CLIENT_REFUND_ACCOUNTANT_TODO_TYPE,
                                PICKLIST_OFFICE_STAFF_SALARY_ACCOUNTANT_TODO_TYPE,
                                PICKLIST_WPS_TRANSFER_ACCOUNTANT_TODO_TYPE,
                                PICKLIST_INTERNATIONAL_SALARIES_TRANSFER_ACCOUNTANT_TODO_TYPE,
                                PICKLIST_SALARY_LOCAL_TRANSFER_ACCOUNTANT_TODO_TYPE,
                                PICKLIST_PAY_PENSION_ACCOUNTANT_TODO_TYPE
                        }),
                //ACC-2903
                new Picklist(
                        PICKLIST_PARTIAL_REFUNDS_FOR_CANCELLATION_PAYMENT_METHOD,
                        "Partial Refund For Cancellation Payments Method",
                        new String[]{
                                PICKLIST_PARTIAL_REFUNDS_FOR_CANCELLATION_MONTHLY_PAYMENT,
                                PICKLIST_PARTIAL_REFUNDS_FOR_CANCELLATION_MINISTRY_RATE
                        }),
                new Picklist(
                        PICKLIST_ABSCONDER_MAID_REFUND_OPTIONS,
                        "Absconder Maid Refund Options",
                        new String[]{
                                "Refund only maid's pending salary",
                                "Refund the client's payment"
                        }
                ),
                new Picklist(
                        PICKLIST_CATEGORY_ORDER_CYCLE,
                        "Order cycle for categories",
                        new String[]{
                                OrderCycle.MONTHLY.getName(),
                                OrderCycle.WEEKLY.getName(),
                                OrderCycle.ONE_TIME.getName(),
                        }),
                new Picklist(
                        ITEM_MEASURE_OF_CONSUMPTION,
                        "Items Measure Of Consumption",
                        new String[]{
                                ItemMeasureOfConsumption.NONE.toString(),
                                ItemMeasureOfConsumption.PER_NUMBER_OF_MAIDS_IN_ACCOMMODATION.toString(),
                                ItemMeasureOfConsumption.PER_NUMBER_OF_ASSIGNED_ROOMS.toString(),
                                ItemMeasureOfConsumption.PER_NUMBER_OF_NEWLY_JOINED_MAIDS.toString(),
                                ItemMeasureOfConsumption.PER_NUMBER_OF_SICK_MAIDS.toString(),
                                ItemMeasureOfConsumption.PER_NUMBER_OF_MAIDS_WHO_WENT_WITH_CLIENT_FROM_ACCOMMODATION.toString(),
                                ItemMeasureOfConsumption.PER_NUMBER_OF_MAIDS_WHO_RETURNED_FROM_CLIENT_TO_ACCOMMODATION.toString(),
                        }),

                //ACC-2913
                new Picklist(
                        EXPENSE_CURRENCY,
                        "Expense Currency",
                        new String[]{
                                EXPENSE_CURRENCY_AED,
                                EXPENSE_CURRENCY_USD,
                                EXPENSE_CURRENCY_EUR,
                                EXPENSE_CURRENCY_QR,
                        }),
                new Picklist(
                        PICKLIST_EXPENSE_REQUESTED_FROM,
                        "Expense Requested From",
                        new String[]{
                                "Show expense in Request Expenses page",
                                "Show expense in Medical Expenses page",
                                "Show expense in Add Payments to Maids page",
                                "Show in Maintenance Request in Request Expense Page",
                                "Show in Add New Payment in Cashier Screen",
                                "Show in Ticketing Expense Screen",
                                "Show in VIP Expense Screen",
                                "Show in One-Time Request"

                        }),
                //Jirra ACC-3692
                new Picklist(
                        PICKLIST_REFUND_PURPOSE_CATEGORY,
                        "Refund Purpose Category",
                        new String[]{
                                "Compensations for active clients",
                                "Full unused monthly payments",
                                "Duplicated payments",
                                "Partial refunds",
                                "Old CC"
                        }),
                new Picklist(
                        MV_PAYMENT_PLAN_OPTIONS,
                        "MV payment plan options",
                        new String[]{
                                "AED 970/month",
                                "AED 10k + 190/month + Insurance",
                                "AED 10k + 160/month + Insurance",
                                "AED 10k + 190/month No Insurance",
                                "AED 10k + 160/month No Insurance",
                                "AED 10k Split + 190/month + Insurance",
                                "AED 10k Split + 160/month + Insurance",
                                "AED 10k Split + 190/month No Insurance",
                                "AED 10k Split + 160/month No Insurance",
                                "AED 590/month",
                                "AED 890/month",
                                "AED 690/month"
                        })
                ,
                new Picklist(PICKLIST_COLLECTION_FLOW_TYPE,
                        PICKLIST_COLLECTION_FLOW_TYPE,
                        new String[]{
                                CollectionFlowLogController.PICKLISTITEM_BOUNCED_PAYMENT_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_DD_REJECTION_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_DATA_ENTRY_REJECTION,
                                CollectionFlowLogController.PICKLISTITEM_SWITCHING_NATIONALITY_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_SWITCHING_BANK_ACCOUNT_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_REFUND_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_IPAM_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_ONLINE_PAYMENTS_REMINDER_FLOW,
                                CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE,
                                CollectionFlowLogController.PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE,
                                CollectionFlowLogController.PICKLISTITEM_INCOMPLETE_FLOW_MISSING_BANK_INFO,
                                CollectionFlowLogController.PICKLISTITEM_EXTENSION_FLOW}

                ),
                new Picklist(
                        CC_PAYMENT_PLAN_OPTIONS,
                        "CC payment plan options",
                        new String[]{
                                "Filipina Monthly",
                                "Filipina Weekly",
                                "African Monthly",
                                "African Weekly",
                                "Ethiopian Weekly",
                                "Ethiopian Monthly"
                        }),
                new Picklist(
                        REFUND_REJECT_REASON,
                        "Refund reject reason",
                        new String[]{
                                "Condition Not Met"
                        }),
                new Picklist(PICKLIST_TENANCY_TYPE_OF_DOCUMENT,
                        PICKLIST_TENANCY_TYPE_OF_DOCUMENT,
                        new String[]{
                                "Tenancy Contracts",
                                "Agreements",
                                "Bank Documents",
                                "Letters",
                                "Operation Permit"
                        })
        );
    }

    @Override
    public List<Parameter> getCustomParameters() {
        return Arrays.asList(
                // Jirra ACC-1862
                new Parameter(ELIGIBLE_VACATION_PAYING_DATE,
                        ELIGIBLE_VACATION_PAYING_DATE,
                        "3",
                        "count of months after renewal maid date to be eligible"),
                new Parameter(PARAMETER_FRONT_END_URL,
                        PARAMETER_FRONT_END_URL,
                        "http://teljoy.io/ng/main.html",
                        "Erp Front End Base URL."),
                new Parameter(YAYA_CHAT_BOT_API,
                        "Yaya chat bot api",
                        "https://www.maid-ae.net/api/test/",
                        "Yaya Bot API Base URL."),
                new Parameter(YAYA_CHAT_BOT_SENDER_API,
                        "Yaya chat bot sender api",
                        "yayasender",
                        "Yaya Bot Sender API Base URL."),
                new Parameter(YAYA_CHAT_BOT_SALARY_DEDUCTIONS_API,
                        "Yaya chat bot salary deductions api",
                        "https://teljoy.io/test/modules/yayabot/web-app/#!/my-salary/deductions?psid=",
                        "Yaya Bot Salary Deduction API Base URL."),
                new Parameter(YAYA_CHAT_BOT_NOT_CONNECTED_MAIDS_API,
                        "Yaya chat bot api for not connected maids",
                        "getconnectedmaids",
                        "Yaya Bot Connected Maids API."),
                new Parameter(YAYA_CHAT_BOT_PaySlipsSender_Sender_API,
                        "Yaya chat bot pay slips sender api",
                        "payslipssender",
                        "Yaya Bot Payslips Sender API."),
                new Parameter(YAYA_CHAT_BOT_PaySlips_Photoes_Server,
                        "Yaya chat bot pay slips photoes server",
                        "http://localhost:8080",
                        "Yaya Bot API Base URL."),
                new Parameter(YAYA_USER,
                        "Yaya chat bot user name",
                        "chatbots",
                        "Yaya Bot User Name."),
                new Parameter(YAYA_PASSWORD,
                        "Yaya chat bot password",
                        "API#Magna$Bot",
                        "Yaya Bot Password."),
                //Jirra ACC-1087
                new Parameter(EXPENSIFY_API,
                        "Expensify Api",
                        "https://integrations.expensify.com/Integration-Server/ExpensifyIntegrations",
                        "Expensify Api."),
                new Parameter(EXPENSIFY_CURRENCY,
                        "Expensify Currency",
                        "USD",
                        "Expensify Currency"),
                new Parameter(EXPENSIFY_APPROVED,
                        "Expensify Approved",
                        "APPROVED",
                        "Expensify Approved KeyWord"),
                new Parameter(EXPENSIFY_POLICY_ID,
                        "Expensify Policy ID",
                        "DF8F9D6CD3862907",
                        "Expensify Policy ID"),
                //Jirra ACC-1135
                new Parameter(EXPENSIFY_CANCELLATION_USER_ID,
                        "Expensify Cancellation User ID",
                        "aa_clientautopay_maids_cc",
                        "Expensify Cancellation User ID"),
                new Parameter(EXPENSIFY_CANCELLATION_PWD,
                        "Expensify Cancellation Password",
                        "08d365e3e431e6f0779a3054734971c9b012494d",
                        "Expensify Cancellation Password"),
                new Parameter(EXPENSIFY_CANCELLATION_EMAIL,
                        "Expensify Cancellation Email",
                        "<EMAIL>",
                        "Expensify Cancellation Email"),
                new Parameter(EXPENSIFY_CANCELLATION_POLICY_ID,
                        "Expensify Cancellation Policy ID",
                        "DF8F9D6CD3862907",
                        "Expensify Cancellation Policy ID"),
                new Parameter(SALESFORCE_APIS_BASE_URL,
                        "Salesforce apis base url",
                        "https://sandbox-maidsae.cs87.force.com/",
                        "Salesforce apis base url"),
                new Parameter(SALESFORCE_APIS_CLEANERS_SALARIES,
                        "Salesforce apis cleaners salaries",
                        "cleaners/services/apexrest/cleanersalariesws",
                        "Salesforce apis cleaners salaries"),
                new Parameter(SALESFORCE_APIS_QATAR_CLEANERS,
                        "Salesforce apis qatar cleaners",
                        "cleaners/services/apexrest/cleanerspayrollws",
                        "Salesforce apis qatar cleaners"),
                new Parameter(PARAMETER_REQUEST_MONEY_EMAIL,
                        PARAMETER_REQUEST_MONEY_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email of Money Requets"),
                new Parameter(PARAMETER_RHIZ_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        null,
                        "Not Active"),
                new Parameter(PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email to send Housemaid Allowanace."),
                new Parameter(PARAMETER_OFFICESTAFF_VACATION_ALLOWANCE_EMAIL,
                        PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email to send OfficeStaff Allowanace."),
                new Parameter(PARAMETER_OFFICESTAFF_WEEKLY_PAYROLL_REPORT_EMAIL,
                        PARAMETER_HOUSEMAID_VACATION_ALLOWANCE_EMAIL,
                        Setup.isTestMode() ? "" : "",
                        "Email to send report of OfficeStaff Weekly Termination Addition"),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_ETHIOPIAN,
                        DEFAULT_MONTHLY_REPAYMENT_ETHIOPIAN,
                        "200",
                        "Default amount of Ethiopian maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_KENYANS,
                        DEFAULT_MONTHLY_REPAYMENT_KENYANS,
                        "200",
                        "Default amount of Kenyan maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_PHILIPPINES,
                        DEFAULT_MONTHLY_REPAYMENT_PHILIPPINES,
                        "500",
                        "Default amount of Philippines maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT_AFRICANS,
                        DEFAULT_MONTHLY_REPAYMENT_AFRICANS,
                        "200",
                        "Default amount of African maids monthly repayment."),
                new Parameter(DEFAULT_MONTHLY_REPAYMENT,
                        DEFAULT_MONTHLY_REPAYMENT,
                        "500",
                        "Default amount of maids monthly repayment."),
                new Parameter(PARAMETER_PNL_Summary_Report_Levels,
                        PARAMETER_PNL_Summary_Report_Levels,
                        "2",
                        "Default amount of maids monthly repayment."),
                new Parameter(PARAMETER_DEDUCTION_LIMIT,
                        PARAMETER_DEDUCTION_LIMIT,
                        "350",
                        "Default amount of maids deductions limit."),
                new Parameter(PARAMETER_FILIPINOES_DEDUCTION_LIMIT,
                        PARAMETER_FILIPINOES_DEDUCTION_LIMIT,
                        "500",
                        "Default amount of maids deductions limit."),
                //
                new Parameter(PARAMETER_MAIDSAT_DEDUCTION_LIMIT,
                        PARAMETER_MAIDSAT_DEDUCTION_LIMIT,
                        "700",
                        "Default amount of maids.at deductions limit."),
                new Parameter(PARAMETER_PAYROLL_JOBS_START,
                        PARAMETER_PAYROLL_JOBS_START,
                        "26",
                        "The start date to call Repayments and Deductions Limit jobs."),
                new Parameter(PARAMETER_PAYROLL_JOBS_END,
                        PARAMETER_PAYROLL_JOBS_END,
                        "5",
                        "The end date to call Repayments and Deductions Limit jobs."),
                new Parameter(PARAMETER_PNL_Summary_Report_Colored,
                        PARAMETER_PNL_Summary_Report_Colored,
                        "1",
                        "A flag to color PNL summary."),
                new Parameter(PARAMETER_BACKEND_BASE_URL,
                        PARAMETER_BACKEND_BASE_URL,
                        "http://teljoy.io:8080",
                        "Erp Back End Base URL."),
                new Parameter(PARAMETER_PAYMENT_MAIL_CSV,
                        "Payment Mails for CSV sending",
                        "<EMAIL>",
                        "Not Active"),
                new Parameter(PARAMETER_TRANSACTION_MAIL_CSV,
                        "Transaction mails for CSV sending",
                        "<EMAIL>",
                        "Not Active"),
                new Parameter(PARAMETER_NO_KIDS_DEDUCTION_ENABLED,
                        "No Kids Deduction Enabled",
                        "1",
                        "Parameter to enable or disable No kids deductions."),
                new Parameter(
                        PARAMETER_DATA_CORRECTION_AND_INTEGRITY_EMAILS,
                        PARAMETER_DATA_CORRECTION_AND_INTEGRITY_EMAILS,
                        "<EMAIL>;<EMAIL>",
                        "Email to send table of wrong data."),
                new Parameter(
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_MSG,
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_MSG,
                        "Dear @maid_name@. You've received a deduction of @amount@ AED. Press @link@ to view your deduction and the reason.",
                        "Body of Notification Email on Housmaid Deduction."),
                new Parameter(
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_SMS,
                        PARAMETER_HOUSEMAID_DEDCUCTION_NOTIFICATION_SMS,
                        "Dear @maid_name@. You got a deduction of @amount@ because @reason_of_deduction@",
                        "Notification SMS on Housmaid Deduction."),
                new Parameter(
                        PARAMETER_PDCS_WITHIN_WEEK_EMAILS,
                        PARAMETER_PDCS_WITHIN_WEEK_EMAILS,
                        "<EMAIL>",
                        "Email To send table of PDCs in limited period."),
                new Parameter(
                        PARAMETER_PDCS_WITHIN_N_DAYS,
                        PARAMETER_PDCS_WITHIN_N_DAYS,
                        "7",
                        "The limited period of PDCs Report."),
                new Parameter(
                        PARAMETER_PDCS_CONTRACT_END_WITHIN_DAY_EMAILS,
                        PARAMETER_PDCS_CONTRACT_END_WITHIN_DAY_EMAILS,
                        "<EMAIL>",
                        "Email To send table of Tenancy Contracts whose end dates within a day."),
                new Parameter(
                        PARAMETER_PDCS_CONTRACT_END_WITHIN_WEEK_EMAILS,
                        PARAMETER_PDCS_CONTRACT_END_WITHIN_WEEK_EMAILS,
                        "<EMAIL>",
                        "Email To send table of Tenancy Contracts whose end dates within a week."),
                new Parameter(
                        PARAMETER_PDCS_CONTRACT_END_WITHIN_MONTH_EMAILS,
                        PARAMETER_PDCS_CONTRACT_END_WITHIN_MONTH_EMAILS,
                        "<EMAIL>",
                        "Email To send table of Tenancy Contracts whose end dates within a month."),
                new Parameter(
                        PARAMETER_DDS_AND_COLLECTION_COMPLIANCE_EMAILS,
                        PARAMETER_DDS_AND_COLLECTION_COMPLIANCE_EMAILS,
                        "<EMAIL>",
                        "Email to send table of DDS and collection compliance."),
                new Parameter(
                        PARAMETER_SEND_REPORT_IN_MIGRATION_API_EMAILS,
                        PARAMETER_SEND_REPORT_IN_MIGRATION_API_EMAILS,
                        "<EMAIL>",
                        "Email to send table content information of Direct Debit Or Flow if Paying via cc."),
                new Parameter(
                        PARAMETER_POPUP_WARNING_MESSAGE_DAYS_IN_IPAM_FLOW,
                        PARAMETER_POPUP_WARNING_MESSAGE_DAYS_IN_IPAM_FLOW,
                        "",
                        "popup warning message days in ipam flow"),
                new Parameter(
                        PARAMETER_FAILED_BACKGROUND_TASKS_WITHIN_DAY_EMAILS,
                        PARAMETER_FAILED_BACKGROUND_TASKS_WITHIN_DAY_EMAILS,
                        "<EMAIL>",
                        "Email to send table of failed background tasks per day."),
                new Parameter(
                        PARAMETER_CONTRACT_EXCLUDED_FROM_ACC8101_BULK_ADD_DDS_EMAILS,
                        PARAMETER_CONTRACT_EXCLUDED_FROM_ACC8101_BULK_ADD_DDS_EMAILS,
                        "<EMAIL>",
                        "Contract excluded from ACC-8101 Bulk add DDs recipients."),
                new Parameter(
                        PARAMETER_PAYROLL_EMAILS,
                        PARAMETER_PAYROLL_EMAILS,
                        "<EMAIL>",
                        "Email To send Payroll file."),
                new Parameter(
                        PARAMETER_NUMBER_OF_DAYS_TO_CALCULATE_REFUND_AMOUNT,
                        PARAMETER_NUMBER_OF_DAYS_TO_CALCULATE_REFUND_AMOUNT,
                        "30",
                        "number of days that i calculate the amount of refund after it."),
                new Parameter(
                        ACC_START_ALLOWING_MESSAGE_SENDING_TIME,
                        ACC_START_ALLOWING_MESSAGE_SENDING_TIME,
                        "08:00:00",
                        "Accounting start allowing message sending time"),
                new Parameter(
                        ACC_STOP_ALLOWING_MESSAGE_SENDING_TIME,
                        ACC_STOP_ALLOWING_MESSAGE_SENDING_TIME,
                        "20:00:00",
                        "Accounting stop allowing message sending time"),
                new Parameter(
                        ACC_SHIFT_FIRST_MESSAGE_SENDING_TIME,
                        ACC_SHIFT_FIRST_MESSAGE_SENDING_TIME,
                        "09:00:00",
                        "Accounting shift first message sending time"),
                new Parameter(
                        ACC_SHIFT_SECOND_MESSAGE_SENDING_TIME,
                        ACC_SHIFT_SECOND_MESSAGE_SENDING_TIME,
                        "17:00:00",
                        "Accounting shift second message sending time"),
                new Parameter(
                        ACC_START_SHIFTING_SECOND_MESSAGE_TIME,
                        ACC_START_SHIFTING_SECOND_MESSAGE_TIME,
                        "08:00:00",
                        "Accounting start shifting second message time"),
                new Parameter(
                        ACC_END_SHIFTING_SECOND_MESSAGE_TIME,
                        ACC_END_SHIFTING_SECOND_MESSAGE_TIME,
                        "13:00:00",
                        "Accounting stop shifting second message time"),
                new Parameter(
                        PARAMETER_LIMITATION_DD_DURATION,
                        PARAMETER_LIMITATION_DD_DURATION,
                        "10",
                        "Limit of DD expiry date value in years"),
                new Parameter(
                        PARAMETER_QUEUE_BGT_OF_STATEMENT_PARSING_PROCESSES,
                        PARAMETER_QUEUE_BGT_OF_STATEMENT_PARSING_PROCESSES,
                        "HeavyOperationsQueue",
                        "Queue BGT of statement parsing processes"),
                new Parameter(
                        PARAMETER_AMWAL_WALLET_AUTO_ADJUST_THRESHOLD,
                        PARAMETER_AMWAL_WALLET_AUTO_ADJUST_THRESHOLD,
                        "2",
                        "Threshold amount for auto-adjusting ERP amount in Amwal Wallet transactions"),
                new Parameter(
                        PARAMETER_NOQODI_WALLET_AUTO_ADJUST_THRESHOLD,
                        PARAMETER_NOQODI_WALLET_AUTO_ADJUST_THRESHOLD,
                        "2",
                        "Threshold amount for auto-adjusting ERP amount in Noqodi Wallet transactions"),
                new Parameter(
                        PARAMETER_SHOW_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE,
                        PARAMETER_SHOW_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE,
                        "true",
                        "show note liveout in amend DDs page"),
                new Parameter(
                        PARAMETER_PAYMENT_TYPES_TO_BE_REQUIRED_WITHIN_THE_REMINDER_FLOW,
                        PARAMETER_PAYMENT_TYPES_TO_BE_REQUIRED_WITHIN_THE_REMINDER_FLOW,
                        "same_day_recruitment_fee",
                        "Payment types to be required within the reminder flow"),
                new Parameter(
                        "pass_sign_dd_api_to_test_sign_dd_page",
                        "pass_sign_dd_api_to_test_sign_dd_page",
                        "false",
                        ""),
                new Parameter(
                        PARAMETER_TEXT_IN_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE,
                        PARAMETER_TEXT_IN_NOTE_LIVE_OUT_IN_AMEND_DDS_PAGE,
                        "The new monthly payment amount includes the government-mandated benefits (vacation, ticket, gratuity, holiday pay, insurance) " +
                                "and taxi as per clause 4 paragraph 9 of the government-regulated flexible package, in addition to the housemaid live-out allowance.",
                        "Text in note liveout in amend DDs page"),
                new Parameter(
                        PARAMETER_SEND_NOTIFICATION_FOR_FIRST_X_RECEIVED_PAYMENTS,
                        PARAMETER_SEND_NOTIFICATION_FOR_FIRST_X_RECEIVED_PAYMENTS,
                        "3",
                        "X is a parameter that represents the first X monthly payments we are going to trigger received payments notification."),

                new Parameter(
                        PARAMETER_SEND_NOTIFICATION_FOR_PRE_COLLECTED_FIRST_X_RECEIVED_PAYMENTS,
                        PARAMETER_SEND_NOTIFICATION_FOR_PRE_COLLECTED_FIRST_X_RECEIVED_PAYMENTS,
                        "2",
                        "X is a parameter that represents the first X monthly payments for pre-collected contracts we are going to trigger received payments notification."),

                new Parameter(
                        PARAMETER_SEND_NOTIFICATION_FOR_RECEIVED_PAYMENTS_WAITING_MINUTES,
                        PARAMETER_SEND_NOTIFICATION_FOR_RECEIVED_PAYMENTS_WAITING_MINUTES,
                        "5",
                        "The waiting time to collect all payments and include them in the same interaction before send received payments notification."),
                new Parameter(
                        PARAMETER_CHECKLIST_EMAILS,
                        PARAMETER_CHECKLIST_EMAILS,
                        "<EMAIL>",
                        "Email To send Checklist file."),
                new Parameter(
                        PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS,
                        PARAMETER_CHECKLIST_EXCEPTIONS_EMAILS,
                        "<EMAIL>",
                        "Email To send Checklist file generation exceptions."),
                new Parameter(
                        PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS,
                        PARAMETER_EMPLOYEES_WITH_NO_EID_EMAILS,
                        "<EMAIL>,<EMAIL>",
                        "Email To send Payroll file."),
                new Parameter(
                        PARAMETER_PAYROLL_GENERATION_EMAILS,
                        PARAMETER_PAYROLL_GENERATION_EMAILS,
                        "<EMAIL>;<EMAIL>",
                        "Email To send Information when the housemaid payslips is being generated."),
                new Parameter(
                        PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS,
                        PARAMETER_PAYROLL_SENDING_TO_FACEBOOK_EMAILS,
                        "<EMAIL>;<EMAIL>",
                        "Email To send Information when the housemaid payslips is being sending."),
                new Parameter(
                        PARAMETER_BUCKET_REPLENISHMENT_ALERT_EXCLUDED_USERS_IDS,
                        PARAMETER_BUCKET_REPLENISHMENT_ALERT_EXCLUDED_USERS_IDS,
                        "",
                        "users ids to be excluded from bucket replenishment alert"),
                new Parameter(
                        PARAMETER_PAYROLL_EXCEPTIONS_EMAILS,
                        PARAMETER_PAYROLL_EXCEPTIONS_EMAILS,
                        "<EMAIL>;<EMAIL>;<EMAIL>",
                        "Email To send payroll generation exceptions."),
                new Parameter(
                        PARAMETER_ADEEB_EMAIL,
                        PARAMETER_ADEEB_EMAIL,
                        "<EMAIL>;<EMAIL>",
                        "Email To send final payroll."),
                new Parameter(
                        PARAMETER_ERROR_EMAIL,
                        PARAMETER_ERROR_EMAIL,
                        "<EMAIL>;<EMAIL>",
                        "Email To send general exceptions."),
                new Parameter(
                        PARAMETER_REPAYMENT_SUMMARY_EMAIL,
                        PARAMETER_REPAYMENT_SUMMARY_EMAIL,
                        "<EMAIL>",
                        "Email To send summary of genratred repayments for both housemaids and OfficeStaffs."),
                new Parameter(
                        PARAMETER_PAYSLIPS_ARE_BEING_GENRATED,
                        PARAMETER_PAYSLIPS_ARE_BEING_GENRATED,
                        "0",
                        "Parameter to indicate if Payslips generating job is running or not."),
                new Parameter(
                        PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING,
                        PARAMETER_PAYSLIPS_MESSENGER_JOB_IS_RUNNING,
                        "0",
                        "Parameter to indicate if Payslips sending job is running or not."),
                new Parameter(
                        PARAMETER_CHANGE_PAYMENT_STATUS_ADMIN,
                        PARAMETER_CHANGE_PAYMENT_STATUS_ADMIN,
                        "change_payment_status",
                        "Position code of changing payments status admin."),
                new Parameter(
                        PARAMETER_CHANGE_TRANSACTIONS_DATES_ADMIN_POSITION,
                        PARAMETER_CHANGE_TRANSACTIONS_DATES_ADMIN_POSITION,
                        "change_transactions_dates",
                        "Position code of changing transactions dates admin."),
                new Parameter(
                        PARAMETER_CHANGE_TRANSACTIONS_DAY_LIMIT,
                        PARAMETER_CHANGE_TRANSACTIONS_DAY_LIMIT,
                        "6",
                        "Day limit of changing transactions dates"),
                new Parameter(
                        PARAMETER_DELETE_PAYMENT_MATCHING_FILE_POSITION,
                        PARAMETER_DELETE_PAYMENT_MATCHING_FILE_POSITION,
                        PARAMETER_DELETE_PAYMENT_MATCHING_FILE_POSITION,
                        "Not Active"),
                new Parameter(
                        PARAMETER_CHANGE_PAYMENT_STATUS_BOUNCED,
                        PARAMETER_CHANGE_PAYMENT_STATUS_BOUNCED,
                        PARAMETER_CHANGE_PAYMENT_STATUS_BOUNCED,
                        "Position code of changing bounced payments status."),
                new Parameter(
                        PARAMETER_TRANSACTIONS_PAGE_CSV_ROWS_LIMIT,
                        PARAMETER_TRANSACTIONS_PAGE_CSV_ROWS_LIMIT,
                        "5000",
                        "Transactions page CSV rows limit"),
                new Parameter(
                        PARAMETER_PAYMENTS_PAGE_CSV_ROWS_LIMIT,
                        PARAMETER_PAYMENTS_PAGE_CSV_ROWS_LIMIT,
                        "5000",
                        "Payments page CSV rows limit"),
                new Parameter(
                        PARAMETER_PAYROLL_ACCOUNTANT_TODO_CSV_ROWS_LIMIT,
                        PARAMETER_PAYROLL_ACCOUNTANT_TODO_CSV_ROWS_LIMIT,
                        "5000",
                        "BankTransferToDo page CSV rows limit"),
                new Parameter(
                        PARAMETER_EXPENSE_REQUEST_TODO_CSV_ROWS_LIMIT,
                        PARAMETER_EXPENSE_REQUEST_TODO_CSV_ROWS_LIMIT,
                        "5000",
                        "Expense Request page CSV rows limit"),
                new Parameter(
                        PARAMETER_EXCLUDE_USERS_IDS_FROM_SENDING_PURCHASE_ORDER_REMINDER_EMAIL,
                        PARAMETER_EXCLUDE_USERS_IDS_FROM_SENDING_PURCHASE_ORDER_REMINDER_EMAIL,
                        "",
                        "exclude users ids from sending purchase order reminder email"),
                new Parameter(
                        ACCOUNTING_ADMIN,
                        ACCOUNTING_ADMIN,
                        ACCOUNTING_ADMIN,
                        "Position code of accounting admin."),
                new Parameter(
                        PARAMETER_LOCK_PAYMENTS_ENABLED,
                        PARAMETER_LOCK_PAYMENTS_ENABLED,
                        "1",
                        "Parameter to indicate if payments updating is locked or not."),
                new Parameter(
                        PARAMETER_LOCK_PAYROLL_ENABLED,
                        PARAMETER_LOCK_PAYROLL_ENABLED,
                        "1",
                        "Parameter to indicate if housemaid payroll profile updating is locked or not."),
                new Parameter(
                        PARAMETER_LOCK_PAYROLL_START,
                        PARAMETER_LOCK_PAYROLL_START,
                        "27",
                        "Start date of housemaid payroll profile updating is enabled."),
                new Parameter(
                        PARAMETER_LOCK_PAYROLL_END,
                        PARAMETER_LOCK_PAYROLL_END,
                        "2",
                        "End date of housemaid payroll profile updating is enabled."),
//                new Parameter(
//                        PARAMETER_NO_KIDS_DEDUCTION_AMOUNT,
//                        "No Kids Deduction Amount",
//                        "150"),
                new Parameter(
                        PARAMETER_NO_KIDS_DEDUCTION_RESULT_EMAILS,
                        "No kids deduction result emails",
                        "<EMAIL>;<EMAIL>",
                        "Email to send the result of no kids deductions jobs."),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_ENABLED,
                        "Start Date N Weeks: Policy Enabled",
                        "1",
                        "Parameter to indicate if the job of housemaid start date correction is enabled or not."),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_DURATION,
                        "Start Date N Weeks: Policy Duration (In Days)",
                        "21",
                        "Period of the job of housemaid start date correction."),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_CUTOFF_DATE,
                        "Start Date N Weeks: Policy Cutoff Date",
                        "2018-07-15",
                        "Cutoff date of the job of housemaid start date correction."),
                new Parameter(
                        PARAMETER_START_DATE_N_WEEKS_POLICY_EMAILS,
                        "Start Date N Weeks: Policy Emails",
                        "<EMAIL>;<EMAIL>",
                        "Emails of housemaid start date correction."),
                // Jirra ACC-820
                new Parameter(
                        PARAMETER_RENEWAL_PAGE_WHATSAPP_LINK,
                        PARAMETER_RENEWAL_PAGE_WHATSAPP_LINK,
                        "https://wa.me/00971504238603",
                        "Not Active"),
                new Parameter(
                        PARAMETER_CLIENTS_EMAIL_CONFIG_MAID_VISA,
                        PARAMETER_CLIENTS_EMAIL_CONFIG_MAID_VISA,
                        "{\"host\" :\"smtp.gmail.com\", \"name\": \"Maids.cc Visa Services\", \"username\": \"<EMAIL>\", \"password\": \"QRQ3nqc9cb6hbNY\", \"port\": \"587\"}",
                        "Not Active"),
                new Parameter(
                        PARAMETER_CLIENTS_EMAIL_CONFIG,
                        PARAMETER_CLIENTS_EMAIL_CONFIG,
                        "{\"name\": \"TADBEER Al Mustaqeem\", \"username\": \"<EMAIL>\", \"password\": \"GqnV&u9e\"}",
                        "Not Active"),
                //                acc-1962
                new Parameter(
                        PARAMETER_CASH_ADVANCED_LIMIT,
                        "Cash_Advanced_limit",
                        "3",
                        "how many times to get cash advanced"),
                // Jirra ACC-392
                new Parameter(
                        PARAMETER_SEVEN_DAYS_CASH_ADVANCED_DAYS,
                        "Seven days cash advance: days",
                        "7",
                        "Not Active"),
                new Parameter(
                        PARAMETER_SEVEN_DAYS_CASH_ADVANCED_AMOUNT,
                        "Seven days cash advance: amount",
                        "100",
                        "Not Active"),
                // Jirra ACC-836
                new Parameter(
                        PARAMETER_FIRST_PERIOD_DAYS_CASH_ADVANCED_DAYS,
                        "First period cash advance: days",
                        "14",
                        "Not Active"),
                new Parameter(
                        PARAMETER_FIRST_PERIOD_DAYS_CASH_ADVANCED_AMOUNT,
                        "First period cash advance: amount",
                        "100",
                        "Not Active"),
                new Parameter(
                        PARAMETER_lOCAL_STAFF_NATIONALITY_TAG,
                        "Local Nationality Tag",
                        "local_nationality",
                        "Local Nationality Tag (used for Pick List Items)"),
                new Parameter(
                        PARAMETER_HOUSEMAID_RULES_JOB_EMAILS,
                        "Housemaid Rules Job - Emails",
                        "<EMAIL>",
                        "Emails to send exceptions are accurring during Housemaid Rules Job running."),
                new Parameter(
                        PARAMETER_HOUSEMAID_RULES_JOB_ENABLED,
                        "Housemaid Rules Job - Is Enabled?",
                        String.valueOf(Boolean.FALSE),
                        "A flag to indicate if the Housemaid Rules Job Is Enabled ot not."),
                //Jiira ACC-738
                new Parameter(PARAM_ATTENDANCE_DEDUCTION_AMOUNT_FILIPINO,
                        PARAM_ATTENDANCE_DEDUCTION_AMOUNT_FILIPINO,
                        "100",
                        "Deduction amount for Filipino maid absent."),
                new Parameter(PARAM_ATTENDANCE_DEDUCTION_AMOUNT_OTHER,
                        PARAM_ATTENDANCE_DEDUCTION_AMOUNT_OTHER,
                        "50",
                        "Deduction amount for maid absent."),
                //Jiira ACC-837
                new Parameter(PAYMENT_EMAIL_TO_APPROVE_REQUEST_BUSINESS_MANAGER,
                        "Email to Approve Request Business Manager",
                        "Hi @manager@,<br>" +
                                "You have a payment request from: @business_user@<br>" +
                                "Amount: @amount@<br>" +
                                "Type of payment: @Cash_OR_Transfer@<br>" +
                                "Notes: @user_note@<br><br>" +
                                "If you accept/refuse this transfer, please click on this link:<br>" +
                                "<p style=\"color:green; font-size:16px; text-align:center;\">@manger_todo_request@</p>",
                        "Not Active"),
                new Parameter(PAYMENT_EMAIL_TO_CONFIRM_REQUEST_ACCOUNTANT,
                        "Email to Confirm Request Accountant",
                        "Hi @accountant@,<br>" +
                                "You have a payment request from: @business_user@<br>" +
                                "Amount: @amount@<br>" +
                                "Type of payment: @Cash_OR_Transfer@<br>" +
                                "Approved by: @manger@<br>" +
                                "User Notes: @user_note@<br>" +
                                "Manger Notes: @manger_note@<br>" +
                                "If you accept/refuse this transfer, please click on this link:<br>" +
                                "<p style=\"color:green; font-size:16px; text-align:center;\">@accountant_todo_request@</p>",
                        "Not Active"),
                new Parameter(PAYMENT_EMAIL_TO_APPROVE_REQUEST_CFO,
                        "Email to Approve Request CFO",
                        "Hi @cfo@,<br>" +
                                "You have a payment request from: @business_user@<br>" +
                                "Amount: @amount@<br>" +
                                "Type of payment: @Cash_OR_Transfer@<br>" +
                                "Approved by: @manger@<br>" +
                                "User Notes: @user_note@<br>" +
                                "Manger Notes: @manger_note@<br>" +
                                "Please forward your decision to Accounting.",
                        "Not Active"),
                new Parameter(PAYMENT_EMAIL_TO_APPROVE_ACCOUNTANT_REQUEST_TO_CFO,
                        "Email to Approve Accountant Request to CFO",
                        "Hi @cfo@,<br>" +
                                "You have a payment request from: @accountant@<br>" +
                                "Amount: @amount@<br>" +
                                "Type of payment: @Cash_OR_Transfer@<br>" +
                                "Notes: @accountant_note@<br>" +
                                "Please forward your decision to Accounting.",
                        "Not Active"),
                new Parameter(PAYMENT_EMAIL_OF_ACCOUNTANT_NOTES_TO_BUSINESS_MANAGER,
                        "Email to Send Accountant Notes to Business Manager",
                        "Hi @manager@,<br>" +
                                "Accountant sent you the following notes on your request:<br>" +
                                "@accountant_note@",
                        "Not Active"),
                new Parameter(PAYMENT_EMAIL_TO_CLIENT,
                        "Email to Client",
                        "We just sent you @amount@ by Bank Transfer. " +
                                "Please expect to receive the amount to your bank account within 1 business day.",
                        "Email body to send to the Client when the his refund is approved on Expensify."),
                new Parameter(COLLECTION_SMS_MAID_VISA_811,
                        "Colleaction MaidVisa: 8.1.1",
                        "We'll send you the amount of AED @remaining_balance@ within the next 2 days.",
                        "Email body to send to the Client if we owe him money, and we need to transfer him the amount"),
                new Parameter(COLLECTION_SMS_MAID_VISA_812,
                        "Colleaction MaidVisa: 8.1.2",
                        "Unfortunately, the bank didn't process the cancellation of your future payments on time.  "
                                + "You were charged AED @amount@. Don't worry; we will send you that amount within the next 2 days.",
                        "Email body to send to the Client If we receive an amount from the client when he doesn't owe us any amount"),
                new Parameter(COLLECTION_SMS_MAID_VISA_813,
                        "Colleaction MaidVisa: 8.1.3",
                        "You were charged AED @amount@ today. But do not worry, we will transfer you the same amount by @scheduled_termination_date@.",
                        "Email body to send to the Client If we receive an amount from the client when he doesn't owe us any amount"),
                new Parameter(COLLECTION_SMS_MAID_VISA_814,
                        "Colleaction MaidVisa: 8.1.4",
                        "We've just sent you @amount@ by Bank transfer. Please expect to receive the transfer within 2 business days. "
                                + "You can view the proof of transfer here @link@. ",
                        "Email body to send to the Client Whenever the transfer proof is uploaded to ERP"),
                new Parameter(PAYMENT_REQUEST_BUSINESS_MANAGER_TODO,
                        "Payment Request Business Manager Todo",
                        "/accounting/payment-request-flow/business-manager",
                        "Not Active"),
                new Parameter(PAYMENT_REQUEST_ACCOUNTANT_TODO,
                        "Payment Request Accountant Todo",
                        "/accounting/payment-request-flow/payment-request-manager",
                        "Not Active"),
                new Parameter(CFO_USERNAME,
                        "CFO Usename",
                        "adeeb",
                        "Not Active"),
                new Parameter(PAYMENT_REQUEST_PROOF_PAGE,
                        "Payment Request Proof Page",
                        "http://teljoy.io/ng/modules/accounting/payment-request-flow/check-transfer-proof/index.html",
                        "Not Active"),
                new Parameter(MAIDS_AE_Payslips_Sender_API,
                        MAIDS_AE_Payslips_Sender_API,
                        "maidsae_payslipssender",
                        "Yaya bot API to send AE maids payslips."),
                //Jiira ACC-1170
                new Parameter(PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_TITLE,
                        "Title of the Email to Check Petty Cash Balance",
                        "@bucket_name@ is @amount@ AED",
                        "Bucket Balance Email Title."),
                new Parameter(PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_ADDRESS,
                        "Address of the Email to Check Petty Cash Balance",
                        "<EMAIL>",
                        "Bucket Balance Email Address."),
                new Parameter(PARAMETER_BUCKET_BALANCE_ASSISTANT_MAIL_OWNER,
                        "Name of the Assistant for Checking Petty Cash Balance Process",
                        "Mussab",
                        "Bucket Balance Email Owner."),
                new Parameter(PARAMETER_ACC5587_INTERVAL_DAYS_ALLOWED,
                        PARAMETER_ACC5587_INTERVAL_DAYS_ALLOWED,
                        "1",
                        "Interval days allowed between payment date and online card statement date"),
                // jira acc-1651
                new Parameter(PARAMETER_LOAN_HOMELESS_MAID,
                        "loan for homeless maid",
                        "3000",
                        "loan for homeless maid upon a decision by Recruitment manager"),
                new Parameter(PARAMETER_OFFER_CC_PAYMENT_BEFORE_X_TRIALS_REMINDER,
                        PARAMETER_OFFER_CC_PAYMENT_BEFORE_X_TRIALS_REMINDER,
                        "{" +
                                "\"x_number_before_max_trial_or_reminder\" : \"2\"," +
                                "\"x_days_before_dd_start_date_to_scheduled_generate_dds\" : \"5\"," +          // ACC-7421
                                "\"monthly_sms_sentence\" : \" You can settle your payment using a credit or debit card " +
                                    "via the following link: @online_payment_link@ . If you pay with a credit or debit card, " +
                                    "we will switch your payment method to card payments. You may revert to bank payment " +
                                    "forms at any time using your personalized maids.cc app.\"," +
                                "\"monthly_notification_sentence\" : \" You can settle your payment using a credit or " +
                                    "debit card by clicking on the “Pay Now” button below. If you pay with a credit or debit card, " +
                                    "we will switch your payment method to card payments. You may revert to bank payment " +
                                    "forms at any time using your personalized maids.cc app.\"," +
                                "\"non_monthly_sms_sentence\" : \" You can settle your payment using a credit or " +
                                    "debit card via the following link: @online_payment_link@ .\"," +
                                "\"non_monthly_notification_sentence\" : \" You can settle your payment using a credit or " +
                                    "debit card by clicking on the “Pay Now” button below.\"," +
                                "\"cta_label\" : \"Pay Now\"," +
                                "\"excluded_flows\" : \"ClientPaidCashAndNoSignatureProvided, OnlineCreditCardPaymentReminders, ClientsPayingViaCreditCard\"," +
                                "\"enable_feature\" : \"true\"" +
                                "}",
                        "This parameter holds all the information we need to add the CC offer to the end of each ACC message"),
                //Jiira ACC-1135
                new Parameter(PAYMENT_REQUEST_PURPOSE_PARTIAL_REFUND,
                        "Partial Refund",
                        "Partial Refund",
                        "Payment Request Purpose (Partial Refund)"),
                new Parameter(PAYMENT_REQUEST_PURPOSE_FULL_REFUND,
                        "Full Refund",
                        "Full Refund",
                        "Payment Request Purpose (Full Refund)"),
                new Parameter(OEC_AMEND_DDS_JOB_START_DAY,
                        "OEC Amend DDs Job Start Day",
                        "2",
                        "OEC_AMEND_DDS_JOB_START_DAY"),
                new Parameter(DDS_CANCELLATION_SCHEDULED_JOB_START_DAY,
                        "DDS Cancellation Scheduled Job Start Day",
                        "4",
                        "Start the Job Before N Day From the End of the Month"),
                new Parameter(
                        PARAMETER_DDS_CANCELLATION_SCHEDULED_JOB_EXCEPTIONS_EMAILS,
                        PARAMETER_DDS_CANCELLATION_SCHEDULED_JOB_EXCEPTIONS_EMAILS,
                        "<EMAIL>;<EMAIL>",
                        "Email To send dd cancellation scheduled job exceptions."),
                new Parameter(
                        PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT,
                        PARAMETER_WORKING_PUBLIC_HOLIDAYS_LIMIT,
                        "25",
                        "working public holidays limit"),
                new Parameter(
                        PARAMETER_Full_TIME_MAIDS_AT_CONVERTED_TO_HOUSE_MAID_CASH_ADVANCE_AMOUNT,
                        PARAMETER_Full_TIME_MAIDS_AT_CONVERTED_TO_HOUSE_MAID_CASH_ADVANCE_AMOUNT,
                        "3000",
                        "Amount of cash advance when MaidsAt converted to housemaid"),
                new Parameter(
                        PARAMETER_LAST_CALCULATE_BUCKETS_BALANCE,
                        PARAMETER_LAST_CALCULATE_BUCKETS_BALANCE,
                        "",
                        "The date of last calculate buckets balance"),

                //Jirra ACC-1435 from here
                new Parameter(PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES, "Number Of Direct Debit Signatures", "5", "number of direct debit signatures"),
                new Parameter(PARAMETER_NUMBER_OF_DIRECT_DEBIT_SIGNATURES_FOR_CLIENT, "Number Of Direct Debit Signatures For " +
                        "Client", "10", "number of direct debit signatures for client"),
                new Parameter(PARAMETER_DIRECT_DEBIT_DAY_OF_MONTH_EXPIRY_DAY,
                        "Contract Direct Debit Day of Month Expiry Date", "8", "day number which set as expiry date for monthly direct debits"),
                new Parameter(PARAMETER_SUSPEND_DD_MSGS_JOBS, "Suspend direct debit messages job", "true", "parameter that's allow to suspend job for sending sms to complete signature for generated DDs"),
                new Parameter(PARAMETER_ONE_TIME_DD_MONTH_DURATION, "One-Time DD Month Duration", "3", "One-Time DD Months Duration"),
                new Parameter(PARAMETER_MAKE_DD_EXPIRED_CONFIG_JOB, "make dd expired config", "{\"active\":false}", "json configuration parameter to change direct debit status from (pending or confirmed) to expired"),
                new Parameter(SW_EOF, "SW EOF", "6", "number of days before the end of month that determine if replacement maid process needs first payment as ONE_TIME direct debit"),
                new Parameter(PARAMETER_SW_ACCOUNT_DAYS, "PARAMETER_SW_ACCOUNT_DAYS", "5", "number of days before the end of month for switching account"),
                new Parameter(SUSPEND_VAT_JOBS, "suspend vat jobs", "true", "parameter that's allow to suspend VAT job"),
                new Parameter(PARAMETER_VAT_BATCH_SMS_SIZE, "VAT Batch sms size", "200", "daily batch size of SMSs to sent for vat"),
                new Parameter(PARAMETER_VAT_CONTACT_NUMBERS, "VAT Contact Numbers",
                        "{\"whatsapp\" :\"123\", \"phone\": \"456\"}", "json parameter for mobile and phone number for contact about VAT"),
                new Parameter(SUSPEND_VAT_REMINDER_JOBS, "suspend vat reminder jobs", "true", "parameter that's allow to suspend job for sending sms reminders for VAT"),
                new Parameter(PARAMETER_VAT_REMINDER_GREETINGS, "VAT Remider Greetings",
                        "Greetings again from maids-cc", "greetings text in vat reminder"),
                new Parameter(PARAMETER_UPDATE_CPT_CONFIG_RECIPIENT_ADDRESS, "update cpt config recipient address", "", "Recipients email addresses to notified when contract payment term config updated or kept"),
                new Parameter(PARAMETER_CONTRACT_PAYMENTS_RECEIPT_HIGHER_PRICE_BOUND,
                        "Contract Payments Receipt Higher Price Bound", "3500", "higher price bound for contract payment when generating payment receipt files"),
                new Parameter(PARAMETER_IBAN_CHECK_API_KEY, "IBAN Validation API Key",
                        "3c63b9cab271a4be7a1ddd1807fb0c3e", "api key that used to get IBAN info"),
                //Jirra ACC-1435 to here
                // jira acc-1545
                new Parameter(PARAMETER_DEFAULT_DPI_RESOLUTION, "default dpi resolution", "180", "the default dpi resolution"),
                //Jirra ACC-1922
                new Parameter(ACTIVATE_QUERY_LOG_FOR_TRANSACTIONS, "Activate Query Log for Transactions", "1", "Activate Query Log for Transactions"),
                // jira acc-1876
                new Parameter(PARAMETER_DD_IMAGES_FORMAT, "DD Images Formats", "png", "DD Images Formats"),
                new Parameter(PARAMETER_DD_IMAGES_LOSS_VALUE, "DD Images Loss Value", "0", "DD Images Loss Value"),
                //Jirra ACC-2621
                new Parameter(PARAMETER_DEFAULT_DPI_RESOLUTION_CANCELLATION, "default dpi resolution cancellation", "150", "the default dpi resolution"),
                new Parameter(PARAMETER_DD_IMAGES_FORMAT_CANCELLATION, "DD Images Formats Cancellation", "jpg", "DD Images Formats"),
                new Parameter(PARAMETER_DD_IMAGES_LOSS_VALUE_CANCELLATION, "DD Images Loss Value Cancellation", "50", "DD Images Loss Value"),
                //Jirra ACC-1922
                new Parameter(ACTIVATE_QUERY_LOG_FOR_TRANSACTIONS, "Activate Query Log for Transactions", "1", "Activate Query Log for Transactions"),

                // jira acc-1542
                new Parameter(PARAMETER_SMS_DD_INFO,
                        "send pending dd info sms",
                        "true",
                        "send pending dd info sms"),

                new Parameter(PARAMETER_SMS_DD_INFO_REMINDER_1,
                        "send pending dd info sms reminder 1",
                        "true",
                        "send pending dd info sms reminder 1"),

                new Parameter(PARAMETER_SMS_DD_INFO_REMINDER_2,
                        "send pending dd info sms reminder 2",
                        "true",
                        "send pending dd info sms reminder 2"),

                new Parameter(PARAMETER_SMS_DD_INFO_REMINDER_3,
                        "send pending dd info sms reminder 3",
                        "true",
                        "send pending dd info sms reminder 3"),

                new Parameter(PARAMETER_SMS_DD_INFO_REMINDER_4,
                        "send pending dd info sms reminder 4",
                        "true",
                        "send pending dd info sms reminder 4"),

                new Parameter(PARAMETER_SMS_DD_INFO_REMINDER_5,
                        "send pending dd info sms reminder 5",
                        "false",
                        "send pending dd info sms reminder 5"),
                //Jirra ACC-4047
                new Parameter(PARAMETER_COLLECTION_FLOW_DEBUG_MAILS,
                        PARAMETER_COLLECTION_FLOW_DEBUG_MAILS,
                        "<EMAIL>;<EMAIL>",
                        "Collection Flow Job Debug mails"),
                //Jirra ACC-1435 to here
                // acc-1595
                new Parameter(PARAMETER_DD_MAX_MANUAL_TRIALS, "DD rejection Max DD Manual Trials", "5", "the MaxDDManualTrials"),
                new Parameter(PARAMETER_DD_MAX_AUTO_TRIALS, "DD rejection Max Bank Auto Trials", "5", "the MaxDDAutoTrials"),

                new Parameter(PARAMETER_DD_MAX_TRIALS, "PARAMETER_DD_MAX_TRIALS", "3", "Max number of allowed trials for all rejection reasons except signature rejection"),
                new Parameter(PARAMETER_DD_MAX_RE_SIGN_TRIALS, "PARAMETER_DD_MAX_RE_SIGN_TRIALS", "5", "Max number of allowed trials for  signature rejection only"),
                new Parameter(PARAMETER_DD_MAX_SAME_SIGNATURE_TRIALS, "PARAMETER_DD_MAX_SAME_SIGNATURE_TRIALS", "2", "Max allowed trials for same signature set"),

                new Parameter(PARAMETER_REJECTION_FLOW_START_PAYING_VIA_CC_TRIAL,
                        PARAMETER_REJECTION_FLOW_START_PAYING_VIA_CC_TRIAL,
                        "5",
                        "Max allowed trials for rejected signature before start paying via credit card flow"),

                new Parameter(PARAMETER_DD_MAX_REMINDERS, "PARAMETER_DD_MAX_REMINDERS", "0", "Max allowed reminders for each trial of all rejection reasons except signature rejection"),
                new Parameter(PARAMETER_DD_MAX_SIGN_REMINDERS, "PARAMETER_DD_MAX_SIGN_REMINDERS", "0", "Max allowed reminders for each trial of signature rejection only"),
                new Parameter(PARAMETER_DD_REMINDER_PERIOD, "PARAMETER_DD_REMINDER_PERIOD", "0", "Period to wait between each reach reminder for all rejection reasons trials except signature trials"),
                new Parameter(PARAMETER_DD_SIGN_REMINDER_PERIOD, "PARAMETER_DD_SIGN_REMINDER_PERIOD", "0", "Period to wait between each reach reminder for signature trials only"),
                new Parameter(PARAMETER_DD_SIGN_LAST_TRIAL_PERIOD, "PARAMETER_DD_SIGN_LAST_TRIAL_PERIOD", "0", "Period to wait in the last signature trial before scheduling for termination"),
                // jira acc-4603
                new Parameter(PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC, "PARAMETER_BOUNCED_MAX_TRIALS_FOR_CC", "3", "Max number of allowed trials for bounced payments in maids.cc"),
                new Parameter(PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV, "PARAMETER_BOUNCED_MAX_TRIALS_FOR_MV", "6", "Max number of allowed trials for bounced payments in maidsVisa"),

                // Parameters for contract termination days
                new Parameter(PARAMETER_DD_POSTPONE_SCHEDULE_TERMINATION_CC, "CC Termination Days", "3", "Number of days to schedule termination for CC maids when max trials/reminders are reached and PED <= Today"),
                new Parameter(PARAMETER_DD_POSTPONE_SCHEDULE_TERMINATION_MV, "MV Termination Days", "5", "Number of days to schedule termination for MV maids when max trials/reminders are reached and PED <= Today"),
                new Parameter(PARAMETER_DD_POSTPONE_SCHEDULE_TERMINATION_RECIPIENTS, "Termination Alert Recipients", "<EMAIL>", "Email recipients for alerts when contracts are scheduled for termination due to max trials/reminders and PED <= Today"),


                new Parameter(PARAMETER_SCHEDULE_FOR_TERMINATION_AFTER_DAYS, "schedule for termination if client didn't sign ", "23", "after how many days we will schedule the contract for termination if client didn't signed a dd"),


//                new Parameter(PARAMETER_DD_MAX_SIGNATURE_TRIALS, "DD rejection Max Signature Trials", "3", "the MaxSignatureTrials"),
//                new Parameter(PARAMETER_DD_MAX_BANK_INFO_TRIALS, "DD rejection Max Bank Info Trials", "3", "the MaxBankInfoTrials"),
//                new Parameter(PARAMETER_DD_MAX_SAME_SIGNATURE_TRIALS_B, "DD rejection Max Same Signature Trials B", "5", "the MaxSameSignatureTrialsB"),
//                new Parameter(PARAMETER_DD_MAX_SIGNATURE_TRIALS_B, "DD rejection Max Signature Trials B", "5", "the MaxSignatureTrialsB"),
                // acc-1597
//                new Parameter(PARAMETER_DD_MAX_SIGNATURE_TRIALS_B, "DD rejection Max Signature Trials B", "5", "the MaxSignatureTrialsB"),

                new Parameter(PARAMETER_DD_ACTIVATION_BATCH_MAX, "dd activation file batch max size", "24", "the maximum number of records in ddf batch"),
                new Parameter(PARAMETER_DD_SIGNATURE_BATCH_SIZE, "dd signature batch size", "10", "max signature number for one dd"),
                new Parameter(PARAMETER_NOT_PRORATED_CONTRACT_START_DATE, "not prorated contract start date", "24", "not prorated contract start date for dd generation"),
                new Parameter(PARAMETER_MANUAL_DD_BATCH_MAX, "manual dd batch max size", "200", "the maximum number of records in manual ddf batch"),
                new Parameter(PARAMETER_MANUAL_DD_BATCH_INITIAL_INCREMENT, "manual dd batch initial increment", "15", "the initial value for manual dd batch increment"),
                new Parameter(PARAMETER_MANUAL_DD_BATCH_RECORD_INDEX, "manual dd batch record index", "003500", "the record index for manual dd batch"),
                new Parameter(PARAMETER_MANUAL_DD_BATCH_FILE_INDEX, "manual dd batch file index", "NC500", "the file index for manual dd batch records"),
                new Parameter(PARAMETER_APPROVED_MANUAL_DD_EMAIL, "manual dd emails", "<EMAIL>;<EMAIL>", "email address of manual dd batch recipients"),
                new Parameter(PAYMENT_REQUEST_PURPOSE_DUPLICATED_PAYMENT, "Duplicated payment", "Duplicated payment", "Payment Request Purpose (Duplicated payment)"),

                new Parameter(PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP,
                        PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP,
                        "Maid's salary due to missing medical certificate",
                        "Payment Request Purpose (MV salary refund maid excluded due to her not finishing the medical step)"),
                // ACC-9190
                new Parameter(PARAMETER_PCS_FAILED_MEDICAL_PAYMENT_REQUEST_PURPOSE,
                        PARAMETER_PCS_FAILED_MEDICAL_PAYMENT_REQUEST_PURPOSE,
                        "PCS Failed Medical Refund",
                        "Payment Request Purpose (PCS Failed Medical Refund)"),

                // acc-1777
                new Parameter(
                        PARAMETER_ACCOUNTANT_EMAILS,
                        PARAMETER_ACCOUNTANT_EMAILS,
                        "",
                        "Emails To send cheque bounced payment after 10 days."),
                new Parameter(
                        PARAMETER_ACCOUNTANT_EMAILS_CHEQUE_DAYS,
                        PARAMETER_ACCOUNTANT_EMAILS_CHEQUE_DAYS,
                        "10",
                        "days to send Emails of cheque bounced payment"),
                // acc-1777
                new Parameter(
                        PARAMETER_ACCOMMODATION_LOCATION_MESSAGES,
                        PARAMETER_ACCOMMODATION_LOCATION_MESSAGES,
                        "https://goo.gl/maps/KrAkfQQ3CkhsqcHD7",
                        "@accommodation_location@ parameter value in dd messaging setup"),
                new Parameter(PARAMETER_NON_DD_PAYMENT_EMAIL, "non DD payments email", "", "email address of Non DD Payments recipients"),
                new Parameter(
                        PARAMETER_ACCOUNTANT_EMAILS_FOR_BOUNCING_PAYMENTS,
                        PARAMETER_ACCOUNTANT_EMAILS_FOR_BOUNCING_PAYMENTS,
                        "<EMAIL>",
                        "accountant email for bounced payment that future PDC payments different in amount"),
                new Parameter(
                        PARAMETER_DD_DATA_ENTRY_CLERK_EMAIL,
                        PARAMETER_DD_DATA_ENTRY_CLERK_EMAIL,
                        "<EMAIL>",
                        "dd data entry clerk email for newly added DDs waiting for confirmation"),

                new Parameter(
                        PARAMETER_TRANSACTION_POSTING_RULE_NOT_FOUND_EMAIL,
                        PARAMETER_TRANSACTION_POSTING_RULE_NOT_FOUND_EMAIL,
                        "<EMAIL>",
                        "recipients that will receive an email when payment type doesn't linked with any posting rule"),

                new Parameter(
                        PARAMETER_PAYMENT_TYPES_DONT_REQUIRE_POSTING_RULE,
                        PARAMETER_PAYMENT_TYPES_DONT_REQUIRE_POSTING_RULE,
                        "",
                        "payments types that not required posting rules"),

                new Parameter(
                        PARAMETER_MAID_VISA_CONTRACT_START_DATE_FOR_INITIAL_PAYMENTS,
                        PARAMETER_MAID_VISA_CONTRACT_START_DATE_FOR_INITIAL_PAYMENTS,
                        "24",
                        "contract start date for maid visa contracts initial payments generation"),
                new Parameter(
                        PARAMETER_SCHEDULED_DAY_FOR_TERMINATION_IN_EXTENSION_FLOW,
                        PARAMETER_SCHEDULED_DAY_FOR_TERMINATION_IN_EXTENSION_FLOW,
                        "21",
                        "Scheduled day for termination in the extension flow"),
                new Parameter(
                        PARAMETER_SWITCH_MAID_EOF,
                        PARAMETER_SWITCH_MAID_EOF,
                        "5",
                        "num of days before end of month, for Switching Nationalities"),
                //Jirra ACC-1862
                new Parameter(
                        PAYMENT_REQUEST_PAY_VACATION_DAYS,
                        PAYMENT_REQUEST_PAY_VACATION_DAYS,
                        "Pay Vacation Days",
                        "PAYMENT_REQUEST_PAY_VACATION_DAYS"),
                new Parameter(PAYMENT_REQUEST_PURPOSE_SWITCHING_TO_A_CHEAPER_NATIONALITY_REFUND,
                        "Switching to a cheaper nationality Refund",
                        "Switching to a cheaper nationality Refund",
                        "Payment Request Purpose (Switching to a cheaper nationality Refund)"),
                new Parameter(
                        PARAMETER_REGEX_TO_CHECK_FILE_NAME_IN_DDF_DOWNLOAD_BATCH_API,
                        PARAMETER_REGEX_TO_CHECK_FILE_NAME_IN_DDF_DOWNLOAD_BATCH_API,
                        "^NC300\\d{23}$",
                        "Regex to check file name in ddf download batch api"),
                new Parameter(PARAMETER_IN_COMPLETE_DD_MAX_REMINDER,
                        PARAMETER_IN_COMPLETE_DD_MAX_REMINDER,
                        "3",
                        "max reminder for complete Incomplete DD info, then Terminate the contract"),
                new Parameter(PARAMETER_IN_COMPLETE_DD_MAX_TRIALS,
                        PARAMETER_IN_COMPLETE_DD_MAX_TRIALS,
                        "3",
                        "max trials for Incomplete DD info confirmation, then Terminate the contract"),
                new Parameter(PARAMETER_DEFAULT_EXTENSION_DURATION,
                        PARAMETER_DEFAULT_EXTENSION_DURATION,
                        "120",
                        "represents number of months to extend the contract, when replacing the maid"),
                new Parameter(PARAMETER_MIN_PRORATE_AMOUNT,
                        PARAMETER_MIN_PRORATE_AMOUNT,
                        "100",
                        "min amount ti generate ONE_TIME DD when replacing the maid"),
                new Parameter(PARAMETER_NUMBER_OF_DAYS_TO_CREATE_HIDDEN_DDS,
                        PARAMETER_NUMBER_OF_DAYS_TO_CREATE_HIDDEN_DDS,
                        "2",
                        "number of days before end of month to create hiden DDS"),
                new Parameter(PARAMETER_BOUNCING_FLOW_PAUSING_DAYS,
                        "Parameter Bouncing Flow Pausing Days",
                        "3",
                        "CC APP Info - Bouncing Flow Pausing Days"),
                new Parameter(PARAMETER_REFUND_DAYS,
                        "Parameter for refund days",
                        "3",
                        "CC APP Info - i think i'm overcharged"),
                new Parameter(PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_ALL_MONTHS,
                        PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_ALL_MONTHS,
                        "2",
                        "number of days to consider a payment as full refund for all months"),
                new Parameter(PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_FEBRUARY,
                        PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_FEBRUARY,
                        "2",
                        "number of days to consider a payment as full refund for February"),
                new Parameter(PARAMETER_CREDIT_CARD_DEFAULT_MANAGER,
                        PARAMETER_CREDIT_CARD_DEFAULT_MANAGER,
                        "-1",
                        "The default manager for credit card refund"),
                new Parameter(PARAMETER_PNL_CHANGE_CUTOFF_DAY,
                        PARAMETER_PNL_CHANGE_CUTOFF_DAY,
                        "8",
                        "day of month to change Transaction PNL Value Date"),
                new Parameter(PARAMETER_MATCHING_WIRE_BOUNCED_PAYMENT,
                        PARAMETER_MATCHING_WIRE_BOUNCED_PAYMENT,
                        "100",
                        PARAMETER_MATCHING_WIRE_BOUNCED_PAYMENT),
                new Parameter(PARAMETER_GEORGE_EMAIL,
                        PARAMETER_GEORGE_EMAIL,
                        "<EMAIL>",
                        "George Email, for maid.cc contract cancellation retraction"),
                new Parameter(PARAMETER_MARIO_MOBILE_NUMBER,
                        PARAMETER_MARIO_MOBILE_NUMBER,
                        "55555",
                        "Mario Mobile Number, for maid.cc contract cancellation retraction"),
                new Parameter(PARAMETER_GEORGE_EMAIL_NEW_DD_REJECTION_REAOSNS_REPORT,
                        PARAMETER_GEORGE_EMAIL_NEW_DD_REJECTION_REAOSNS_REPORT,
                        "<EMAIL>",
                        "George email, to send new DD Rejection Reasons Report."),
                /*new Parameter(PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL,
                        PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL,
                        "value",
                        "reason to call the creation of expert todo when exceeds the auth rejection trials."),
                new Parameter(PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES,
                        PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES,
                        "The client's direct debit was rejected for three " +
                                "consecutive times due to authorization issue, This could " +
                                "happen when the client decline or ignore the bank's " +
                                "request to validate the direct debit set up. The client will " +
                                "need to ask his/her bank to approve it to avoid any " +
                                "interruption in the service as we will send his/her forms " +
                                "back in two days. This will be the last time we try to " +
                                "contact him/her and submit the payment form before " +
                                "cancellation.",
                        "initial notes to be filled in the expert todo when exceeds the auth rejection trials."),*/
                new Parameter(PARAMETER_DD_FORM_MANAGER_NAME,
                        PARAMETER_DD_FORM_MANAGER_NAME,
                        "Jad Barghout",
                        "Name to be added to the DD Forms"),
                new Parameter(
                        PARAMETER_DDS_ACTIVATION_REPORT_RPA_MAIL,
                        PARAMETER_DDS_ACTIVATION_REPORT_RPA_MAIL,
                        "<EMAIL>",
                        "email receivers for receive dds activation report uploaded by RPA"),
                //Acc-2826
                new Parameter(PARAMETER_CLIENT_REFUND_AGENCY_NAME,
                        PARAMETER_CLIENT_REFUND_AGENCY_NAME,
                        "Ansari",
                        ""),
                new Parameter(PARAMETER_CLIENT_REFUND_PENSION_AUTHORITY_NAME,
                        PARAMETER_CLIENT_REFUND_PENSION_AUTHORITY_NAME,
                        "Ansari",
                        ""),
                new Parameter(PARAMETER_CLIENT_REFUND_AGENCY_EMAIL,
                        PARAMETER_CLIENT_REFUND_AGENCY_EMAIL,
                        "",
                        ""),
                new Parameter(PARAMETER_DDS_ACTIVATION_RPA_MAIL,
                        PARAMETER_DDS_ACTIVATION_RPA_MAIL,
                        "<EMAIL>",
                        "email to send RPA DDs Activation summary to"),
                new Parameter(PARAMETER_DDS_CANCELLATION_RPA_MAIL,
                        PARAMETER_DDS_CANCELLATION_RPA_MAIL,
                        "<EMAIL>",
                        "email to send RPA DDs Cancellation summary to"),
                new Parameter(PARAMETER_AUTO_CONFIRM_DD_RPA_RECORDS,
                        PARAMETER_AUTO_CONFIRM_DD_RPA_RECORDS,
                        "false",
                        "Toggle to enable/disable auto-confirmation of DD records for both RPA and manual uploads"),
                new Parameter(
                        PARAMETER_DDS_CANCELLATION_REPORT_RPA_MAIL,
                        PARAMETER_DDS_CANCELLATION_REPORT_RPA_MAIL,
                        "<EMAIL>",
                        "email receivers for receive dds cancellation report uploaded by RPA"),
                new Parameter(PARAMETER_SIGNING_PAPER_MODE_THRESHOLD_TRIAL,
                        PARAMETER_SIGNING_PAPER_MODE_THRESHOLD_TRIAL,
                        "3",
                        "the number of DD rejection trials after it the contract must be marked as Signing Paper Mode"),
                new Parameter(PARAMETER_DISCOUNT_EFFECTIVE_AFTER_THRESHOLD,
                        PARAMETER_DISCOUNT_EFFECTIVE_AFTER_THRESHOLD,
                        "2",
                        "the threshold of discount effective applied after number of months starting from contract start date"),
                new Parameter(PARAMETER_SWITCHING_NATIONALITY_TRIAL_DAYS,
                        PARAMETER_SWITCHING_NATIONALITY_TRIAL_DAYS,
                        "15",
                        "Switching Nationality- num of Trial Days"),
                new Parameter(SALES_BINDER_API_URL,
                        SALES_BINDER_API_URL,
                        "https://nosairat.salesbinder.com/api/2.0/",
                        "salesBinder url to be used for fetching data to erp "),
                new Parameter(SALES_BINDER_API_KEY,
                        SALES_BINDER_API_KEY,
                        "b3e083c7cd75faca9ef41ea8e35ebddfba1ee1c4",
                        "salesBinder Key to be used in authentication for fetching data to erp "),
                new Parameter(EDIRHAMS_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        EDIRHAMS_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        "BC 200",
                        "Edirhams visa expense transaction from bucket default value"),
                new Parameter(NOQOODI_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        NOQOODI_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        "BC 42",
                        "Noqoodi visa expense transaction from bucket default value"),
                new Parameter(CBD_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        CBD_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        "BC 93",
                        "CBD visa expense transaction from bucket default value"),
                new Parameter(EWALLET_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        EWALLET_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        "N_E-W",
                        "EWallet visa expense transaction from bucket default value"),
                new Parameter(CASH_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        CASH_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        "BC 064",
                        "Cash visa expense transaction from bucket default value"),
                new Parameter(PAY_PRO_WALLET_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        PAY_PRO_WALLET_VISA_EXPENSE_TRANSACTION_FROM_BUCKET,
                        "PPV",
                        "Pay PRO Wallet visa expense transaction from bucket default value"),
                new Parameter(NBR_DAYS_BEFORE_START_ORDER_CYCLE_WEEKLY,
                        NBR_DAYS_BEFORE_START_ORDER_CYCLE_WEEKLY,
                        "2",
                        "number of days before start weekly order cycle"),
                new Parameter(NBR_DAYS_BEFORE_START_ORDER_CYCLE_MONTHLY,
                        NBR_DAYS_BEFORE_START_ORDER_CYCLE_MONTHLY,
                        "2",
                        "number of days before start monthly order cycle"),

                new Parameter(NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_WEEKLY,
                        NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_WEEKLY,
                        "3",
                        "number of days before start reminders of weekly order cycle"),
                new Parameter(NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_MONTHLY,
                        NBR_DAYS_BEFORE_START_REMINDER_ORDER_CYCLE_MONTHLY,
                        "3",
                        "number of days before start reminders of monthly order cycle"),

                new Parameter(REMINDERS_TO_STOCK_KEEPER,
                        REMINDERS_TO_STOCK_KEEPER,
                        "3",
                        "number of reminder to stock keeper"),
                new Parameter(STOCK_KEEPER_WEEKLY_REMINDER_INDEX,
                        STOCK_KEEPER_WEEKLY_REMINDER_INDEX,
                        "0",
                        "index of the current weekly reminder to stock keeper"),
                new Parameter(STOCK_KEEPER_MONTHLY_REMINDER_INDEX,
                        STOCK_KEEPER_MONTHLY_REMINDER_INDEX,
                        "0",
                        "index of the current monthly reminder to stock keeper"),
                new Parameter(PARAMETER_SWITCHING_NATIONALITY_DD_FORMS_AMENDING_CONFIRMATION_MAIL,
                        PARAMETER_SWITCHING_NATIONALITY_DD_FORMS_AMENDING_CONFIRMATION_MAIL,
                        "<EMAIL>",
                        "Switching Nationality- when Client Confirm to Amend his DD Forms"),

                new Parameter(PARAMETER_CLIENT_REFUND_NUMBER_OF_UNUSED_DAYS_VALIDATION,
                        PARAMETER_CLIENT_REFUND_NUMBER_OF_UNUSED_DAYS_VALIDATION,
                        "3",
                        ""),
                //ACC-3126
                new Parameter(PARAMETER_PAYMENT_REQUEST_PURPOSE_COMPENSATING_ABOVE_4000,
                        PARAMETER_PAYMENT_REQUEST_PURPOSE_COMPENSATING_ABOVE_4000,
                        "Compensating clients for bad experience/days without service above AED 4000",
                        ""),

                new Parameter(PARAMETER_PAYMENT_REQUEST_PURPOSE_SAME_DAY_RECRUITMENT_FEE_FOR_MAID_VISA,
                        PARAMETER_PAYMENT_REQUEST_PURPOSE_SAME_DAY_RECRUITMENT_FEE_FOR_MAID_VISA,
                        "Same day recruitment fee for maidvisa",
                        ""),

                new Parameter(PARAMETER_MV_MAID_FAILED_TICKET_AND_ADMINISTRATION_FEES,
                        PARAMETER_MV_MAID_FAILED_TICKET_AND_ADMINISTRATION_FEES,
                        "2000",
                        ""),

                new Parameter(PARAMETER_PAYMENT_REQUEST_PURPOSE_COMPENSATING_BELOW_4000,
                        PARAMETER_PAYMENT_REQUEST_PURPOSE_COMPENSATING_BELOW_4000,
                        "Compensating clients for bad experience/days without service below AED 4000",
                        ""),

                new Parameter(PARAMETER_LOAN_EXPENSE_CODE,
                        PARAMETER_LOAN_EXPENSE_CODE,
                        "Loan Expense",
                        "Loan Expense."),
                new Parameter(PARAMETER_CLIENT_REFUND_AGENCY_IBAN,
                        PARAMETER_CLIENT_REFUND_AGENCY_IBAN,
                        "Agency_iban",
                        "Client Refund- Money Transfer, Agency IBAN"),
                new Parameter(PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME,
                        PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME,
                        "Agency_account_name",
                        "Client Refund- Money Transfer, Agency Account-Name"),
                new Parameter(PARAMETER_UNSUCCESSFUL_DD_CANCELLATION_REPORT_MAIL,
                        PARAMETER_UNSUCCESSFUL_DD_CANCELLATION_REPORT_MAIL,
                        "<EMAIL>",
                        "Unsuccessful DD Cancellation Report mail"),
                new Parameter(EXPENSE_LOCAL_CURRENCY,
                        EXPENSE_LOCAL_CURRENCY,
                        EXPENSE_CURRENCY_AED,
                        "Local Currency"),
                new Parameter(PARAMETER_EXPENSE_NOTIFICATION_NUMBER_OF_REQUESTS_THRESHOLD,
                        PARAMETER_EXPENSE_NOTIFICATION_NUMBER_OF_REQUESTS_THRESHOLD,
                        "1",
                        "Expense notification number of requests threshold."),

                new Parameter(PARAMETER_WORSE_CASE_SCENARIO_TAG,
                        PARAMETER_WORSE_CASE_SCENARIO_TAG,
                        "worse_case_scenario",
                        "the tag we use in item_measure_of_consumption picklist to know what is the worse case scenario."),

                new Parameter(PARAMETER_WEEK_START_TAG,
                        PARAMETER_WEEK_START_TAG,
                        "isWeekStart",
                        "the tag we use in Days picklist to know which item (day) is the start of the week."),
                new Parameter(PURCHASE_CONTROLLER_MAIL,
                        PURCHASE_CONTROLLER_MAIL,
                        "<EMAIL>",
                        "email of controller that will recive mail if purchase order cost is changed"),

                new Parameter(PARAMETER_TRANSPORTATION_EXPENSE_CODE,
                        PARAMETER_TRANSPORTATION_EXPENSE_CODE,
                        "transportation_expense",
                        "the value of this parameter must be used as the code of the transportation expense upon creation."),
                new Parameter(PARAMETER_TAXI_REIMBURSEMENT_EXPENSE_CODE,
                        PARAMETER_TAXI_REIMBURSEMENT_EXPENSE_CODE,
                        "taxi_reimbursement_expense",
                        "Must be used as the code of the taxi reimbursement expense upon creation."),
                new Parameter(PARAMETER_TAXI_REIMBURSEMENT_APPLICANT_EXPENSE_CODE,
                        PARAMETER_TAXI_REIMBURSEMENT_APPLICANT_EXPENSE_CODE,
                        "taxi_reimbursement__applicant_expense",
                        "Must be used as the code of the taxi reimbursement expense upon creation (for Applicant)."),
                new Parameter(PARAMETER_COVID_TEST_EXPENSE_CODE,
                        PARAMETER_COVID_TEST_EXPENSE_CODE,
                        "covid_test_expense",
                        "Must be used as the code of the COVID-19 test expense upon creation."),
                new Parameter(PARAMETER_COVID_LASER_TEST_EXPENSE_CODE,
                        PARAMETER_COVID_LASER_TEST_EXPENSE_CODE,
                        "covid_laser_test_expense",
                        "Must be used as the code of the COVID-19 laser test expense upon creation."),
                new Parameter(PARAMETER_CAREEM_SUPPLIER_NAME,
                        PARAMETER_CAREEM_SUPPLIER_NAME,
                        "Careem",
                        "Must be used as the name of the supplier representing Careem transportation."),
                new Parameter(PARAMETER_HALA_SUPPLIER_NAME,
                        PARAMETER_HALA_SUPPLIER_NAME,
                        "Hala",
                        "Must be used as the name of the supplier representing Hala transportation."),
                new Parameter(PARAMETER_ANSARI_EXPENSE_CODE,
                        PARAMETER_ANSARI_EXPENSE_CODE,
                        "ansari_expense_code",
                        "Represents the code of the Ansari-Expense, which is used in (Expense, Replenishment) Request Confirmation"),
                new Parameter(PARAMETER_EXPENSE_VACATION_DAYS_CODE,
                        PARAMETER_EXPENSE_VACATION_DAYS_CODE,
                        "expense_vacation_days",
                        "Must be used as the code of the vacation days expense upon creation."),
                new Parameter(PARAMETER_EXPENSE_SALARY_DISPUTE_CODE,
                        PARAMETER_EXPENSE_SALARY_DISPUTE_CODE,
                        "expense_salary_dispute",
                        "Must be used as the code of the salary dispute expense upon creation."),
                new Parameter(PARAMETER_EXPENSE_BONUS_CODE,
                        PARAMETER_EXPENSE_BONUS_CODE,
                        "expense_bonus",
                        "Must be used as the code of the Bonus expense upon creation."),
                new Parameter(PARAMETER_EXPENSE_MAIDS_AT_OTHER_EXPENSES_CODE,
                        PARAMETER_EXPENSE_MAIDS_AT_OTHER_EXPENSES_CODE,
                        "expense_maids_at_other_expenses",
                        "Must be used as the code of the maids at other expenses expense upon creation."),
                new Parameter(PARAMETER_INSURANCE_EXPENSE_CODE,
                        PARAMETER_INSURANCE_EXPENSE_CODE,
                        "insurance_expense",
                        "Must be used as the code of the insurance expense upon creation."),
                new Parameter(PARAMETER_PAYMENT_TYPES_TO_BE_COLLECTED_BY_CREDIT_CARD,
                        PARAMETER_PAYMENT_TYPES_TO_BE_COLLECTED_BY_CREDIT_CARD,
                        "{\"overstay_fee\" : {\"required\" : \"true\"}, " +
                                "\"insurance\" : {\"required\" : \"false\"}}",
                        "Payment types to be collected by credit card."),
                new Parameter(PARAMETER_DAYS_BEFORE_SEND_NOTIFICATION_FOR_GENERATION_PLAN_INSURANCE,
                        PARAMETER_DAYS_BEFORE_SEND_NOTIFICATION_FOR_GENERATION_PLAN_INSURANCE,
                        "1",
                        "Number of days before sending notification reminder for generation plan insurance"),
                new Parameter(PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_DAYS,
                        PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_DAYS,
                        "5",
                        "number of days for DD with Status PENDING_FOR_CANCELLATION to notify the user"),
                new Parameter(PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_EMAIL,
                        PARAMETER_DD_PENDING_FOR_CANCELLATION_SJ_EMAIL,
                        "<EMAIL>",
                        "email for sending DD Pending For Cancellation Report"),

                new Parameter(PARAMETER_CLIENT_REFUND_AGENCY_IBAN,
                        PARAMETER_CLIENT_REFUND_AGENCY_IBAN,
                        "Agency_iban",
                        "Client Refund- Money Transfer, Agency IBAN"),

                new Parameter(PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME,
                        PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME,
                        "Agency_account_name",
                        "Client Refund- Money Transfer, Agency Account-Name"),

                new Parameter(PARAMETER_ALREADY_MATCHED_TRANSACTIONS_BEFORE_X_DAYS_THRESHOLD,
                        PARAMETER_ALREADY_MATCHED_TRANSACTIONS_BEFORE_X_DAYS_THRESHOLD,
                        "7",
                        "Threshold number of days before to get matched transaction as from now."),

                new Parameter(PARAMETER_DD_CANCELLATION_FAILED_DUE_RPA_PROCESS_EMAIL,
                        PARAMETER_DD_CANCELLATION_FAILED_DUE_RPA_PROCESS_EMAIL,
                        "<EMAIL>",
                        "email to send DD Cancellation Failed due to RPA process to"),

                new Parameter(PARAMETER_PAYMENT_EXPIRY_FLOW_EOM_NUM_OF_DAYS,
                        PARAMETER_PAYMENT_EXPIRY_FLOW_EOM_NUM_OF_DAYS,
                        "7",
                        "Payment Expiry Flow- number of days to end of month"),
                new Parameter(PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR,
                        PARAMETER_COO_NIGHT_REVIEW_TIME_HOUR,
                        "20",
                        "time to get COO night review records"),

                new Parameter(PARAMETER_REMINDING_THE_COO_OF_APPROVAL_TASKS,
                        PARAMETER_REMINDING_THE_COO_OF_APPROVAL_TASKS,
                        "<EMAIL>",
                        "A daily email to remind the COO of approval tasks recipients"),
                new Parameter(PARAMETER_COO_SENDING_INQUIRIES,
                        PARAMETER_COO_SENDING_INQUIRIES,
                        "<EMAIL>",
                        "COO sending inquiries recipients"),

                new Parameter(PARAMETER_BANK_CONFIRMATION_AND_NIGHT_REVIEW_RECIPIENTS,
                        PARAMETER_BANK_CONFIRMATION_AND_NIGHT_REVIEW_RECIPIENTS,
                        "<EMAIL>",
                        "Bank confirmation and night review recipients"),

                new Parameter(PARAMETER_MV_MAID_FAILED_EMAIL_RECIPIENTS,
                        PARAMETER_MV_MAID_FAILED_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "Mv maid failed medical auto refund email recipients"),

                new Parameter(PARAMETER_NO_PRORATED_DD_FOR_ONE_MONTH_AGREEMENT_EMAIL_RECEIPTS,
                        PARAMETER_NO_PRORATED_DD_FOR_ONE_MONTH_AGREEMENT_EMAIL_RECEIPTS,
                        "<EMAIL>",
                        "no prorated dd for one month agreement email receipts"),

                new Parameter(PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_MANUAL_SWITCHING_NATIONALITY_EMAIL_RECEIPTS,
                        PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_MANUAL_SWITCHING_NATIONALITY_EMAIL_RECEIPTS,
                        "<EMAIL>",
                        "User switches nationality manually for a client who pays with a credit card email receipts"),
                new Parameter(PARAMETER_CASHIER_REJECT_CASH_COLLECTION_RECIPIENT,
                        PARAMETER_CASHIER_REJECT_CASH_COLLECTION_RECIPIENT,
                        "<EMAIL>",
                        "Cashier reject cash collection recipient"),

                new Parameter(PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_DOWNGRADE_RECIPIENTS_EMAIL,
                        PARAMETER_CLIENT_PAYING_VIA_CREDIT_CARD_DOWNGRADE_RECIPIENTS_EMAIL,
                        "<EMAIL>",
                        "Client paying via credit card downgrade recipients email"),
                new Parameter(PARAMETER_CLIENT_PAYING_VIA_CC_SWITCH_NATIONALITY_RECIPIENTS_EMAIL,
                        PARAMETER_CLIENT_PAYING_VIA_CC_SWITCH_NATIONALITY_RECIPIENTS_EMAIL,
                        "<EMAIL>",
                        "Client paying via credit card switched nationality recipients email"),
                new Parameter(PARAMETER_PAYMENT_EXPIRY_FLOW_EOM_NUM_OF_DAYS,
                        PARAMETER_PAYMENT_EXPIRY_FLOW_EOM_NUM_OF_DAYS,
                        "7",
                        "Payment Expiry Flow- number of days to end of month"),
                new Parameter(PARAMETER_EXPENSE_REQUEST_REDIRECT_AFTER_ACTION_PAGE,
                        PARAMETER_EXPENSE_REQUEST_REDIRECT_AFTER_ACTION_PAGE,
                        "modules/accounting/expense-approval/index.html#!/",
                        "page URL to redirect user after taking action regarding the Expense Request"),
                new Parameter(PARAMETER_PAYTAB_PAYMENT_REDIRECT_AFTER_ACTION_PAGE,
                        PARAMETER_PAYTAB_PAYMENT_REDIRECT_AFTER_ACTION_PAGE,
                        "modules/accounting/paytab-payment-confirmation/#!/",
                        "page URL to redirect user after taking action regarding the Expense Request"),
                // ACC-7580
                new Parameter(PARAMETER_RISK_DOCUMENT_MGMT_PAGE,
                        PARAMETER_RISK_DOCUMENT_MGMT_PAGE,
                        "accounting/v2/risk-documents-mgmt",
                        "Risk documents mgmt page URL"),

                new Parameter(PARAMETER_CONTRACT_REACTIVATED_WHILE_SWITCH_NATIONALITY_FLOW_RUNNING_EMAIL_RECIPIENTS,
                        PARAMETER_CONTRACT_REACTIVATED_WHILE_SWITCH_NATIONALITY_FLOW_RUNNING_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "Contract reactivated while switch nationality flow running email recipients"),

                new Parameter(PARAMETER_SWITCHING_BANK_ACCOUNT_EMAIL,
                        PARAMETER_SWITCHING_BANK_ACCOUNT_EMAIL,
                        "<EMAIL>",
                        "email address, when Client Switches his Bank Account"),
                new Parameter(RECEIPTS_EMAIL_UPON_VIP_CLIENT_RECEIVE_MESSAGE,
                        RECEIPTS_EMAIL_UPON_VIP_CLIENT_RECEIVE_MESSAGE,
                        "<EMAIL>",
                        "email address, when vip client receive a message"),

                new Parameter(PARAMETER_DD_CONFIGURATION_DEFAULT_NBR_OF_GENERATED_DDS,
                        PARAMETER_DD_CONFIGURATION_DEFAULT_NBR_OF_GENERATED_DDS,
                        "3",
                        "DD Configuration: default value for nbr of generated DDs"),

                new Parameter(PARAMETER_DD_CONFIGURATION_DEFAULT_DDA_TIME_FRAME,
                        PARAMETER_DD_CONFIGURATION_DEFAULT_DDA_TIME_FRAME,
                        "3",
                        "DD Configuration: default value for DDA time frame"),

                new Parameter(PARAMETER_DD_CONFIGURATION_DEFAULT_CREATE_MANUAL_FOR_DDB,
                        PARAMETER_DD_CONFIGURATION_DEFAULT_CREATE_MANUAL_FOR_DDB,
                        "true",
                        "DD Configuration: default value for create manual for DDB"),

                new Parameter(PARAMETER_DD_CONFIGURATION_DEFAULT_INCLUDE_MANUAL_IN_DDB_FLOW,
                        PARAMETER_DD_CONFIGURATION_DEFAULT_INCLUDE_MANUAL_IN_DDB_FLOW,
                        "true",
                        "DD Configuration: default value for include manual in DDB flow"),

                new Parameter(PARAMETER_MAIDS_CC_CLIENT_CALL,
                        PARAMETER_MAIDS_CC_CLIENT_CALL,
                        "971505741759",
                        "Company Communication Numbers for Prospect/Client"),
                new Parameter(PARAMETER_MV_CLIENT_CALL,
                        PARAMETER_MV_CLIENT_CALL,
                        "971505741759",
                        "Company Communication Number for MV Contracts Clients"),
                new Parameter(PARAMETER_DEFAULT_CC_AMOUNT_FOR_FILIPINA,
                        PARAMETER_DEFAULT_CC_AMOUNT_FOR_FILIPINA,
                        "4301",
                        "Default CC Amount for Filipina"),
                new Parameter(PARAMETER_DEFAULT_CC_AMOUNT_FOR_NON_FILIPINA,
                        PARAMETER_DEFAULT_CC_AMOUNT_FOR_NON_FILIPINA,
                        "3129",
                        "Default CC Amount for Non-Filipina"),
                new Parameter(PARAMETER_BOUNCING_REJECTION_SIGNING_PAPER_MODE_THRESHOLD_TRIAL,
                        PARAMETER_BOUNCING_REJECTION_SIGNING_PAPER_MODE_THRESHOLD_TRIAL,
                        "2",
                        "the number of DD Bouncing Rejection trials after it the contract must be marked as Signing Paper Mode"),
                new Parameter(PARAMETER_BOUNCING_No_MANUAL_DD_SCHEDULE_CONTRACT_FOR_TERMINATION_MAX_TRIAL,
                        PARAMETER_BOUNCING_No_MANUAL_DD_SCHEDULE_CONTRACT_FOR_TERMINATION_MAX_TRIAL,
                        "4",
                        "the number of DD Bouncing Rejection trials after it the contract must be Scheduled for Termination"),

                new Parameter(PARAMETER_CC_CONTRACT_CHARGE_FREE_DAYS,
                        PARAMETER_CC_CONTRACT_CHARGE_FREE_DAYS,
                        "3",
                        "Maid.CC Contracts, the number of days client which can still terminate his contract and without any charge"),
                new Parameter(PARAMETER_MAXIMUM_BOUNCED_PAYMENT_PAUSE_TIALS,
                        PARAMETER_MAXIMUM_BOUNCED_PAYMENT_PAUSE_TIALS,
                        "3",
                        "allowed number of bounced payment pauses in a month"),
                new Parameter(PARAMETER_WIRE_TRANSFER_AMOUNT_DIFFERENCE_THRESHOLD,
                        PARAMETER_WIRE_TRANSFER_AMOUNT_DIFFERENCE_THRESHOLD,
                        "100",
                        "Expected Wire Transfer amount difference threshold"),

                new Parameter(PARAMETER_CC_CONTRACT_PRORATED_CHARGE_FREE_DAYS,
                        PARAMETER_CC_CONTRACT_PRORATED_CHARGE_FREE_DAYS,
                        "3",
                        "Maid.CC Contracts, the number of days client which can still terminate his contract and without any charge"),
                new Parameter(MAX_SIGNATURE_COLLECTION_REMINDERS,
                        MAX_SIGNATURE_COLLECTION_REMINDERS,
                        "2",
                        "DD signature collection flow max signature collection reminders"),
                new Parameter(SCHEDULED_DD_SEND_NOTIFICATION_PERIOD_BEFORE_START_DATE,
                        SCHEDULED_DD_SEND_NOTIFICATION_PERIOD_BEFORE_START_DATE,
                        "5",
                        "Period before the scheduled DD start date to send the client the needed notification"),
                new Parameter(MAX_SIGNATURE_COLLECTION_TRIALS,
                        MAX_SIGNATURE_COLLECTION_TRIALS ,
                        "1",
                        "DD signature collection flow max signature collection trials"),
                new Parameter(NEW_DDA_CREATED_START_DATE_PASSED_REPORT_RECIPIENT,
                        NEW_DDA_CREATED_START_DATE_PASSED_REPORT_RECIPIENT,
                        "<EMAIL>",
                        "Email that send upon ERP created an automatic DD with a start date that has passed already"),
                new Parameter(DD_GEN_PRE_POSTPONE_PERIOD,
                        DD_GEN_PRE_POSTPONE_PERIOD,
                        "5",
                        "Postpone period for generate one-time direct debit"),
                new Parameter(PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY,
                        PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY,
                        "2",
                        "Postpone period for DD start date after x day"),
                new Parameter(PARAMETER_ACCEPTED_GEN_POSTPONE_GENERATION_DATE_PASSED_X_DAY,
                        PARAMETER_ACCEPTED_GEN_POSTPONE_GENERATION_DATE_PASSED_X_DAY,
                        "2",
                        "Accepted generation Postpone if generation date is passed before x day"),
                new Parameter(ADJUSTED_END_DATE_DD_GEN_PRE_POSTPONE_PERIOD,
                        ADJUSTED_END_DATE_DD_GEN_PRE_POSTPONE_PERIOD,
                        "5",
                        "Postpone period for generate adjusted end date direct debit "),
                new Parameter(PARAMETER_NON_MONTHLY_POSTPONED_DDS_GENERATION_REPORT_RECIPIENT,
                        PARAMETER_NON_MONTHLY_POSTPONED_DDS_GENERATION_REPORT_RECIPIENT,
                        "<EMAIL>",
                        "The recipients for non monthly postponed dds report"),
                new Parameter(FAILED_DDS_GENERATION_REPORT_RECIPIENT,
                        FAILED_DDS_GENERATION_REPORT_RECIPIENT,
                        "<EMAIL>",
                        "The recipient's email containing a Failed DDS generation report"),
                new Parameter(PERIOD_BETWEEN_SIGNATURE_COLLECTION_REMINDER,
                        PERIOD_BETWEEN_SIGNATURE_COLLECTION_REMINDER ,
                        "24",
                        "Period between signature collection reminder in DD signature collection flow"),
                new Parameter(PARAMETER_DD_MULTI_CONFIRMED_EMAILS ,
                        PARAMETER_DD_MULTI_CONFIRMED_EMAILS ,
                        "<EMAIL>",
                        "User's email will be notified when multi-ddf is confirmed"),
                new Parameter(PARAMETER_EXPIRED_LINK_WHATSAPP_NUMBER ,
                        PARAMETER_EXPIRED_LINK_WHATSAPP_NUMBER ,
                        "97142479153",
                        "Whatsapp number to redirect client in case payment link expires"),
                new Parameter(PARAMETER_ACC5044_CONTRACT_CREATION_DATE,
                        PARAMETER_ACC5044_CONTRACT_CREATION_DATE ,
                        "2021-09-01",
                        "Date of contract creation date in ACC-5044"),
                new Parameter(PARAMETER_ACC5044_MONTHLY_PAYMENT_AMOUNT ,
                        PARAMETER_ACC5044_MONTHLY_PAYMENT_AMOUNT ,
                        "4121",
                        "Amount of monthly payment in ACC-5044"),
                new Parameter(
                        PARAMETER_PRE_PDP_PAYMENT_WITHIN_WEEK_EMAILS,
                        PARAMETER_PRE_PDP_PAYMENT_WITHIN_WEEK_EMAILS,
                        "<EMAIL>",
                        "Email To send table of PRE-PDP credit card payments in limited period."),
                new Parameter(
                        PARAMETER_ELIGIBLE_REFUND_EMAIL,
                        PARAMETER_ELIGIBLE_REFUND_EMAIL,
                        "<EMAIL>",
                        "Email To send table of eligible refund in last 24 hour"),
                new Parameter(
                    PARAMETER_ELIGIBLE_REFUND_EMAIL_CC,
                    PARAMETER_ELIGIBLE_REFUND_EMAIL_CC,
                    "<EMAIL>",
                    "Email CC To send table of eligible refund in last 24 hour"),
                new Parameter(
                        PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD,
                        PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD,
                        "7",
                        "Running payment expiry flow before x days from paid end date"),
                new Parameter(
                        CC_APP_RETRACT_CANCELLATION_MAIL_RECIPIENTS,
                        CC_APP_RETRACT_CANCELLATION_MAIL_RECIPIENTS,
                        "<EMAIL>",
                        "A recipient's email when a cancellation contract is retracted."),
                new Parameter(
                        PARAMETER_DD_REQUESTED_AND_CLIENT_PAYING_VIA_CREDIT_CARD_RECIPIENTS,
                        PARAMETER_DD_REQUESTED_AND_CLIENT_PAYING_VIA_CREDIT_CARD_RECIPIENTS,
                        "<EMAIL>",
                        "A recipient's email when a dd requested and client paying via credit card."),
                new Parameter(URGENT_VISA_FEES, URGENT_VISA_FEES, "500",
                        "Urgent Visa Charges - VPM-2585"),
                new Parameter(PARAMETER_EXPENSE_REJECTED_FROM_EMAIL_PAGE,
                        PARAMETER_EXPENSE_REJECTED_FROM_EMAIL_PAGE,
                        "/modules/accounting/expenses-approval-byemail/index.html#!/",
                        "A page link for approving/rejecting an expense from an email"),
                new Parameter(
                        CCAPP_CHANGE_BANK_DETAILS_THRESHOLD,
                        CCAPP_CHANGE_BANK_DETAILS_THRESHOLD,
                        "10",
                        "CMA-2728 Threshold monitor"),
                new Parameter(
                        CCAPP_PAY_BY_CARD_THRESHOLD,
                        CCAPP_PAY_BY_CARD_THRESHOLD,
                        "3",
                        "CMA-2728 Threshold monitor"),
                new Parameter(
                        CCAPP_VIEW_PAYMENT_HISTORY_THRESHOLD,
                        CCAPP_VIEW_PAYMENT_HISTORY_THRESHOLD,
                        "100",
                        "CMA-2728 Threshold monitor"),
                new Parameter(
                        CCAPP_CHANGE_BANK_DETAILS_EMAIL_RECIPIENTS,
                        CCAPP_CHANGE_BANK_DETAILS_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "CMA-2728 Threshold monitor"),
                new Parameter(
                        CCAPP_PAY_BY_CARD_EMAIL_RECIPIENTS,
                        CCAPP_PAY_BY_CARD_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "CMA-2728 Threshold monitor"),
                new Parameter(
                        CCAPP_VIEW_PAYMENT_HISTORY_EMAIL_RECIPIENTS,
                        CCAPP_VIEW_PAYMENT_HISTORY_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "CMA-2728 Threshold monitor"),
                new Parameter(
                        PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET,
                        PARAMETER_STATEMENTS_TRANSACTIONS_FROM_BUCKET,
                        "BC 10",
                        "From Bucket Code used in statements transactions"),
                new Parameter(BANK_TRANSFER_PAGE_NOTIFICATION_RECIPIENTS,
                        BANK_TRANSFER_PAGE_NOTIFICATION_RECIPIENTS,
                        "<EMAIL>",
                        "All recipients' emails upon bank transfer expense creation"),
                new Parameter(PURCHASE_AUDITOR_PAGE_NOTIFICATION_RECIPIENTS,
                        PURCHASE_AUDITOR_PAGE_NOTIFICATION_RECIPIENTS,
                        "<EMAIL>",
                        "All recipients' emails upon purchase auditor expense creation"),
                new Parameter(PURCHASE_MANAGER_PAGE_NOTIFICATION_RECIPIENTS,
                        PURCHASE_MANAGER_PAGE_NOTIFICATION_RECIPIENTS,
                        "<EMAIL>",
                        "All recipients' emails upon purchase manager expense creation"),
                new Parameter(CREDIT_CARD_HOLDER_PAGE_NOTIFICATION_RECIPIENTS,
                        CREDIT_CARD_HOLDER_PAGE_NOTIFICATION_RECIPIENTS,
                        "<EMAIL>",
                        "All recipients' emails upon credit card expense creation"),
                new Parameter(AUDIT_MANAGER_PAGE_NOTIFICATION_RECIPIENTS,
                        AUDIT_MANAGER_PAGE_NOTIFICATION_RECIPIENTS,
                        "<EMAIL>",
                        "All recipients' emails upon audit manager expense creation"),

                // ACC-6831
                new Parameter(PARAMETER_ACCOUNT_CEILING_EXCEEDED_CC_EMAIL,
                        PARAMETER_ACCOUNT_CEILING_EXCEEDED_CC_EMAIL,
                        "<EMAIL>",
                        "This parameter define the email addresses should be listed as CC for email #1 in Emails"),

                new Parameter(PARAMETER_HOURS_TO_EXPIRED_MATCHING_CREDIT_CARD_WHEN_PARSING_STATEMENT,
                        PARAMETER_HOURS_TO_EXPIRED_MATCHING_CREDIT_CARD_WHEN_PARSING_STATEMENT,
                        "48",
                        "The absolute difference between card transaction date, and expense request date"),
                new Parameter(
                        PARAMETER_LUGGAGE_COMPENSATION_EXPENSE_CODE,
                        PARAMETER_LUGGAGE_COMPENSATION_EXPENSE_CODE,
                        "PM 03",
                        "Luggage compensation expense code"),

                new Parameter(
                        PARAMETER_MISSING_TRANSFER_AMOUNT_FOR_CLIENT_ALERT_RECIPIENTS,
                        PARAMETER_MISSING_TRANSFER_AMOUNT_FOR_CLIENT_ALERT_RECIPIENTS,
                        "{" +
                                "\"clientMV\" : \"<EMAIL>\"," +
                                "\"clientCCLiveOut\" : \"<EMAIL>\"," +
                                "\"clientCCLiveIn\" : \"<EMAIL>\"" +
                                "}",
                        "Missing transfer amount for client alert recipients emails"),

                new Parameter(PARAMETER_MISSING_TAX_INVOICE_TAB_DEPLOYMENT_DATE,
                        PARAMETER_MISSING_TAX_INVOICE_TAB_DEPLOYMENT_DATE,
                        "",
                        "The date of deploying the related task, which will be used to only filter on a results after this date"),
                new Parameter(
                         PARAMETER_OFFSET_FOR_VALID_EXPENSE_REQUEST,
                         PARAMETER_OFFSET_FOR_VALID_EXPENSE_REQUEST,
                         "2",
                         "number of days that the expense request will be valid within"),
                new Parameter(
                        PARAMETER_CONTRACT_RETRACT_AFTER_PAYMENT_RECEIVED_REASONS,
                        PARAMETER_CONTRACT_RETRACT_AFTER_PAYMENT_RECEIVED_REASONS,
                        "client_declines_paying_in_cheques;client_did_not_provide_new_info_after_rejection;" +
                                "client_did_not_provide_new_info_bounced_payment;" +
                                "client_did_not_sign_dd_after_x_days;" +
                                "due_paying_via_cc_flow;" +
                                "due_ipam_flow;" +
                                "due_extension_flow;",
                        "Reasons of termination for retract contract after monthly payment received"),
                new Parameter(
                        PARAMETER_BANK_STATEMENT_CONFIRMED_TRANSACTION_DIRECT_CALL,
                        PARAMETER_BANK_STATEMENT_CONFIRMED_TRANSACTION_DIRECT_CALL,
                        "true",
                        "direct call when confirmed transaction After upload bank statement"),
                new Parameter(
                        PARAMETER_SPECIFY_BGT_QUEUE_OF_CONFIRM_BANK_STATEMENT_TRANSACTION,
                        PARAMETER_SPECIFY_BGT_QUEUE_OF_CONFIRM_BANK_STATEMENT_TRANSACTION,
                        "HeavyOperationsQueue",
                        "Specify queue type of bgt: HeavyOperationsQueue, NormalOperationsQueue"),
                new Parameter(
                        PARAMETER_CREDIT_CARD_REFUND_FAILED_EMAIL_RECIPIENTS,
                        PARAMETER_CREDIT_CARD_REFUND_FAILED_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "Credit card refund failed email recipients"),
                new Parameter(
                        PARAMETER_MIN_DATE_PAYMENTS_VALUE,
                        PARAMETER_MIN_DATE_PAYMENTS_VALUE,
                        "2024-09-10",
                        "min date payments value for gpt api"),
                new Parameter(
                        PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_FROM_BUCKET,
                        PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_FROM_BUCKET,
                        "BC 421",
                        "Credit card statement file pos transaction default from bucket code"),
                new Parameter(
                        PARAMETER_DATE_TO_EXCLUDE_CONTRACTS_FROM_PAYMENT_EXPIRY_FLOW,
                        PARAMETER_DATE_TO_EXCLUDE_CONTRACTS_FROM_PAYMENT_EXPIRY_FLOW,
                        "2025-05-01",
                        "date to exclude contracts from payment expiry flow"),
                new Parameter(
                        PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD,
                        PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_CANCELLATION_RECORD,
                        "NormalOperationsQueue",
                        "Specify queue type of background task for Bank dd Cancellation file : NormalOperationsQueue, HeavyOperationsQueue, SequentialQueue"),
                new Parameter(
                        PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_EXPENSE,
                        PARAMETER_CREDIT_CARD_STATEMENT_FILE_POS_TRANSACTION_DEFAULT_EXPENSE,
                        "FT 206",
                        "Credit card statement file pos transaction default expense code"),
                new Parameter(
                        PARAMETER_X_HOUR_AGO_DURING_WHICH_ALL_GENERATED_DD_HIDDEN,
                        PARAMETER_X_HOUR_AGO_DURING_WHICH_ALL_GENERATED_DD_HIDDEN,
                        "1",
                        "x hour ago during which all generated DD hidden"),
                new Parameter(
                        PARAMETER_ENABLE_MATCHING_THE_DDC_WITH_DD_OR_PAYMENT,
                        PARAMETER_ENABLE_MATCHING_THE_DDC_WITH_DD_OR_PAYMENT,
                        "{" +
                            "\"matchWithDDs\" : \"on\"," +
                            "\"matchWithPayments\" : \"on\"" +
                        "}",
                        "enable feature of matching the ddc cpt with DDs or Payments"),
                new Parameter(
                        PARAMETER_BANK_STATEMENT_CONFIRMED_TRANSACTION_DIRECT_CALL,
                        PARAMETER_BANK_STATEMENT_CONFIRMED_TRANSACTION_DIRECT_CALL,
                        "true",
                        "direct call when confirmed transaction After upload bank statement"),
                new Parameter(
                        PARAMETER_ENABLE_FAILED_CAPTURE_RECURRING_PAYMENT,
                        PARAMETER_ENABLE_FAILED_CAPTURE_RECURRING_PAYMENT,
                        "false",
                        "enable failed capture recurring payment"),
                new Parameter(
                        PARAMETER_DIFFERENCE_RESET_TRIAL_BOUNCED_PAYMENT,
                        PARAMETER_DIFFERENCE_RESET_TRIAL_BOUNCED_PAYMENT,
                        "2",
                        "difference between current trial with max trial for reset trial of bounced payment"),
                new Parameter(
                        PARAMETER_NGPT_REFUND_PURPOSES_FOR_CC_CONTRACT,
                        PARAMETER_NGPT_REFUND_PURPOSES_FOR_CC_CONTRACT,
                        "",
                        "set of refund purposes for CC contract just for GPT"),
                new Parameter(
                        PARAMETER_NGPT_REFUND_PURPOSES_FOR_MV_CONTRACT,
                        PARAMETER_NGPT_REFUND_PURPOSES_FOR_MV_CONTRACT,
                        "GPT Pre R-visa cancellation",
                        "set of refund purposes for MV contract just for GPT"),
                new Parameter(PARAMETER_REFUND_DELAY_HOURS,
                        PARAMETER_REFUND_DELAY_HOURS,
                        "48",
                        "Deley CC refund for canceled contract in hours"),
                new Parameter(
                        PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_ACTIVATION_RECORD,
                        PARAMETER_SPECIFY_BACKGROUND_TASK_QUEUE_OF_BANK_DDS_ACTIVATION_RECORD,
                        "NormalOperationsQueue",
                        "Specify queue type of background task for Bank dd Activation file : NormalOperationsQueue, HeavyOperationsQueue, SequentialQueue"),
                new Parameter(
                        PARAMETER_PAYMENT_COLLECTED_AFTER_TERMINATION_RECEIPTS,
                        PARAMETER_PAYMENT_COLLECTED_AFTER_TERMINATION_RECEIPTS,
                        "<EMAIL>",
                        "Payment Collected After Termination Receipts"),
              new Parameter(
                      PARAMETER_PAYMENT_COLLECTED_AFTER_TERMINATION_RECEIPTS,
                      PARAMETER_PAYMENT_COLLECTED_AFTER_TERMINATION_RECEIPTS,
                      "<EMAIL>",
                     "Payment Collected After Termination Receipts"),
                new Parameter(
                        PARAMETER_MV_EXTENSION_FLOW_ENABLED,
                        PARAMETER_MV_EXTENSION_FLOW_ENABLED,
                        "true",
                        "Enable/disable extension flow for MV clients"),
                new Parameter(
                        PARAMETER_UPGRADING_REPLACEMENT_FAILED_AFTER_CLIENT_PAYING_UPGRADING_FEE_EMAIL_RECIPIENTS,
                        PARAMETER_UPGRADING_REPLACEMENT_FAILED_AFTER_CLIENT_PAYING_UPGRADING_FEE_EMAIL_RECIPIENTS,
                        "<EMAIL>",
                        "Switch nationality fees mapping replacement failed email recipients"),
                new Parameter(
                        PARAMETER_CONTRACT_RETRACT_AFTER_CLIENT_SIGN_DD_REASONS,
                        PARAMETER_CONTRACT_RETRACT_AFTER_CLIENT_SIGN_DD_REASONS,
                        "Due_bounced_payment;" +
                        "signature_collection_flow_max_trials_reached;" +
                        "client_did_not_sign_dd_after_x_days;" +
                        "direct_debit_rejection_type_a_maxsignaturetrials_reached;" +
                        "direct_debit_rejection_type_a_maxbankinfotrials_reached;" +
                        "direct_debit_rejection_type_b_bank_info_max_trials_reached;" +
                        "direct_debit_rejection_type_b_maxsignaturetrialsb_reached;" +
                        "client_did_not_provide_new_info_after_rejection;",
                        "Reasons of termination for retract contract after client sign DDs"),
                new Parameter(
                        PARAMETER_ACC_CREDIT_CARD_TOKENIZATION_ENABLED,
                        PARAMETER_ACC_CREDIT_CARD_TOKENIZATION_ENABLED,
                        "true",
                        "Enable Credit Card Tokenization in ERP"),
                new Parameter(
                        PARAMETER_STOP_CLOSE_MAIN_DDC_TODO,
                        PARAMETER_STOP_CLOSE_MAIN_DDC_TODO,
                        "false",
                        "Stop Close Main Ddc Todo"),
                new Parameter(
                        PARAMETER_BOT_USERS_LOGIN_NAMES,
                        PARAMETER_BOT_USERS_LOGIN_NAMES,
                        "",
                        "Login names of users who are bots"),
                //ACC-6840
                new Parameter(PARAMETER_CREDIT_CARD_UNKNOWN_ERROR_CODES_EMAIL_RECEIPTS,
                        PARAMETER_CREDIT_CARD_UNKNOWN_ERROR_CODES_EMAIL_RECEIPTS,
                        "<EMAIL>",
                        "Credit card unknown error codes Email Receipts"),
                new Parameter(PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_PRORATED_DISCOUNT_FOR_MATCH_PTC,
                        PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_PRORATED_DISCOUNT_FOR_MATCH_PTC,
                        "0.0",
                        "Difference between prorated or discounted payment that can be accepted for match PTC"),
                new Parameter(PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_TWO_AMOUNT_FOR_MATCH_PTC,
                        PARAMETER_ACCEPTED_DIFFERENCE_BETWEEN_TWO_AMOUNT_FOR_MATCH_PTC,
                        "0.0",
                        "difference between two amount that can be accepted"),
                new Parameter(
                        PARAM_INTERVAL_FOR_DUE_PAYMENTS,
                        PARAM_INTERVAL_FOR_DUE_PAYMENTS,
                        "4",
                        "interval from current date before which to consider CC payments as DUE"),
                new Parameter(PARAMETER_SEND_VISA_EXPENSE_MISSING_ERP_ALERT_DEPLOYMENT_DATE,
                        PARAMETER_SEND_VISA_EXPENSE_MISSING_ERP_ALERT_DEPLOYMENT_DATE,
                        "2025-07-19",
                        "The deployment date for send alerts for missing visa statement transactions"),
                new Parameter(PARAMETER_VISA_EXPENSE_SAME_REFERENCE_NUMBER_BUT_DIFFERENT_AMOUNTS_ALERT_RECIPIENTS,
                        PARAMETER_VISA_EXPENSE_SAME_REFERENCE_NUMBER_BUT_DIFFERENT_AMOUNTS_ALERT_RECIPIENTS,
                        "<EMAIL>",
                        "Missing ERP Alert Recipients Three days unclosed"),
                new Parameter(PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS_AFTER_THREE_DAYS_UNCLOSED,
                        PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS_AFTER_THREE_DAYS_UNCLOSED,
                        "<EMAIL>",
                        "Missing ERP Alert Recipients Three days unclosed"),
                new Parameter(PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS,
                        PARAMETER_VISA_EXPENSE_MISSING_ERP_ALERT_RECIPIENTS,
                        "<EMAIL>",
                        "Missing ERP Alert Recipients")
                );
    }

    private void adminSetup() {
        Picklist employeeTypesPicklist = pickListRep.findByCode(Picklist.EMPLOYEE_TYPES);
        if (employeeTypesPicklist != null) {
            employeeTypesPicklist.addItems(new String[]{"Manager"});
            pickListRep.save(employeeTypesPicklist);
        }

        Picklist mangerNotesPicklist = pickListRep.findByCode(PICKLIST_MANAGER_NOTE_DEDUCTION_REASONS_CODE);
        if(mangerNotesPicklist != null) {
            mangerNotesPicklist.addItems(
                    new String[]{"Converting Maid To MaidVISA", "Repeat EID Deduction", "Pension Compensation",
                            "NO_SHOW_FIRST_MORNING", "NO_SHOW_SECOND_MORNING", "NO_SHOW_THIRD_MORNING",
                            "NO_SHOW_FOURTH_MORNING", "NO_SHOW_FIFTH_MORNING", "NO_SHOW_SIXTH_MORNING",
                            "Self Replacement", "Faulty Replacement"});
            pickListRep.save(mangerNotesPicklist);
        }

        // acc-1595
        Picklist reasonsOfTerminationList =
                pickListRep.findByCode(PICKLIST_TERMINATION_REASON_LIST);
        if (reasonsOfTerminationList != null) {
            reasonsOfTerminationList.addItems(
                    new String[]{"direct_debit_rejection_type_b_bank_info_max_trials_reached",
                            "direct_debit_rejection_type_b_maxsignaturetrialsb_reached",
                            "direct_debit_rejection_type_a_maxbankinfotrials_reached",
                            "direct_debit_rejection_type_a_maxsignaturetrials_reached",
                            "client_did_not_sign_dd_after_x_days",
                            "client_did_not_provide_new_info_after_rejection",
                            "client_did_not_provide_new_info_bounced_payment",
                            "client_do_not_have_mobile_number"
                    });
            pickListRep.save(reasonsOfTerminationList);
        }

        /*Picklist humanSmsReasonSms
                = pickListRep.findByCode(AccountingModule.PICKLIST_CALL_REASONS_SMS);

        humanSmsReasonSms.addItems(new String[]{"DDInfoMissingWithoutCashPayment"});
        pickListRep.save(humanSmsReasonSms);*/

        // ACC-837
        Picklist additionPicklist =
                pickListRep.findByCode(PICKLIST_MANAGER_NOTE_ADDITION_REASONS_CODE);
        if(additionPicklist != null) {
            additionPicklist.addItems(new String[]{"Cover Deduction Limit", "housemaid refund", "Cover Negative Salary",
                    PICKLIST_ITEM_MANAGER_NOTE_PAYING_VACATION_DAYS //ACC-1862
            });
            pickListRep.save(additionPicklist);
        }

        // ACC-3442
        Picklist accountantTodoTypesPicklist =
                pickListRep.findByCode(PICKLIST_ACCOUNTANT_TODO_TYPES);
        if (accountantTodoTypesPicklist != null) {
            employeeTypesPicklist.addItems(new String[]{"employer_agreement"});
            pickListRep.save(accountantTodoTypesPicklist);
        }


        // ACC-837
        for (Position p : getUserPositions()) {
            Position pp = positionRepository.findByCode(p.getCode());
            if (pp == null) positionRepository.save(p);
        }

        // ACC-3315
        Picklist expenseRequestedFrom = pickListRep.findByCode(PICKLIST_EXPENSE_REQUESTED_FROM);
        if(expenseRequestedFrom != null) {
            expenseRequestedFrom.addItem("Show expense in Taxi page");
            pickListRep.save(expenseRequestedFrom);
        }

        // ACC-2305
        Picklist bouncedPaymentMessageStatuses = pickListRep.findByCode(PICKLIST_BOUNCED_PAYMENT_STATUS);
        if(bouncedPaymentMessageStatuses != null) {
            bouncedPaymentMessageStatuses.addItem("Bounced payment received - not including worker salary");
            pickListRep.save(bouncedPaymentMessageStatuses);
        }
    }

    @Override
    public void setup() {
        try {
            adminSetup();

            // ACC-1706
            // SMSes
            messageTemplateService.getMessageTemplates()
                    .forEach(t -> createTemplate(t.getName(), t.getSpecs(), t.getText(), t.isUnicode()));

            // with expressions
            messageTemplateService.getMessageTemplatesWithExpression()
                    .forEach(t -> createTemplate(t.getMsgTemplate().getName(), t.getMsgTemplate().getSpecs(),
                            t.getMsgTemplate().getText(), t.getMsgTemplate().isUnicode(), t.getExpressions()));

            // ACC-2106
            // EMAILS
            emailTemplateService.getEmailTemplates()
                    .forEach(t -> createTemplate(t.getName(), t.getDescription(), t.getSubject(), t.getText()));
            setupCcAppCmsTemplates.setupTemplates();

            // NEW SMSes
            ccAppSmsTemplate.getMessageTemplates()
                    .forEach(t -> createTemplate(t.getName(), t.getSpecs(), t.getText(), t.isUnicode()));
            mvAppSmsTemplate.getMessageTemplates()
                    .forEach(t -> createTemplate(t.getName(), t.getSpecs(), t.getText(), t.isUnicode()));

            // NOTIFICATIONS
            CCAppNotificationTemplates.createCCAppNotifications();
            MvAppNotificationTemplates.createCCAppNotifications();
            MvHousemaidNotificationTemplates.createTemplates();

            CmOngoingCollectionFlowsTemplates.createTemplates();

            ChatAITemplateService.createTemplates();
        } catch (Exception e) {
            e.printStackTrace();
            Setup.getMailService()
                    .sendEmail(
                            EmailHelper.getRecipients("<EMAIL>"),
                            new TextEmail("An error happened on module startup", e.getMessage()),
                            EmailReceiverType.Office_Staff);
        }
    }

    // ACC-837
    private List<Position> getUserPositions() {
        return Arrays.asList(
                new Position(CHANGE_TRANSACTIONS_DATES_ADMIN_POSITION, "change_transactions_dates", ""), // ACC-505
                new Position(PARAMETER_DELETE_PAYMENT_MATCHING_FILE_POSITION, PARAMETER_DELETE_PAYMENT_MATCHING_FILE_POSITION, ""), // ACC-600
                new Position(PAYMENT_REQUEST_HOUSEMAID_BUSINESS_USER_POSITION, "Payments Requests: Housemaid Refunds", ""), // ACC-837
                new Position(PAYMENT_REQUEST_CLIENT_BUSINESS_USER_POSITION, "Payments Requests: Clients Refunds", ""),
                new Position(PAYMENT_REQUEST_BUSINESS_MANGER_USER_POSITION, "Payments Requests: Business Manager", ""),
                new Position(PAYMENT_REQUEST_PAYMENT_REQUEST_MANGER_USER_POSITION, "Payments Requests: Request Manager", ""),
                new Position(PAYMENT_REQUEST_ACCOUNTANT_USER_POSITION, "Payments Requests: Accountant", ""),
                new Position(PAYMENT_REQUEST_TRANSFERRER_POSITION, "Payments Requests: Transferer", ""),
                new Position(PAYMENT_REQUEST_RELEASER_USER_POSITION, "Payments Requests: Releaser", ""),
                new Position(PAYMENT_REQUEST_MONEY_MAANAGER_USER_POSITION, "Payments Requests: Money Manager", ""),
                new Position(PAYMENT_REQUEST_MAIDS_REFUND_USER_POSITION, "Payments Requests: Maids Refunds Manager", ""), // ACC-1186
                new Position(PAYMENT_REQUEST_CLIENTS_REFUND_USER_POSITION, "Payments Requests: Clients Refunds Manager", ""),
//                new Position(PAYMENT_REQUEST_CEO_USER_POSITION, "Payments Requests: CEO", ""),
//                new Position(PAYMENT_REQUEST_CFO_USER_POSITION, "Payments Requests: CFO", ""));
                new Position(CLIENT_REFUND_COO_USER_POSITION, "Client Refund: COO", ""),
                new Position(CLIENT_REFUND_APPROVER_USER_POSITION, "Client Refund: Approver", ""),
                new Position(CLIENT_REFUND_REQUESTER_USER_POSITION, "Client Refund: Requester", ""),
                new Position(BANK_TRANSFER_ACCOUNTANT_USER_POSITION, "Bank Transfer: ACCOUNTANT", ""),
                new Position(BANK_TRANSFER_REVIEWER_USER_POSITION, "Bank Transfer: Reviewer", ""),
                new Position(BANK_TRANSFER_COO_USER_POSITION, "Bank Transfer: COO", ""),
                new Position(COO_PAGE_TABS_FULL_CONTROL, "COO Full Control", ""),
                new Position(EXPENSE_HISTORY_FULL_ACCESS, "Full Access to Expenses history", ""),
                new Position(COO_PAGE_TABS_PENDING_APPROVALS, "Pending Approval: COO", ""),
                new Position(NIGHT_REVIEW_COO_USER_POSITION, "Night Review: COO", ""),
                new Position(STOCK_KEEPER, "Stock keeper", ""),
                new Position(STOCK_KEEPER_MANAGER, "Stock keeper manager", ""),
                new Position(EXPENSE_AUDIT_MANAGER_USER_POSITION, "Expense: Audit Manager", ""),
                new Position(EXPENSE_RECONCILITOR_USER_POSITION, "Expense: Reconciliator", ""),
                new Position(EXPENSE_COO_USER_POSITION, "Expense: COO", ""),
                new Position(SUPPLIER_EDITOR_POSITION, "Supplier Editor", ""),
                new Position(ALLOW_ADD_REFUND_TO_MAID_VISA_CONTRACT_POSITION, "Allow add refund to maid visa contract", ""),
                new Position(MANAGE_ALL_CASHIER_TODOS_POSITION, "View and manage all Cahier to-dos", ""),
                new Position(STATEMENT_FILE_OVERSEES_SALARY_POSITION, "Statement File Oversees Salary", "")
        );
    }

    @Override
    public List<NotificationType> getCustomNotificationTypes() {
        return Arrays.asList(
                new NotificationType(NEW_CONTRACT_PAYMENT_NOTIFICATION_TYPE,
                        "New Contract Payment",
                        "A new contract payment pending for confirmation"),
                new NotificationType(DD_DATA_ENTRY_CLERK_NOTIFICATION_TYPE,
                        "New DD Data Entry",
                        "A new DD Data is pending for confirmation")//,
//                new NotificationType(BOUNCED_PAYMENT_CLIENT_NOTIFICATION_TYPE,
//                        "Bounced Payment Client Notification",
//                        "CC APP - Bounced Payment Client Notification")
        );
    }

    @Override
    public List<String> getWorkflowInstances() {
        return Arrays.asList("directDebitCancelationToDoFlow",
                "directDebitRejectionToDoFlow",
                "clientRefundFlow",
                "expenseRequestFlow",
                "bucketReplenishmentFlow",
                ExpensePaymentFlow.flowName,
                PurchasingFlow.flowName,
                MaintenanceRequestFlow.flowName);
    }

    @Override
    public String getVersion() {
        return "9.9.0";
    }

    @Override
    public ModuleType getModuleType() {
        return ModuleType.BusinessLogic;
    }
}