package com.magnamedia.helper;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created at Jun 19, 2018
 */
@Component
public class PicklistHelper {

    public static PicklistItem getItem(
            String picklistCode, String itemCodeOrName)
            throws IllegalArgumentException {

        if (picklistCode == null || picklistCode.equals("")) {
            throw new IllegalArgumentException("list code is empty");
        }
        if (itemCodeOrName == null || itemCodeOrName.equals("")) {
            throw new IllegalArgumentException("item code is empty");
        }
        Picklist list = Setup.getRepository(PicklistRepository.class).findByCode(picklistCode);

        if (list == null) {
            throw new IllegalArgumentException("list cannot be find");

        }
        String itemCode = PicklistItem.getCode(itemCodeOrName);
        List<PicklistItem> items
                = Setup.getRepository(PicklistItemRepository.class)
                .findAllByListAndCode(
                        list,
                        itemCode
                );

        if (items.isEmpty()) {

            PicklistItem item = Setup.getRepository(PicklistItemRepository.class)
                    .findByListAndName(
                            list,
                            itemCodeOrName
                    );

            if (item == null)
                throw new IllegalArgumentException(String.format("item with '%s' cannot be find", itemCode));

            items = Collections.singletonList(item);
        }
        return items.get(0);
    }

    //Jirra ACC-1642
    public static PicklistItem getItemNoException(
            String picklistCode, String itemCodeOrName) {

        if (picklistCode == null || picklistCode.equals(""))
            return null;
        if (itemCodeOrName == null || itemCodeOrName.equals(""))
            return null;
        Picklist list = Setup.getRepository(PicklistRepository.class).findByCode(picklistCode);

        if (list == null)
            return null;

        String itemCode = PicklistItem.getCode(itemCodeOrName);
        PicklistItem item
                = Setup.getRepository(PicklistItemRepository.class)
                .findByListAndCodeIgnoreCase(
                        list,
                        itemCode
                );

        return item;
    }
}
