package com.magnamedia.helper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.api.client.util.ArrayMap;
import com.google.api.client.util.Strings;
import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.controller.DDMessagingToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.Ocr;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.Storage;
import com.magnamedia.core.helper.TextService;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.module.type.DirectDebitRejectCategory;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.stereotype.Component;

import javax.net.ssl.HttpsURLConnection;
import java.io.*;
import java.math.BigInteger;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.magnamedia.controller.ContractPaymentTermController.OCR_EID_NATIONALITY_PATTERN;
import static com.magnamedia.controller.ContractPaymentTermController.OCR_EID_SEX_PATTERN;
import static org.springframework.http.HttpHeaders.USER_AGENT;

/**
 * <AUTHOR> Haj Hussein <<EMAIL>>
 * Created At 4/16/2022
 **/

@Component
public class ContractPaymentTermHelper {
    private static final Logger logger = Logger.getLogger(ContractPaymentTermHelper.class.getName());

    @Autowired
    ContractPaymentTermRepository contractPaymentTermRep;
    @Autowired
    DDMsgLogRepository ddMsgLogRepository;
    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    private Ocr ocr;
    @Autowired
    private TextService textService;
    @Autowired
    private PushNotificationHelper pushNotificationHelper;
    @Autowired
    private DDMsgConfigRepository directDebitMessagesConfigRepository;
    @Autowired
    private Utils utils;
    @Autowired
    private ProjectionFactory projectionFactory;
    @Autowired
    private PicklistItemRepository picklistItemRepository;
    @Autowired
    private PicklistRepository picklistRepository;
    @Autowired
    private ClientDocumentRepository clientDocumentRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private PaymentRepository paymentRepository;

    public Map getActiveContractPaymentTermByContract(Contract contract) {
        List<Map> terms = contractPaymentTermRep.findActiveTermsByContract(contract);
        if (terms.isEmpty())
            return null;
        if (terms.size() > 1)
            throw new RuntimeException("There is more than one active Contract Payment Term");
        return terms.get(0);
    }

    public void sendClientSignError(
            Contract contract) {

        HashMap<String, String> parameters = new HashMap<>();
        parameters.put("client_id", contract.getClient().getId().toString());
        parameters.put("date", DateUtil.formatDateDashed(DateTime.now().toDate()));

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToOfficeStaff("client_sign_error_email",
                        parameters, Setup.getCoreParameter(CoreParameter.OPERATION_ALERT_RECEIVERS),
                        "Client Sign Error");

        throw new RuntimeException("sorry, something went wrong, please refresh the page and try again");
    }

    public void changeRejectedFilesFlag(
            ContractPaymentTerm contractPaymentTerm,
            InputStream eidPhoto, InputStream ibanPhoto, InputStream accountNamePhoto) {

        if (contractPaymentTerm.getIsAccountHolderRejected()
                || contractPaymentTerm.getIsIBANRejected()
                || contractPaymentTerm.getIsEidRejected()) {

            SelectQuery<DDMsgLog> query = new SelectQuery<>(DDMsgLog.class);
            query.filterBy("active", "=", true);
            query.filterBy("contractPaymentTerm", "=", contractPaymentTerm);
            List<DDMsgLog> ddMsgLogs = query.execute();

            for (DDMsgLog ddMsgLog : ddMsgLogs) {
                ddMsgLog.setActive(false);
                ddMsgLogRepository.save(ddMsgLogs);
            }
        }

        if (eidPhoto != null) {
            contractPaymentTerm.setIsEidRejected(false);
            contractPaymentTerm.setEidRejectionReason(null);
        }
        if (ibanPhoto != null) {
            contractPaymentTerm.setIsIBANRejected(false);
            contractPaymentTerm.setIbanRejectionReason(null);
        }
        if (accountNamePhoto != null) {
            contractPaymentTerm.setIsAccountHolderRejected(false);
            contractPaymentTerm.setAccountNameRejectionReason(null);
        }

        contractPaymentTermRep.save(contractPaymentTerm);
    }

    public ContractPaymentTerm extractBankInfoByOCR(
            ContractPaymentTerm cpt,
            Attachment eidPhoto, Attachment ibanPhoto, Attachment accountNamePhoto,
            Boolean eidPhotoChanged, Boolean ibanPhotoChanged, Boolean accountNamePhotoChanged,
            String eid, String iban, String account) throws Exception {

        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        cpt.setEid(extractBankInfoEid(cpt, eid, eidPhotoChanged, eidPhoto));

        // ACC-2820
        if (((accountNamePhotoChanged != null && accountNamePhotoChanged) ||
                cpt.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME) == null) &&
                        (!Strings.isNullOrEmpty(account) || accountNamePhoto != null)) {

            logger.info("accountNamePhotoChanged");
            if (accountNamePhoto != null) {
                logger.info("account name photo is not null");
                Attachment attachment = Storage.storeTemporary("account_name." + accountNamePhoto.getExtension().toLowerCase(),
                        utils.getInputStreamFromAttachmentOrMultiPartFile(accountNamePhoto),
                        ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME, true);
                cpt.addAttachment(attachment);

                if (!accountNamePhoto.getExtension().equalsIgnoreCase("pdf")) {
                    String result = this.getOcrPersonName(attachment);
                    cpt.setAccountName(result != null && !result.isEmpty() ? result.replace("\\n", "") : null);
                }
            }// ACC-1622
            else {
                logger.info("account name photo is null");
                cpt.setAccountName(account);
                cpt.setBankInfoTextBased(true);
            }
        } else if (Contract.ContractSource.CHAT_GPT.equals(cpt.getContract().getSource()) && // ACC-6534
                    Setup.getApplicationContext()
                            .getBean(DirectDebitService.class)
                            .hasRejectionForAccountName(cpt.getContract().getClient())) {
            String accountName = getNameFromEidPhoto(eidPhoto);
            logger.info("ChatGPT contract accountName: " + accountName);
            if (accountName != null) {
                cpt.setAccountName(accountName);
            }
        }

        // ACC-2820
        if (((ibanPhotoChanged != null && ibanPhotoChanged) ||
                cpt.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN) == null) &&
                (!Strings.isNullOrEmpty(iban) || ibanPhoto != null)) {

            logger.info("ibanPhotoChanged");
            if (ibanPhoto != null) {
                logger.info("iban photo is not null");
                Attachment attachment = Storage.storeTemporary("iban."  + ibanPhoto.getExtension().toLowerCase(),
                        utils.getInputStreamFromAttachmentOrMultiPartFile(ibanPhoto),
                        ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN, true);
                cpt.addAttachment(attachment);

                if (!ibanPhoto.getExtension().equalsIgnoreCase("pdf")) {
                    String ibanText = this.getOcrText(attachment);
                    String result = null;
                    if (ibanText != null && !ibanText.isEmpty())
                        result = getMatcherByPattern(ibanText, ContractPaymentTermController.OCR_IBAN_PATTERN);

                    if (result != null && !result.isEmpty()) result = result.trim().replace("\\n", "");

                    cpt.setIbanNumber(result != null && !result.isEmpty() && !result.equals("AE") ? result : null);

                    if (!isBankInfoConfirmedBefore(cpt)) {
                        logger.info("Bank Info is not confirmed before");
                        cpt.setBank(null);
                        cpt.setBankName(null);
                    } else {
                        logger.info("Bank Info is confirmed before");
                        extractBankAndBankName(cpt, result);
                    }
                }

            }// ACC-1622
            else {
                logger.info("iban photo is null");
                cpt.setIbanNumber(iban);

                if (!isBankInfoConfirmedBefore(cpt)) {
                    logger.info("Bank Info is not confirmed before");
                    cpt.setBank(null);
                    cpt.setBankName(null);
                } else {
                    logger.info("Bank Info is confirmed before");
                    extractBankAndBankName(cpt, cpt.getIbanNumber());
                }
            }
        } else {
            logger.info("ibanPhoto Didn't Change");
            extractBankAndBankName(cpt, cpt.getIbanNumber());
        }

        return cpt;
    }

    public String extractBankInfoEid(ContractPaymentTerm cpt, String eid, Boolean eidPhotoChanged, Attachment eidPhoto) throws IOException {

        // ACC-2820
        if (((eidPhotoChanged != null && eidPhotoChanged) ||
                cpt.getAttachment(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID) == null) &&
                (!Strings.isNullOrEmpty(eid) || eidPhoto != null)) {

            logger.info("eidPhotoChanged");
            if (eidPhoto != null) {
                logger.info("eid photo is not null");
                Attachment attachment = Storage.storeTemporary("eid." + eidPhoto.getExtension().toLowerCase(),
                        utils.getInputStreamFromAttachmentOrMultiPartFile(eidPhoto),
                        ContractPaymentTermController.FILE_TAG_BANK_INFO_EID, true);
                cpt.addAttachment(attachment);

                if (!eidPhoto.getExtension().equalsIgnoreCase("pdf")) {
                    String eidText = getOcrText(attachment);
                    String result = null;
                    if (eidText != null && !eidText.isEmpty())
                        result = getMatcherByPattern(eidText, ContractPaymentTermController.OCR_EID_PATTERN);

                    if (result != null && !result.isEmpty()) result = result.trim();

                    // return new Eid
                    return result != null && !result.isEmpty() ? result : null;
                }
            }// ACC-1622
            else {
                logger.info("eid photo is null");
                // return old Eid
                return eid;
            }
        }
        return cpt.getEid();
    }

    public ContractPaymentTerm saveAttachmentsPendingOcr(
            ContractPaymentTerm contractPaymentTerm,
            Attachment eidPhoto,
            Attachment ibanPhoto,
            Attachment accountNamePhoto) throws IOException {

        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        contractPaymentTerm.getAttachments().stream()
            .filter(att -> Arrays.asList(
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME,
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN).contains(att.getTag()))
            .forEach(att -> attachementRepository.delete(att));

        if (eidPhoto != null)
            contractPaymentTerm.addAttachment(Storage.storeTemporary(
                    "eid." + eidPhoto.getExtension().toLowerCase(),
                    utils.getInputStreamFromAttachmentOrMultiPartFile(eidPhoto),
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR, false));

        if (accountNamePhoto != null)
            contractPaymentTerm.addAttachment(Storage.storeTemporary(
                    "account_name." + accountNamePhoto.getExtension().toLowerCase(),
                    utils.getInputStreamFromAttachmentOrMultiPartFile(accountNamePhoto),
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR, false));

        if (ibanPhoto != null)
            contractPaymentTerm.addAttachment(Storage.storeTemporary(
                    "iban." + ibanPhoto.getExtension().toLowerCase(),
                    utils.getInputStreamFromAttachmentOrMultiPartFile(ibanPhoto),
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_PENDING_OCR, false));

        return contractPaymentTerm;
    }

    public void extractBankInfoWithoutOCR(
            ContractPaymentTerm cpt,
            Attachment eidPhoto, Attachment ibanPhoto, Attachment accountNamePhoto,
            String eid, String iban, String account) throws Exception {

        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        String accountName = null;
        if (eidPhoto != null) {
            logger.info("eid photo is not null");
            Attachment attachment = Storage.storeTemporary("eid." + eidPhoto.getExtension(),
                    utils.getInputStreamFromAttachmentOrMultiPartFile(eidPhoto),
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_EID, true);
            cpt.addAttachment(attachment);

            // ACC-6534
            if (Contract.ContractSource.CHAT_GPT.equals(cpt.getContract().getSource()) &&
                    accountNamePhoto == null && account == null &&
                    Setup.getApplicationContext()
                            .getBean(DirectDebitService.class)
                            .hasRejectionForAccountName(cpt.getContract().getClient())) {

                accountName = getNameFromEidPhoto(eidPhoto);
                logger.info("account name: " + accountName);
            }
        }
        cpt.setEid(eid != null && !eid.isEmpty() ? eid : null);

        if (accountNamePhoto != null) {
            logger.info("account name photo is not null");
            Attachment attachment = Storage.storeTemporary("account_name." + accountNamePhoto.getExtension(),
                    utils.getInputStreamFromAttachmentOrMultiPartFile(accountNamePhoto),
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME, true);
            cpt.addAttachment(attachment);
        }
        cpt.setAccountName(account != null && !account.isEmpty() ? account : accountName);
        cpt.setBankInfoTextBased(true);

        if (ibanPhoto != null) {
            logger.info("iban photo is not null");
            Attachment attachment = Storage.storeTemporary("iban." + ibanPhoto.getExtension(),
                    utils.getInputStreamFromAttachmentOrMultiPartFile(ibanPhoto),
                    ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN, true);
            cpt.addAttachment(attachment);
        }
        cpt.setIbanNumber(iban != null && !iban.isEmpty() ? iban : null);

        if (!isBankInfoConfirmedBefore(cpt)) {
            logger.info("Bank Info is not confirmed before");
            cpt.setBank(null);
            cpt.setBankName(null);
        } else {
            logger.info("Bank Info is confirmed before");
            extractBankAndBankName(cpt, cpt.getIbanNumber());
        }
    }

    private void extractBankAndBankName(
            ContractPaymentTerm contractPaymentTerm,
            String ibanNumber) throws Exception {

        if (contractPaymentTerm != null && ibanNumber != null && !ibanNumber.isEmpty()) {
            Map ibanInfo = checkIBAN(ibanNumber);
            logger.info("ibanBankInfo: is null" + (ibanInfo == null));
            if (ibanInfo != null) {
                try {
                    logger.info("ibanBankInfo: " + objectMapper.writeValueAsString(ibanInfo));
                } catch (Exception e) {
                }
                contractPaymentTerm.setBank((PicklistItem) ibanInfo.get("picklistItemInfo"));
                contractPaymentTerm.setBankName(((HashMap) ibanInfo.get("bank_data")).get("bank").toString());
            }
        }
    }

    public String getOcrText(Attachment attachment) {
        String ocrText = "";
        try {
            ocrText = ocr.getText(attachment);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "exception in ocr process attachment id: " + attachment.getId());
        }
        return ocrText;
    }

    // ACC-1521
    public String getOcrText(InputStream is) {
        String ocrText = "";
        try {
            ocrText = ocr.getText(is);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "exception in ocr process input stream func");
        }
        return ocrText;
    }

    // ACC-1521
    public String getOcrPersonName(Attachment attachment) {
        String ocrText = "";
        try {
            ocrText = textService.getPersonName(attachment);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "exception in ocr processing person name attachment id: " + attachment.getId());
        }
        return ocrText;
    }

    public String getMatcherByPattern(String target, String patternStr) {
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(target);
        if (matcher.find()) return matcher.group();
        return null;
    }


    public Boolean isBankInfoConfirmedBefore(ContractPaymentTerm cpt) {
        return Setup.getApplicationContext().getBean(DirectDebitService.class)
                .isBankInfoConfirmedBefore(cpt.getContract().getClient(), cpt.getIbanNumber(), cpt.getEid(), cpt.getAccountName());
    }

    public static Map<String, Object> checkIBAN(String IBANNumber) throws IOException {
        String url = "https://api.iban.com/clients/api/v4/iban/";
        URL obj = new URL(url);
        HttpsURLConnection con = (HttpsURLConnection) obj.openConnection();

        //add reuqest header
        con.setRequestMethod("POST");
        con.setRequestProperty("User-Agent", USER_AGENT);
        con.setRequestProperty("Accept-Language", "en-US,en;q=0.5");

        StringBuilder sb = new StringBuilder();
        sb.append("api_key=")
                .append(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_IBAN_CHECK_API_KEY))
                .append("&format=json").append("&iban=").append(IBANNumber);

        // Send post request
        con.setDoOutput(true);
        DataOutputStream wr = new DataOutputStream(con.getOutputStream());
        wr.writeBytes(sb.toString());
        wr.flush();
        wr.close();

        BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
        String inputLine;
        StringBuffer response = new StringBuffer();
        while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
        }
        in.close();

        JSONObject myResponse = new JSONObject(response.toString());
        Map bankInfo = myResponse.toMap();
        String bankName = (String) ((HashMap) bankInfo.get("bank_data")).get("bank");
        PicklistItem picklistItemInfo = null;
        if ((bankName != null) && (!bankName.isEmpty())) {
            List<PicklistItem> l = Setup.getRepository(PicklistRepository.class).findByCode("BankName")
                    .getItemsWithTag("IBAN_BANK_NAME", bankName);
            // ACC-6342
            if (l.isEmpty()) throw new BusinessException(bankName + " is missing in picklist BankName");
            picklistItemInfo = l.get(0);
        }
        bankInfo.put("picklistItemInfo", picklistItemInfo);
        return bankInfo;
    }

    public boolean shouldApplyDiscount(
            ContractPaymentTerm contractPaymentTerm,
            Double amount,
            Date fromDate,
            DirectDebitType directDebitType,
            PicklistItem oneTimePaymentType) {

        long discountStartDateInMillis = Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getDiscountStartDateInMillis(contractPaymentTerm);

        AccountingEntityProperty oecProp = Setup.getRepository(AccountingEntityPropertyRepository.class)
                .findByOriginAndKeyAndDeletedFalse(contractPaymentTerm.getContract().getId(), Contract.OEC_AMEND_DDS);
        boolean isOecAmend = oecProp != null && DateUtil.formatDateDashed(new Date())
                .equals(DateUtil.formatDateDashed(oecProp.getCreationDate()));

        return !isOecAmend && amount != null && fromDate.getTime() >= discountStartDateInMillis &&
                (directDebitType.equals(DirectDebitType.MONTHLY) || PaymentHelper.isMonthlyPayment(oneTimePaymentType));
    }

    public void closeRelatedNotificationsForDDSinging(
            Contract contract) {

        // CMA-3580
        pushNotificationHelper.stopDisplaying(
                Setup.getApplicationContext().getBean(DisablePushNotificationRepository.class)
                .findActiveNotificationsBySignLink(contract.getClient().getId().toString(), contract.getId()));
    }

    public void closeRelatedDDMessages(Contract contract) {
        DDMessagingToDoController ddMessagingToDoCtrl = Setup.getApplicationContext().getBean(DDMessagingToDoController.class);

        ddMessagingToDoCtrl.closeRelatedToDos(contract.getUuid(), Arrays.asList(
                DDMessagingType.DirectDebitRejected,
                DDMessagingType.ClientPaidCashAndNoSignatureProvided,
                DDMessagingType.IncompleteDDRejectedByDataEntry,
                DDMessagingType.ExpiryPayment));
    }

    public Map projectResultMap(Map map) {
        Map returnedMap = new HashMap();

        if (map == null)
            return returnedMap;

        returnedMap.putAll(map);

        if (map.containsKey("contractPaymentTerm") &&
                (map.get("contractPaymentTerm") instanceof ContractPaymentTerm)) {

            returnedMap.put("contractPaymentTerm", projectionFactory.
                    createProjection(ContractPaymentTermSalesProjection.class, map.get("contractPaymentTerm")));
        }

        if (map.containsKey("newContractPaymentTerm") &&
                (map.get("newContractPaymentTerm") instanceof ContractPaymentTerm)) {

            returnedMap.put("newContractPaymentTerm", projectionFactory.
                    createProjection(ContractPaymentTermSalesProjection.class, map.get("newContractPaymentTerm")));
        }

        if (map.containsKey("currentContractPaymentTerm") &&
                (map.get("currentContractPaymentTerm") instanceof ContractPaymentTerm)) {

            returnedMap.put("currentContractPaymentTerm", projectionFactory.
                    createProjection(ContractPaymentTermSalesProjection.class, map.get("currentContractPaymentTerm")));
        }

        if (map.containsKey("contract") && (map.get("contract") instanceof Contract)) {
            returnedMap.put("contract", projectionFactory.
                    createProjection(ContractSalesProjection.class, map.get("contract")));
        }

        if (map.containsKey("client") && (map.get("client") instanceof Client)) {
            returnedMap.put("client", projectionFactory.
                    createProjection(ClientSalesProjection.class, map.get("client")));
        }

        if (map.containsKey("payments") && (map.get("payments") instanceof List)) {
            if (((List) map.get("payments")).stream().anyMatch(payment -> payment instanceof ContractPayment)) {
                returnedMap.put("payments", ((List) map.get("payments")).stream().filter(payment -> payment instanceof ContractPayment)
                        .map(payment ->
                                projectionFactory.createProjection(ContractPaymentSalesProjection.class, payment)).collect(Collectors.toList()));
            } else {
                returnedMap.put("payments", map.get("payments"));
            }
        }

        if (map.containsKey("oneTimePayments") && (map.get("oneTimePayments") instanceof List)) {
            if (((List) map.get("oneTimePayments")).stream().anyMatch(payment -> payment instanceof ContractPayment)) {
                returnedMap.put("oneTimePayments", ((List) map.get("oneTimePayments")).stream().filter(payment -> payment instanceof ContractPayment)
                        .map(payment ->
                                projectionFactory.createProjection(ContractPaymentSalesProjection.class, payment)).collect(Collectors.toList()));
            } else {
                returnedMap.put("oneTimePayments", map.get("oneTimePayments"));
            }
        }

        if (map.containsKey("directDebits") && (map.get("directDebits") instanceof List)) {
            if (((List) map.get("directDebits")).size() > 0 && (((List) map.get("directDebits")).get(0) instanceof DirectDebit)) {
                returnedMap.put("directDebits", ((List) map.get("directDebits")).stream()
                        .map(dd -> projectionFactory.createProjection(DirectDebitSalesProjection.class, dd))
                        .collect(Collectors.toList()));
            }
        }

        if (map.containsKey("newDirectDebits") && (map.get("newDirectDebits") instanceof List)) {
            if (((List) map.get("newDirectDebits")).size() > 0 && (((List) map.get("newDirectDebits")).get(0) instanceof DirectDebit)) {
                returnedMap.put("newDirectDebits", ((List) map.get("newDirectDebits")).stream()
                        .map(dd -> projectionFactory.createProjection(DirectDebitSalesProjection.class, dd))
                        .collect(Collectors.toList()));
            }
        }

        return returnedMap;
    }

    public PicklistItem getItem(String listCode, String nameOrCode) {
        return picklistItemRepository.findByListAndCodeIgnoreCase(picklistRepository.findByCode(listCode), PicklistItem.getCode(nameOrCode));
    }

    public boolean isAttachmentInfoCompleted(Contract contract) {
        Client client = contract.getClient();
        Housemaid housemaid = contract.getHousemaid();
        return isAttachmentInfoCompleted(client) && isAttachmentInfoCompleted(housemaid);
    }

    public boolean isAttachmentInfoCompleted(Client client) {
        return client != null && client.getName() != null && !client.getName().isEmpty() && client.getNormalizedMobileNumber() != null &&
                !client.getNormalizedMobileNumber().isEmpty();
    }

    public boolean isAttachmentInfoCompleted(Housemaid housemaid) {
        if (housemaid == null ||
                housemaid.getNationality() == null ||
                housemaid.getNationality().getId() == null)
            return false;

        PicklistItem nationality = picklistItemRepository.findOne(housemaid.getNationality().getId());
        return housemaid != null && housemaid.getName() != null
                && !housemaid.getName().toLowerCase().contains("unknown") &&
                !nationality.getCode().toLowerCase().contains("unknown");
    }

    public boolean hasClientFrontSideDoc(Client client) {
        return clientDocumentRepository.existsByClientAndType(client, Setup.getItem("ClientDocumentType", "EMIRATES_ID_FRONT_SIDE"));
    }

    public ContractPayment createProratedPayment(ContractPaymentTerm cpt, DateTime contractStartDate) {
        PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");
        AbstractPaymentTypeConfig contractPaymentType = cpt.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        ContractPayment payment = new ContractPayment();
        payment.setContractPaymentTerm(cpt);
        payment.setPaymentType(monthlyPaymentType);
        payment.setSubType(contractPaymentType.getSubType());
        // ACC-4905
        Map<String, Object> map = new ArrayMap<>();
        map.put("dailyRateAmount", cpt.getDailyRateAmount());
        map.put("monthlyPaymentAmount", cpt.getMonthlyPayment());
        map.put("proRatedDate", contractStartDate.toLocalDate());
        map.put("isOneMonthAgreement", cpt.getContract().isOneMonthAgreement());
        map.put("firstMonthPayment", cpt.getFirstMonthPayment());

        return createProratedPayment(cpt, contractStartDate,
                Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getProRatedAmount(map));
    }

    public ContractPayment createProratedPayment(ContractPaymentTerm cpt, DateTime contractStartDate, Double amount) {
        PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");
        AbstractPaymentTypeConfig contractPaymentType = cpt.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        ContractPayment payment = new ContractPayment();
        payment.setContractPaymentTerm(cpt);
        payment.setPaymentType(monthlyPaymentType);
        payment.setAmount(amount);
        payment.setDate(contractStartDate.toDate());
        payment.setIsProRated(true);
        payment.setDescription(contractPaymentType.getDescription());
        payment.setAffectsPaidEndDate(contractPaymentType.getAffectsPaidEndDate());

        logger.info("Creating pro-rated payment, Date: " + new LocalDate(payment.getDate().getTime()).toString("yyyy-MM-dd"));

        return payment;
    }

    public List<ContractPayment> createOneMonthAgreementAndProratedPayment(
        ContractPaymentTerm cpt, DateTime contractStartDate) {

        List<ContractPayment> payments = new ArrayList<>();
        Contract contract = cpt.getContract();
        DateTime proRatedDate = contractStartDate.plusMonths(1);

        if (!paymentService.hasOneMonthlyPaymentReceived(contract)) {
            payments.add(createMonthlyContractPayment(cpt, contractStartDate.toDate()));
            logger.log(Level.INFO, "Creating one month agreement payment, Date: {0}",
                new LocalDate(contractStartDate.toDate()).toString("yyyy-MM-dd"));

        } else  {
            proRatedDate = paymentService.getLastReceivedMonthlyPaymentDate(
                contract).plusMonths(1).withDayOfMonth(contractStartDate.getDayOfMonth());
        }

        if (!Setup.getRepository(PaymentRepository.class).existsMonthlyPaymentReceived(cpt.getContract().getId(), true)) {
            payments.add(createProratedPayment(cpt, proRatedDate));
        }
        return payments;
    }

    public String getPaymentDescriptionForSigningScreen(ContractPayment cp) {
        Date today = new DateTime().withTimeAtStartOfDay().toDate();
        Date cpDate = new DateTime(cp.getDate()).withTimeAtStartOfDay().toDate();

        if (cpDate.equals(today)) return "Today";

        return DateUtil.formatNotDashedFullDate(cp.getDate());
    }

    public ContractPayment createProratedPlusMonthPayment(ContractPaymentTerm cpt, DateTime contractStartDate) {
        PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");
        AbstractPaymentTypeConfig contractPaymentType = cpt.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        ContractPayment payment = new ContractPayment();
        payment.setContractPaymentTerm(cpt);
        payment.setPaymentType(monthlyPaymentType);
        payment.setSubType(contractPaymentType.getSubType());
        payment.setAmount(cpt.getFirstMonthPayment() + cpt.getMonthlyPayment());
        payment.setDate(contractStartDate.toDate());
        payment.setIsProRated(true);
        payment.setProRatedPlusMonth(true);
        payment.setDescription(contractPaymentType.getDescription());
        payment.setAffectsPaidEndDate(contractPaymentType.getAffectsPaidEndDate());

        logger.info("Creating pro-rated plus month payment, Date: " +
                new LocalDate(payment.getDate().getTime()).toString("yyyy-MM-dd"));

        return payment;
    }

    public ContractPayment createMonthlyContractPayment(ContractPaymentTerm cpt, Date date) {
        PicklistItem monthlyPaymentType = getItem("TypeOfPayment", "monthly_payment");
        AbstractPaymentTypeConfig contractPaymentType = cpt.getPaymentTypeConfig(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        ContractPayment cp = new ContractPayment();
        cp.setContractPaymentTerm(cpt);
        cp.setPaymentType(monthlyPaymentType);
        cp.setSubType(contractPaymentType.getSubType());
        cp.setAmount(cpt.getMonthlyPayment());
        cp.setDate(date);
        cp.setDescription(contractPaymentType.getDescription());
        cp.setAffectsPaidEndDate(contractPaymentType.getAffectsPaidEndDate());

        logger.info("Creating full payment, Date: " +
                new LocalDate(cp.getDate().getTime()).toString("yyyy-MM-dd"));

        return cp;
    }

    // ACC-8796
    public void createPaymentsForPreCollectedSalaryContract(
            ContractPaymentTerm cpt, DateTime d, List<ContractPayment> payments) {

        // add payment for pre-Collected salary DDA (month 1).
        ContractPayment cp = createMonthlyContractPayment(cpt, d.plusMonths(1).withDayOfMonth(1).toDate());
        payments.add(cp);

        // add payment for last DDA (month 2).
        payments.add(createMonthlyContractPayment(cpt, d.plusMonths(2).withDayOfMonth(1).toDate()));
    }

    public void handleProratedPayment(
            Contract contract, ContractPaymentTerm contractPaymentTerm, DateTime contractStartDate,
            List<ContractPayment> payments, AbstractPaymentTypeConfig monthlyContractPaymentType) {

        if (contract.getProRatedPlusMonth() && monthlyContractPaymentType.getStartsOn() <= 1) {
            payments.add(createProratedPlusMonthPayment(
                    contractPaymentTerm, contractStartDate));
        } else if (monthlyContractPaymentType.getStartsOn().equals(0)) {
            payments.add(createMonthlyContractPayment(
                    contractPaymentTerm, contractStartDate.toDate()));
        }

        if (ContractService.isPreCollectedSalary(contract)) {
            createPaymentsForPreCollectedSalaryContract(
                    contractPaymentTerm, contractStartDate, payments);
        }
    }

    public void handleFirstFullPayment(
            Contract contract, ContractPaymentTerm contractPaymentTerm, DateTime contractStartDate,
            List<ContractPayment> payments, AbstractPaymentTypeConfig monthlyContractPaymentType) {

        if (monthlyContractPaymentType.getStartsOn() > 1) return;

        if (ContractService.isPreCollectedSalary(contract)) {
            createPaymentsForPreCollectedSalaryContract(
                    contractPaymentTerm, contractStartDate, payments);
        } else {
            Date paymentDate = contractStartDate.plusMonths(1)
                    .dayOfMonth()
                    .withMinimumValue()
                    .toDate();

            payments.add(createMonthlyContractPayment(
                    contractPaymentTerm, paymentDate));
        }
    }

    public String getPaymentDescriptionForCCAPP(ContractPayment cp) {
        Date today = new DateTime().withTimeAtStartOfDay().toDate();
        if (new DateTime(cp.getDate()).withTimeAtStartOfDay().toDate().equals(today)) {
            return "Today";
        }

        return DateUtil.formatCCAPPDate(cp.getDate());
    }

    public String getNameFromEidPhoto(Object photo) throws IOException {

        if (photo == null)
            return null;
        String eidText = getOcrText(utils.getInputStreamFromAttachmentOrMultiPartFile(photo));

        if (eidText == null || eidText.isEmpty())
            return null;
        String[] parts = eidText.split(ContractPaymentTermController.OCR_EID_NAME_PATTERN);
        if (parts.length != 2)
            return null;
        String result = parts[1].substring(0, parts[1].indexOf("\n")).trim();
        return result;
    }

    //ACC-4657
    public Map<String, Object> extractBankInfoFromPhotos(Map<String, Object> map)  {
        Map<String, Object> result = new HashMap<>();
        List<Attachment> pendingOcrAttachments = (List<Attachment>) map.get("pendingOcrAttachments");
        boolean enableAccountName = (boolean) map.get("enableAccountName");
        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        for (Attachment attachment : pendingOcrAttachments) {
            try {
                logger.log(Level.INFO, "attachment uuid : {0}", attachment.getUuid());
                String text = getOcrText(utils.getInputStreamFromAttachmentOrMultiPartFile(attachment));
                if (text == null)
                    return result;
                if (result.get("eid") == null) {
                    String eid = getMatcherByPattern(text, ContractPaymentTermController.OCR_EID_PATTERN);
                    if (eid != null && !eid.isEmpty()) {
                        logger.log(Level.INFO, "eid matched");
                        result.put("eid", eid);
                        if (!enableAccountName) {
                            String accountName = getNameFromEidPhoto(attachment);
                            if (accountName != null) {
                                result.put("accountName", accountName);
                            }
                        }
                        attachment.setTag(ContractPaymentTermController.FILE_TAG_BANK_INFO_EID);
                        attachment.setUniqueTag(true);
                        attachment.setName("eid.png");
                        attachementRepository.save(attachment);
                        continue;
                    }
                }

                if (result.get("iban") == null) {
                    String iban = getMatcherByPattern(text, ContractPaymentTermController.OCR_IBAN_PATTERN);
                    if (iban != null && !iban.isEmpty()) {
                        logger.log(Level.INFO, "iban matched");
                        iban = iban.trim().replace("\\n", "");
                        if (iban.equals("AE")) continue;
                        result.put("iban", iban);
                        attachment.setTag(ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN);
                        attachment.setUniqueTag(true);
                        attachment.setName("iban.png");
                        attachementRepository.save(attachment);
                        continue;
                    }
                }

                if (result.get("accountName") == null) {
                    String accountHolderName = getOcrPersonName(attachment);
                    if (accountHolderName != null && !accountHolderName.isEmpty()) {
                        logger.log(Level.INFO, "accountName matched");
                        result.put("accountName", accountHolderName);
                        attachment.setTag(ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);
                        attachment.setUniqueTag(true);
                        attachment.setName("account_name.png");
                        attachementRepository.save(attachment);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    //ACC-4657
    public String completeBankInfoGetCurrentFlow(Map<String, Object> map) {
        ContractPaymentTerm cpt = (ContractPaymentTerm) map.get("cpt");
        String currentFlow = "&currentFlow=new_dds_signing";
        DDMessaging ddMessaging = (DDMessaging) map.get("ddMessaging");
        DirectDebitRejectCategory directDebitRejectCategory = (DirectDebitRejectCategory) map.get("directDebitRejectCategory");
        DDMessagingType ddMessagingType = ddMessaging != null ?
                ddMessaging.getEvent() : (DDMessagingType) map.getOrDefault("ddMessagingType", null);
        boolean ignoreDataEntryRejection = (boolean) map.getOrDefault("ignoreDataEntryRejection", false);

        if (!ignoreDataEntryRejection && (cpt.getIsEidRejected() ||
                cpt.getIsIBANRejected() ||
                cpt.getIsAccountHolderRejected())) {

            currentFlow = "&currentFlow=document_rejection_erp_side";
            currentFlow += "&isEidRejected=" + cpt.getIsEidRejected();
            currentFlow += "&isIbanRejected=" + cpt.getIsIBANRejected();
            currentFlow += "&isAccountNameRejected=" + cpt.getIsAccountHolderRejected();

            if (ddMessagingType != null && ddMessagingType.equals(DDMessagingType.IncompleteDDRejectedByDataEntry)) {
                logger.log(Level.INFO, currentFlow);
                return  currentFlow;
            }
        }

        if (directDebitRejectCategory == null) {
            List<DirectDebitRejectionToDo> todos = Setup.getRepository(DirectDebitRejectionToDoRepository.class)
                    .findActiveByContractPaymentTerm(cpt);
            if (!todos.isEmpty() && todos.get(0).getLastRejectCategory() != null) {
                directDebitRejectCategory = todos.get(0).getLastRejectCategory();
            }
        }

        if (directDebitRejectCategory != null) {
            switch(directDebitRejectCategory) {
                case Account:
                    currentFlow = "&currentFlow=account_name_rejection_bank_side";
                    break;
                case EID:
                    currentFlow = "&currentFlow=eid_rejection_bank_side";
                    break;
                case Invalid_Account:
                    currentFlow = "&currentFlow=iban_rejection_bank_side";
                    break;
                case Signature:
                    currentFlow = "&currentFlow=signature_rejection";
                    break;
            }
        }

        if (ddMessaging != null &&
                ddMessaging.getEvent().equals(DDMessagingType.DirectDebitRejected) &&
                ddMessaging.getRejectCategory() != null) {

            switch (ddMessaging.getRejectCategory()) {
                case Account:
                    currentFlow = "&currentFlow=account_name_rejection_bank_side";
                    break;
                case EID:
                    currentFlow = "&currentFlow=eid_rejection_bank_side";
                    break;
                case Invalid_Account:
                    currentFlow = "&currentFlow=iban_rejection_bank_side";
                    break;
                case Signature:
                    currentFlow = "&currentFlow=signature_rejection";
                    break;
            }
        }

        if (currentFlow.equals("&currentFlow=new_dds_signing")) {
            boolean enableAccountName = Setup.getApplicationContext().getBean(DirectDebitService.class)
                    .hasRejectionForAccountName(cpt.getContract().getClient());
            currentFlow += "&enableAccountName=" + enableAccountName;
        }

        logger.log(Level.INFO, currentFlow);
        return currentFlow;
    }

    public String extractNationalityAndGenderFromEid(Long lastId, Long clientId, Long attachmentId) throws InterruptedException {

        List<Object[]> l;
        if (clientId != null && attachmentId != null) {
            l = new ArrayList<>();
            l.add(new Object[]{BigInteger.valueOf(clientId), BigInteger.valueOf(attachmentId)});
        } else l = contractPaymentTermRep.findClientForAcc6214(lastId);

        PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
        PicklistItemRepository picklistItemRepository = Setup.getRepository(PicklistItemRepository.class);
        ClientRepository clientRepository = Setup.getRepository(ClientRepository.class);
        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);

        Picklist root = picklistRepository.findByCode("countries");
        Picklist nationalities = picklistRepository.findByCode("nationalities");
        Picklist personTitle = picklistRepository.findByCode("PersonTitle");

        List<PicklistItem> countries = picklistItemRepository.findByListOrderByNameAsc(root);
        PicklistItem mr = picklistItemRepository.findByListAndCodeIgnoreCase(personTitle, "mr");
        PicklistItem mis = picklistItemRepository.findByListAndCodeIgnoreCase(personTitle, "mis");
        AtomicInteger i = new AtomicInteger(1);
        StringBuilder result = new StringBuilder();
        while (!l.isEmpty()) {
            for (Object[] objects : l) {
                InputStream stream = null;
                try {
                    Client c = clientRepository.findOne(((BigInteger) objects[0]).longValue());
                    result.append("#").append(i.get()).append(" Client id: ").append(c.getId());
                    Attachment a = attachementRepository.findOne(((BigInteger) objects[1]).longValue());
                    result.append("; Attachment id: ").append(a.getId());
                    result.append("; Attachment uuid: ").append(a.getUuid());
                    logger.info("client id: " + c.getId() +
                            " ; attachment id: " + a.getId() +
                            " ; attachment uuid: " + a.getUuid());
                    stream = utils.getInputStreamFromAttachmentOrMultiPartFile(a);

                    String text = getOcrText(stream);
                    if (text == null || text.isEmpty()) {
                        result.append("; OCR failed").append("<br/><br/>");
                        continue;
                    }

                    PicklistItem nationality = null, title = null;

                    if (c.getNationality() == null) {
                        Map<String, Object> m = extractNationalityFromEid(text, countries, nationalities);
                        result.append(m.get("result"));
                        if (m.containsKey("nationality"))
                            nationality = (PicklistItem) m.get("nationality");
                    }

                    if (c.getTitle() == null) {
                        Map<String, Object> m = extractGenderFromEid(text, mr, mis);
                        result.append(m.get("result"));
                        if (m.containsKey("title"))
                            title = (PicklistItem) m.get("title");
                    }

                    if (nationality != null || title != null) {
                        if (nationality != null) c.setNationality(nationality);
                        if (title != null) c.setTitle(title);

                        clientRepository.save(c);
                        result.append("; end with save").append("<br/><br/>");
                        continue;
                    }

                    result.append("; end without save").append("<br/><br/>");

                } catch (Exception e) {
                    result.append("; error ").append(e.getMessage()).append("<br/><br/>");
                    e.printStackTrace();
                } finally {
                    StreamsUtil.closeStream(stream);
                    i.getAndIncrement();
                }
            }

            if (clientId != null && attachmentId != null) {
                break;
            }

            lastId = ((BigInteger) l.get(l.size() - 1)[0]).longValue();
            l = contractPaymentTermRep.findClientForAcc6214(lastId);

            Thread.sleep(1000);
        }

        return result.toString();
    }

    public Map<String, Object> extractNationalityFromEid(String text, List<PicklistItem> countries, Picklist nationalities) {
        Map<String, Object> m = new HashMap<>();
        StringBuilder result = new StringBuilder("; Client Nationality is null");
        String[] parts = text.split(OCR_EID_NATIONALITY_PATTERN);
        logger.info("parts: " +  Arrays.stream(parts).collect(Collectors.joining("; ")));

        if (parts.length != 2) {
            result.append("; extract nationality failed");
            m.put("result", result);
            return m;
        }

        String nationalityStr = parts[1].contains("\n") ?
                parts[1].substring(0, parts[1].indexOf("\n")).trim() :
                parts[1].trim();

        logger.info("nationalityStr: " +  nationalityStr);

        result.append("; nationality from Eid: ").append(nationalityStr);
        PicklistItem i = countries.stream()
                .filter(p -> nationalityStr.toLowerCase().contains(p.getName().toLowerCase()))
                .findFirst()
                .orElse(null);

        if (i == null) {
            result.append("; did not match any root nationality on erp ");
        } else {
            List<PicklistItem> n = Setup.getRepository(PicklistItemRepository.class)
                    .findByListAndRootOrderByNameAsc(nationalities, i);
            if (n.isEmpty()) {
                result.append("; did not match any nationality on erp ");
                m.put("result", result);
                return m;
            }
            result.append("; matched with nationality on erp id : ")
                    .append(n.get(0).getId())
                    .append(" name: ")
                    .append(n.get(0).getName());
            m.put("nationality", n.get(0));
        }

        m.put("result", result.toString());
        return m;
    }

    public Map<String, Object> extractGenderFromEid(String text, PicklistItem mr, PicklistItem mis) {
        Map<String, Object> m = new HashMap<>();
        StringBuilder result = new StringBuilder("; Client Title is null");
        String[] parts = text.split(OCR_EID_SEX_PATTERN);
        logger.info("parts: " +  Arrays.stream(parts).collect(Collectors.joining("; ")));

        if (parts.length != 2) {
            result.append("; extract title failed");
            m.put("result", result);
            return m;
        }

        String titleStr = parts[1].contains("\n") ?
                parts[1].substring(0, parts[1].indexOf("\n")).trim() :
                parts[1].trim();

        logger.info("titleStr: " +  titleStr);
        result.append("; title from Eid: ").append(titleStr);

        if (titleStr.isEmpty() || !Arrays.asList("M", "F").contains(titleStr)) {
            result.append("; extract title failed");
        } else {
            switch (titleStr) {
                case "M":
                    m.put("title", mr);
                    break;
                case "F":
                    m.put("title", mis);
                    break;
            }

            if (m.get("title") != null) {
                result.append("; matched with title on erp id : ")
                        .append(((PicklistItem) m.get("title")).getId())
                        .append(" name: ")
                        .append(((PicklistItem) m.get("title")).getName());
            } else {
                result.append("; extract title failed");
            }
        }

        m.put("result", result);
        return m;
    }
}